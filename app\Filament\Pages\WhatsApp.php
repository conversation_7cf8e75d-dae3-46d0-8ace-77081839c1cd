<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Card;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Actions\Action;
use App\Models\Setting;
use App\Services\WhatsAppService;
use App\Services\WhatsAppProcessService;

class WhatsApp extends Page implements HasForms
{
    use InteractsWithForms;

    protected static string $view = 'filament.pages.whats-app';
    protected static ?string $navigationGroup = 'Umum';
    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-oval-left-ellipsis';
    protected static ?string $navigationLabel = 'WhatsApp';

    // Form data
    public ?array $data = [];
    protected ?WhatsAppService $whatsappService = null;
    protected ?WhatsAppProcessService $processService = null;

    // Real-time status
    public $serviceStatus = 'disconnected';
    public $qrCode = null;
    public $isServiceRunning = false;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Pengaturan WhatsApp')
                    ->description('Konfigurasi layanan WhatsApp untuk notifikasi absensi')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                Toggle::make('whatsapp_enabled')
                                    ->label('Aktifkan WhatsApp')
                                    ->helperText('Nyalakan untuk mengaktifkan service WhatsApp dan menampilkan QR Code')
                                    ->live()
                                    ->afterStateUpdated(function ($state) {
                                        $this->handleServiceToggle($state);
                                        $this->saveToggleState($state);
                                    }),
                            ]),
                    ]),

                Section::make('Template Pesan Guru')
                    ->description('Template pesan notifikasi untuk guru')
                    ->schema([
                        Textarea::make('whatsapp_template_masuk')
                            ->label('Template Pesan Absen Masuk')
                            ->rows(4)
                            ->helperText('Gunakan: {nama_guru}, {nama_sekolah}, {tanggal}, {waktu}, {jabatan}, {divisi}')
                            ->placeholder('🌅 Selamat pagi {nama_guru}!\n\nAbsensi masuk Anda telah tercatat pada {waktu}, {tanggal}.\nTerima kasih telah hadir tepat waktu.\n\nSalam,\n{nama_sekolah}'),

                        Textarea::make('whatsapp_template_pulang')
                            ->label('Template Pesan Absen Pulang')
                            ->rows(4)
                            ->helperText('Gunakan: {nama_guru}, {nama_sekolah}, {tanggal}, {waktu}, {durasi}, {jabatan}, {divisi}')
                            ->placeholder('🌆 Selamat sore {nama_guru}!\n\nAbsensi pulang Anda telah tercatat pada {waktu}, {tanggal}.\nDurasi kerja: {durasi}\n\nTerima kasih atas dedikasi Anda!\n\nSalam,\n{nama_sekolah}'),

                        Textarea::make('whatsapp_template_reminder')
                            ->label('Template Pesan Pengingat')
                            ->rows(4)
                            ->helperText('Gunakan: {nama_guru}, {nama_sekolah}, {tanggal}, {waktu}')
                            ->placeholder('⏰ Pengingat Absensi\n\nHalo {nama_guru},\n\nAnda belum melakukan absensi hari ini ({tanggal}).\nMohon segera lakukan absensi.\n\nTerima kasih,\n{nama_sekolah}'),
                    ]),
            ])
            ->statePath('data');
    }

    public function mount(): void
    {
        $this->initializeServices();

        $setting = Setting::getActive();

        // Update initial status first
        $this->updateServiceStatus();

        // Sync database setting with actual service status
        $actualServiceRunning = $this->isServiceRunning;
        $databaseEnabled = $setting->whatsapp_enabled ?? false;

        // If there's a mismatch, update database to match actual service status
        if ($databaseEnabled !== $actualServiceRunning) {
            $this->syncDatabaseWithServiceStatus($actualServiceRunning);
            $setting = Setting::getActive(); // Reload after sync
        }

        $this->form->fill([
            'whatsapp_enabled' => $setting->whatsapp_enabled ?? false,
            'whatsapp_server_url' => $setting->whatsapp_server_url ?? 'http://localhost:3001',
            'whatsapp_template_masuk' => $setting->whatsapp_template_masuk ?? '',
            'whatsapp_template_pulang' => $setting->whatsapp_template_pulang ?? '',
            'whatsapp_template_reminder' => $setting->whatsapp_template_reminder ?? '',
        ]);
    }

    /**
     * Initialize services
     */
    private function initializeServices(): void
    {
        if (!$this->whatsappService) {
            $this->whatsappService = app(WhatsAppService::class);
        }

        if (!$this->processService) {
            $this->processService = app(WhatsAppProcessService::class);
        }
    }

    /**
     * Get WhatsApp service instance
     */
    private function getWhatsAppService(): WhatsAppService
    {
        if (!$this->whatsappService) {
            $this->whatsappService = app(WhatsAppService::class);
        }
        return $this->whatsappService;
    }

    /**
     * Get process service instance
     */
    private function getProcessService(): WhatsAppProcessService
    {
        if (!$this->processService) {
            $this->processService = app(WhatsAppProcessService::class);
        }
        return $this->processService;
    }

    public function save(): void
    {
        try {
            $data = $this->form->getState();

            $setting = Setting::getActive();
            if (!$setting) {
                $setting = new Setting();
            }

            // Update WhatsApp settings
            $setting->whatsapp_enabled = $data['whatsapp_enabled'] ?? false;
            $setting->whatsapp_server_url = $data['whatsapp_server_url'] ?? 'http://localhost:3001';
            $setting->whatsapp_template_masuk = $data['whatsapp_template_masuk'] ?? null;
            $setting->whatsapp_template_pulang = $data['whatsapp_template_pulang'] ?? null;
            $setting->whatsapp_template_reminder = $data['whatsapp_template_reminder'] ?? null;

            $setting->save();

            Notification::make()
                ->title('Pengaturan WhatsApp berhasil disimpan!')
                ->success()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Gagal menyimpan pengaturan WhatsApp')
                ->body('Error: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    /**
     * Save toggle state to database immediately
     */
    public function saveToggleState(bool $enabled): void
    {
        try {
            $setting = Setting::getActive();
            if (!$setting) {
                $setting = new Setting();
            }

            $setting->whatsapp_enabled = $enabled;
            $setting->save();

            \Log::info('WhatsApp toggle state saved', [
                'enabled' => $enabled,
                'timestamp' => now()
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to save WhatsApp toggle state', [
                'enabled' => $enabled,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Sync database setting with actual service status
     */
    public function syncDatabaseWithServiceStatus(bool $serviceRunning): void
    {
        try {
            $setting = Setting::getActive();
            if (!$setting) {
                $setting = new Setting();
            }

            $setting->whatsapp_enabled = $serviceRunning;
            $setting->save();

            \Log::info('Database synced with service status', [
                'service_running' => $serviceRunning,
                'database_updated' => true
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to sync database with service status', [
                'service_running' => $serviceRunning,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle service toggle on/off
     */
    public function handleServiceToggle(bool $enabled): void
    {
        try {
            $processService = $this->getProcessService();

            if ($enabled) {
                // Start WhatsApp service
                $result = $processService->startService();

                if ($result['success']) {
                    Notification::make()
                        ->title('WhatsApp Service Dimulai')
                        ->body('Service WhatsApp berhasil dijalankan. Silakan scan QR code untuk menghubungkan WhatsApp.')
                        ->success()
                        ->send();
                } else {
                    Notification::make()
                        ->title('Gagal Memulai Service')
                        ->body($result['error'] ?? 'Terjadi kesalahan saat memulai service WhatsApp')
                        ->danger()
                        ->send();

                    // Revert toggle if service failed to start
                    $this->saveToggleState(false);
                    return;
                }
            } else {
                // Stop WhatsApp service
                $result = $processService->stopService();

                if ($result['success']) {
                    Notification::make()
                        ->title('WhatsApp Service Dihentikan')
                        ->body('Service WhatsApp berhasil dihentikan.')
                        ->info()
                        ->send();
                } else {
                    Notification::make()
                        ->title('Gagal Menghentikan Service')
                        ->body($result['error'] ?? 'Terjadi kesalahan saat menghentikan service WhatsApp')
                        ->danger()
                        ->send();

                    // Revert toggle if service failed to stop
                    $this->saveToggleState(true);
                    return;
                }
            }

            // Update status after toggle
            $this->updateServiceStatus();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Error Service Toggle')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    /**
     * Update service status and QR code
     */
    public function updateServiceStatus(): void
    {
        try {
            $processService = $this->getProcessService();
            $status = $processService->getStatus();

            $this->isServiceRunning = $status['process_running'] ?? false;
            $this->serviceStatus = $status['connection_status'] ?? 'disconnected';
            $this->qrCode = $status['qr_code'] ?? null;

            // Update form state to reflect actual service status
            $this->updateFormToggleState($this->isServiceRunning);

        } catch (\Exception $e) {
            $this->isServiceRunning = false;
            $this->serviceStatus = 'error';
            $this->qrCode = null;

            // Update form to show service is off
            $this->updateFormToggleState(false);
        }
    }

    /**
     * Update form toggle state without triggering events
     */
    private function updateFormToggleState(bool $enabled): void
    {
        try {
            $currentData = $this->form->getState();
            $currentData['whatsapp_enabled'] = $enabled;
            $this->form->fill($currentData);
        } catch (\Exception $e) {
            \Log::warning('Failed to update form toggle state', [
                'enabled' => $enabled,
                'error' => $e->getMessage()
            ]);
        }
    }

    protected function getFormActions(): array
    {
        return [
            Action::make('save')
                ->label('Simpan Pengaturan')
                ->submit('save')
                ->color('primary'),

            Action::make('refresh_status')
                ->label('Refresh Status')
                ->action('refreshStatus')
                ->color('info'),

            Action::make('restart_service')
                ->label('Restart Service')
                ->action('restartService')
                ->color('warning')
                ->requiresConfirmation(),

            Action::make('view_status')
                ->label('Detail Status')
                ->action('viewStatus')
                ->color('gray'),
        ];
    }

    public function submit(): void
    {
        $this->save();
    }

    public function testConnection(): void
    {
        try {
            $status = $this->whatsappService->getStatus();

            if ($status['connected']) {
                Notification::make()
                    ->title('Koneksi WhatsApp Berhasil!')
                    ->body('Server WhatsApp terhubung dan siap digunakan.')
                    ->success()
                    ->send();
            } else {
                Notification::make()
                    ->title('Koneksi WhatsApp Gagal')
                    ->body($status['error'] ?? 'Server tidak dapat dijangkau')
                    ->warning()
                    ->send();
            }
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error Test Koneksi')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function refreshStatus(): void
    {
        $this->updateServiceStatus();

        Notification::make()
            ->title('Status Diperbarui')
            ->body("Service: " . ($this->isServiceRunning ? 'Running' : 'Stopped') .
                   "\nStatus: " . ucfirst($this->serviceStatus))
            ->info()
            ->send();
    }

    public function restartService(): void
    {
        try {
            $processService = $this->getProcessService();
            $result = $processService->restartService();

            if ($result['success']) {
                Notification::make()
                    ->title('Service Direstart')
                    ->body('WhatsApp service berhasil direstart. Silakan scan QR code jika diperlukan.')
                    ->success()
                    ->send();
            } else {
                Notification::make()
                    ->title('Gagal Restart Service')
                    ->body($result['error'] ?? 'Terjadi kesalahan saat restart service')
                    ->danger()
                    ->send();
            }

            $this->updateServiceStatus();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Error Restart Service')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function viewStatus(): void
    {
        try {
            $processService = $this->getProcessService();
            $status = $processService->getStatus();

            $statusText = $status['connected'] ? 'Terhubung' : 'Terputus';
            $processText = $status['process_running'] ? 'Running' : 'Stopped';
            $qrInfo = isset($status['qr_code']) && $status['qr_code'] ? 'QR Code tersedia' : 'Tidak ada QR Code';

            Notification::make()
                ->title('Detail Status WhatsApp')
                ->body("Process: {$processText}\nKoneksi: {$statusText}\n{$qrInfo}")
                ->info()
                ->persistent()
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error Cek Status')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
}