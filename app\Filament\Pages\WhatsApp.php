<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Card;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Actions\Action;
use App\Models\Setting;
use App\Services\WhatsAppService;

class WhatsApp extends Page implements HasForms
{
    use InteractsWithForms;

    protected static string $view = 'filament.pages.whats-app';
    protected static ?string $navigationGroup = 'Umum';
    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-oval-left-ellipsis';
    protected static ?string $navigationLabel = 'WhatsApp';

    // Form data
    public ?array $data = [];
    protected WhatsAppService $whatsappService;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Pengaturan WhatsApp')
                    ->description('Konfigurasi layanan WhatsApp untuk notifikasi absensi')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                Toggle::make('whatsapp_enabled')
                                    ->label('Aktifkan WhatsApp')
                                    ->helperText('Nyalakan untuk mengaktifkan notifikasi WhatsApp')
                                    ->live(),

                                TextInput::make('whatsapp_server_url')
                                    ->label('URL Server WhatsApp')
                                    ->default('http://localhost:3001')
                                    ->required()
                                    ->url()
                                    ->helperText('URL server Baileys WhatsApp'),
                            ]),
                    ]),

                Section::make('Template Pesan Guru')
                    ->description('Template pesan notifikasi untuk guru')
                    ->schema([
                        Textarea::make('whatsapp_template_masuk')
                            ->label('Template Pesan Absen Masuk')
                            ->rows(4)
                            ->helperText('Gunakan: {nama_guru}, {nama_sekolah}, {tanggal}, {waktu}, {jabatan}, {divisi}')
                            ->placeholder('🌅 Selamat pagi {nama_guru}!\n\nAbsensi masuk Anda telah tercatat pada {waktu}, {tanggal}.\nTerima kasih telah hadir tepat waktu.\n\nSalam,\n{nama_sekolah}'),

                        Textarea::make('whatsapp_template_pulang')
                            ->label('Template Pesan Absen Pulang')
                            ->rows(4)
                            ->helperText('Gunakan: {nama_guru}, {nama_sekolah}, {tanggal}, {waktu}, {durasi}, {jabatan}, {divisi}')
                            ->placeholder('🌆 Selamat sore {nama_guru}!\n\nAbsensi pulang Anda telah tercatat pada {waktu}, {tanggal}.\nDurasi kerja: {durasi}\n\nTerima kasih atas dedikasi Anda!\n\nSalam,\n{nama_sekolah}'),

                        Textarea::make('whatsapp_template_reminder')
                            ->label('Template Pesan Pengingat')
                            ->rows(4)
                            ->helperText('Gunakan: {nama_guru}, {nama_sekolah}, {tanggal}, {waktu}')
                            ->placeholder('⏰ Pengingat Absensi\n\nHalo {nama_guru},\n\nAnda belum melakukan absensi hari ini ({tanggal}).\nMohon segera lakukan absensi.\n\nTerima kasih,\n{nama_sekolah}'),
                    ]),
            ])
            ->statePath('data');
    }

    public function mount(): void
    {
        $this->whatsappService = app(WhatsAppService::class);

        $setting = Setting::getActive();

        $this->form->fill([
            'whatsapp_enabled' => $setting->whatsapp_enabled ?? false,
            'whatsapp_server_url' => $setting->whatsapp_server_url ?? 'http://localhost:3001',
            'whatsapp_template_masuk' => $setting->whatsapp_template_masuk ?? '',
            'whatsapp_template_pulang' => $setting->whatsapp_template_pulang ?? '',
            'whatsapp_template_reminder' => $setting->whatsapp_template_reminder ?? '',
        ]);
    }

    public function save(): void
    {
        try {
            $data = $this->form->getState();

            $setting = Setting::getActive();
            if (!$setting) {
                $setting = new Setting();
            }

            // Update WhatsApp settings
            $setting->whatsapp_enabled = $data['whatsapp_enabled'] ?? false;
            $setting->whatsapp_server_url = $data['whatsapp_server_url'] ?? 'http://localhost:3001';
            $setting->whatsapp_template_masuk = $data['whatsapp_template_masuk'] ?? null;
            $setting->whatsapp_template_pulang = $data['whatsapp_template_pulang'] ?? null;
            $setting->whatsapp_template_reminder = $data['whatsapp_template_reminder'] ?? null;

            $setting->save();

            Notification::make()
                ->title('Pengaturan WhatsApp berhasil disimpan!')
                ->success()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Gagal menyimpan pengaturan WhatsApp')
                ->body('Error: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function getFormActions(): array
    {
        return [
            Action::make('save')
                ->label('Simpan Pengaturan')
                ->action('save')
                ->color('primary'),

            Action::make('test_connection')
                ->label('Test Koneksi')
                ->action('testConnection')
                ->color('info'),

            Action::make('view_status')
                ->label('Status WhatsApp')
                ->action('viewStatus')
                ->color('gray'),
        ];
    }

    public function testConnection(): void
    {
        try {
            $status = $this->whatsappService->getStatus();

            if ($status['connected']) {
                Notification::make()
                    ->title('Koneksi WhatsApp Berhasil!')
                    ->body('Server WhatsApp terhubung dan siap digunakan.')
                    ->success()
                    ->send();
            } else {
                Notification::make()
                    ->title('Koneksi WhatsApp Gagal')
                    ->body($status['error'] ?? 'Server tidak dapat dijangkau')
                    ->warning()
                    ->send();
            }
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error Test Koneksi')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function viewStatus(): void
    {
        try {
            $status = $this->whatsappService->getStatus();

            $statusText = $status['connected'] ? 'Terhubung' : 'Terputus';
            $qrInfo = isset($status['qr']) && $status['qr'] ? 'QR Code tersedia untuk scan' : 'Tidak ada QR Code';

            Notification::make()
                ->title('Status WhatsApp Server')
                ->body("Status: {$statusText}\n{$qrInfo}")
                ->info()
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error Cek Status')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
}