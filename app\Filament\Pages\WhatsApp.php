<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;

class WhatsApp extends Page
{
    protected static string $view = 'filament.pages.whats-app';
    protected static ?string $navigationGroup = 'Umum';
    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-oval-left-ellipsis';
    protected static ?string $navigationLabel = 'WhatsApp';

    // Properti publik untuk field form
    public $pesan_hadir_siswa = '';
    public $pesan_pulang_siswa = '';
    public $pesan_tidak_absen_siswa = '';
    public $pesan_tidak_absen_pulang_siswa = '';
    public $pesan_hadir_guru = '';
    public $pesan_pulang_guru = '';
    public $pesan_tidak_absen_guru = '';
    public $pesan_tidak_absen_pulang_guru = '';

    protected function getFormSchema(): array
    {
        return [
            TextInput::make('pesan_hadir_siswa')
                ->label('Pesan Hadir Siswa'),
            TextInput::make('pesan_pulang_siswa')
                ->label('Pesan Pulang Siswa'),
            TextInput::make('pesan_tidak_absen_siswa')
                ->label('Pesan Tidak Absen Siswa'),
            TextInput::make('pesan_tidak_absen_pulang_siswa')
                ->label('Pesan Tidak Absen Pulang Siswa'),
            TextInput::make('pesan_hadir_guru')
                ->label('Pesan Hadir Guru'),
            TextInput::make('pesan_pulang_guru')
                ->label('Pesan Pulang Guru'),
            TextInput::make('pesan_tidak_absen_guru')
                ->label('Pesan Tidak Absen Guru'),
            TextInput::make('pesan_tidak_absen_pulang_guru')
                ->label('Pesan Tidak Absen Pulang Guru'),
        ];
    }

    public function mount()
    {
        $settings = DB::table('settings')->first();
        if ($settings) {
            $this->pesan_hadir_siswa = $settings->pesan_hadir_siswa ?? '';
            $this->pesan_pulang_siswa = $settings->pesan_pulang_siswa ?? '';
            $this->pesan_tidak_absen_siswa = $settings->pesan_tidak_absen_siswa ?? '';
            $this->pesan_tidak_absen_pulang_siswa = $settings->pesan_tidak_absen_pulang_siswa ?? '';
            $this->pesan_hadir_guru = $settings->pesan_hadir_guru ?? '';
            $this->pesan_pulang_guru = $settings->pesan_pulang_guru ?? '';
            $this->pesan_tidak_absen_guru = $settings->pesan_tidak_absen_guru ?? '';
            $this->pesan_tidak_absen_pulang_guru = $settings->pesan_tidak_absen_pulang_guru ?? '';
        }
        $this->form->fill([
        'pesan_hadir_siswa' => $this->pesan_hadir_siswa,
        'pesan_pulang_siswa' => $this->pesan_pulang_siswa,
        'pesan_tidak_absen_siswa' => $this->pesan_tidak_absen_siswa,
        'pesan_tidak_absen_pulang_siswa' => $this->pesan_tidak_absen_pulang_siswa,
        'pesan_hadir_guru' => $this->pesan_hadir_guru,
        'pesan_pulang_guru' => $this->pesan_pulang_guru,
        'pesan_tidak_absen_guru' => $this->pesan_tidak_absen_guru,
        'pesan_tidak_absen_pulang_guru' => $this->pesan_tidak_absen_pulang_guru,
    ]);
    }

    public function submit()
    {
        $data = $this->form->getState();
        DB::table('settings')->updateOrInsert(['id' => 1], $data);
        Notification::make()->title('Pengaturan whatsapp disimpan!')->success()->send();
    }
}