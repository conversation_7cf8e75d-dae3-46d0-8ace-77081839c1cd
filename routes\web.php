<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\MqttController;
use App\Http\Controllers\DeviceController;
use App\Http\Controllers\PublicDashboardController;

Route::get('/', function () {
    return view('welcome');
});

// Public Dashboard Routes
Route::get('/dashboard', [PublicDashboardController::class, 'index'])->name('public.dashboard');

// API Routes for Dashboard
Route::prefix('api')->group(function () {
    Route::get('/absensi-today', [PublicDashboardController::class, 'getAbsensiToday']);
    Route::get('/current-time', [PublicDashboardController::class, 'getCurrentTime']);
});