{"version": 3, "sources": ["lib/prelude.js", "../node_modules/@protobufjs/aspromise/index.js", "../node_modules/@protobufjs/base64/index.js", "../node_modules/@protobufjs/codegen/index.js", "../node_modules/@protobufjs/eventemitter/index.js", "../node_modules/@protobufjs/fetch/index.js", "../node_modules/@protobufjs/float/index.js", "../node_modules/@protobufjs/inquire/index.js", "../node_modules/@protobufjs/path/index.js", "../node_modules/@protobufjs/pool/index.js", "../node_modules/@protobufjs/utf8/index.js", "../src/common.js", "../src/converter.js", "../src/decoder.js", "../src/encoder.js", "../src/enum.js", "../src/field.js", "../src/index-light.js", "../src/index-minimal.js", "../src/index", "../src/mapfield.js", "../src/message.js", "../src/method.js", "../src/namespace.js", "../src/object.js", "../src/oneof.js", "../src/parse.js", "../src/reader.js", "../src/reader_buffer.js", "../src/root.js", "../src/roots.js", "../src/rpc.js", "../src/rpc/service.js", "../src/service.js", "../src/tokenize.js", "../src/type.js", "../src/types.js", "../src/util.js", "../src/util/longbits.js", "../src/util/minimal.js", "../src/verifier.js", "../src/wrappers.js", "../src/writer.js", "../src/writer_buffer.js"], "names": ["undefined", "modules", "cache", "entries", "protobuf", "1", "require", "module", "exports", "fn", "ctx", "params", "Array", "arguments", "length", "offset", "index", "pending", "Promise", "resolve", "reject", "err", "apply", "base64", "string", "p", "n", "char<PERSON>t", "Math", "ceil", "b64", "s64", "i", "encode", "buffer", "start", "end", "t", "parts", "chunk", "j", "b", "push", "String", "fromCharCode", "slice", "join", "invalidEncoding", "decode", "c", "charCodeAt", "Error", "test", "codegen", "functionParams", "functionName", "body", "Codegen", "formatStringOrScope", "source", "toString", "verbose", "console", "log", "scopeKeys", "Object", "keys", "scopeParams", "scopeValues", "scopeOffset", "Function", "formatParams", "formatOffset", "replace", "$0", "$1", "value", "floor", "JSON", "stringify", "functionNameOverride", "EventEmitter", "this", "_listeners", "prototype", "on", "evt", "off", "listeners", "splice", "emit", "args", "fetch", "<PERSON><PERSON><PERSON><PERSON>", "fs", "inquire", "filename", "options", "callback", "xhr", "readFile", "contents", "XMLHttpRequest", "binary", "onreadystatechange", "readyState", "status", "response", "responseText", "Uint8Array", "overrideMimeType", "responseType", "open", "send", "factory", "Float32Array", "f32", "f8b", "le", "writeFloat_f32_cpy", "val", "buf", "pos", "writeFloat_f32_rev", "readFloat_f32_cpy", "readFloat_f32_rev", "writeFloatLE", "writeFloatBE", "readFloatLE", "readFloatBE", "writeFloat_ieee754", "writeUint", "sign", "isNaN", "round", "exponent", "LN2", "pow", "readFloat_ieee754", "readUint", "uint", "mantissa", "NaN", "Infinity", "bind", "writeUintLE", "writeUintBE", "readUintLE", "readUintBE", "Float64Array", "f64", "writeDouble_f64_cpy", "writeDouble_f64_rev", "readDouble_f64_cpy", "readDouble_f64_rev", "writeDoubleLE", "writeDoubleBE", "readDoubleLE", "readDoubleBE", "writeDouble_ieee754", "off0", "off1", "readDouble_ieee754", "lo", "hi", "moduleName", "mod", "eval", "e", "path", "isAbsolute", "normalize", "split", "absolute", "prefix", "shift", "originPath", "include<PERSON>ath", "alreadyNormalized", "alloc", "size", "SIZE", "MAX", "slab", "call", "utf8", "len", "read", "write", "c1", "c2", "common", "timeType", "commonRe", "name", "json", "nested", "google", "Any", "fields", "type_url", "type", "id", "Duration", "seconds", "nanos", "Timestamp", "Empty", "Struct", "keyType", "Value", "oneofs", "kind", "oneof", "nullValue", "numberValue", "stringValue", "boolValue", "structValue", "listValue", "Null<PERSON><PERSON>ue", "values", "NULL_VALUE", "ListValue", "rule", "DoubleValue", "FloatValue", "Int64Value", "UInt64Value", "Int32Value", "UInt32Value", "BoolValue", "StringValue", "BytesValue", "FieldMask", "paths", "get", "file", "converter", "Enum", "util", "genValuePartial_fromObject", "gen", "field", "fieldIndex", "prop", "resolvedType", "repeated", "typeDefault", "fullName", "isUnsigned", "genValuePartial_toObject", "fromObject", "mtype", "fieldsArray", "safeProp", "map", "toObject", "sort", "compareFieldsById", "repeatedFields", "mapFields", "normalFields", "partOf", "valuesById", "long", "low", "high", "unsigned", "toNumber", "bytes", "arrayDefault", "hasKs2", "_fieldsArray", "indexOf", "filter", "group", "ref", "types", "basic", "packed", "rfield", "required", "wireType", "mapKey", "genTypePartial", "optional", "ReflectionObject", "create", "constructor", "className", "Namespace", "comment", "comments", "TypeError", "reserved", "fromJSON", "enm", "toJSON", "toJSONOptions", "keepComments", "add", "isString", "isInteger", "isReservedId", "isReservedName", "allow_alias", "remove", "Field", "Type", "ruleRe", "extend", "isObject", "toLowerCase", "message", "defaultValue", "<PERSON>", "extensionField", "declaringField", "_packed", "defineProperty", "getOption", "setOption", "ifNotSet", "resolved", "defaults", "parent", "lookupTypeOrEnum", "fromNumber", "freeze", "new<PERSON>uffer", "emptyObject", "emptyArray", "ctor", "d", "fieldId", "fieldType", "fieldRule", "decorateType", "decorateEnum", "fieldName", "default", "_configure", "Type_", "build", "load", "root", "Root", "loadSync", "encoder", "decoder", "verifier", "OneOf", "MapField", "Service", "Method", "Message", "wrappers", "configure", "Reader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Writer", "BufferWriter", "rpc", "roots", "tokenize", "parse", "resolvedKeyType", "fieldKeyType", "fieldValueType", "properties", "$type", "writer", "encodeDelimited", "reader", "decodeDelimited", "verify", "object", "requestType", "requestStream", "responseStream", "resolvedRequestType", "resolvedResponseType", "lookupType", "arrayToJSON", "array", "obj", "_nested<PERSON><PERSON>y", "clearCache", "namespace", "addJSON", "toArray", "nested<PERSON><PERSON><PERSON>", "nested<PERSON><PERSON>", "names", "methods", "getEnum", "prev", "setOptions", "onAdd", "onRemove", "define", "isArray", "ptr", "part", "resolveAll", "lookup", "filterTypes", "parentAlreadyChecked", "found", "lookupEnum", "lookupService", "Service_", "Enum_", "defineProperties", "unshift", "_handleAdd", "_handleRemove", "Root_", "fieldNames", "addFieldsToParent", "oneofName", "oneOfGetter", "set", "oneOfSetter", "keepCase", "base10Re", "base10NegRe", "base16Re", "base16NegRe", "base8Re", "base8NegRe", "numberRe", "nameRe", "typeRefRe", "fqTypeRefRe", "pkg", "imports", "weakImports", "syntax", "token", "tn", "alternateCommentMode", "next", "peek", "skip", "cmnt", "head", "isProto3", "applyCase", "camelCase", "illegal", "insideTryCatch", "line", "readString", "readValue", "acceptTypeRef", "substring", "parseInt", "parseFloat", "parseNumber", "readRanges", "target", "acceptStrings", "parseId", "acceptNegative", "parsePackage", "parseImport", "whichImports", "parseSyntax", "parse<PERSON><PERSON><PERSON>", "parseOption", "ifBlock", "valueType", "parseInlineOptions", "parseMapField", "parseField", "parseOneOf", "extensions", "parseType", "dummy", "parseEnumValue", "parseEnum", "service", "method", "parseMethod", "parseService", "reference", "parseExtension", "fnIf", "fnElse", "trailingLine", "lcFirst", "ucFirst", "parseGroup", "isCustom", "parseOptionValue", "package", "LongBits", "indexOutOfRange", "write<PERSON><PERSON>th", "RangeError", "create_array", "readLongVarint", "bits", "readFixed32_end", "readFixed64", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_slice", "subarray", "uint32", "int32", "sint32", "bool", "fixed32", "sfixed32", "float", "double", "skipType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_", "merge", "int64", "uint64", "sint64", "zzDecode", "fixed64", "sfixed64", "utf8Slice", "min", "deferred", "files", "SYNC", "<PERSON><PERSON><PERSON>", "self", "sync", "finish", "cb", "process", "parsed", "queued", "weak", "idx", "lastIndexOf", "altname", "setTimeout", "readFileSync", "isNode", "exposeRe", "tryHandleExtension", "extendedType", "sisterField", "parse_", "common_", "rpcImpl", "requestDelimited", "responseDelimited", "rpcCall", "requestCtor", "responseCtor", "request", "endedByRPC", "_methodsArray", "inherited", "methodsArray", "rpcService", "methodName", "isReserved", "m", "q", "s", "delimRe", "stringDoubleRe", "stringSingleRe", "setCommentRe", "setCommentAltRe", "setCommentSplitRe", "whitespaceRe", "unescapeRe", "unescapeMap", "0", "r", "unescape", "str", "commentType", "commentText", "commentLine", "commentLineEmpty", "stack", "<PERSON><PERSON><PERSON><PERSON>", "subject", "setComment", "commentOffset", "lines", "trim", "isDoubleSlashCommentLine", "startOffset", "endOffset", "findEndOfLine", "lineText", "cursor", "re", "lastIndex", "match", "exec", "repeat", "curr", "isDoc", "expected", "actual", "ret", "_fieldsById", "_oneofsArray", "_ctor", "fieldsById", "oneofsArray", "generateConstructor", "ctorProperties", "setup", "wrapper", "originalThis", "fork", "l<PERSON>im", "typeName", "bake", "o", "key", "safePropBackslashRe", "safePropQuoteRe", "toUpperCase", "camelCaseRe", "a", "decorateRoot", "enumerable", "decorateEnumIndex", "zero", "zzEncode", "zeroHash", "from", "fromString", "toLong", "fromHash", "hash", "toHash", "mask", "part0", "part1", "part2", "dst", "src", "newError", "CustomError", "captureStackTrace", "pool", "global", "window", "versions", "node", "Number", "isFinite", "isset", "isSet", "hasOwnProperty", "utf8Write", "_B<PERSON>er_from", "_Buffer_allocUnsafe", "sizeOrArray", "dcodeIO", "key2Re", "key32Re", "key64Re", "longToHash", "longFromHash", "fromBits", "ProtocolError", "fieldMap", "longs", "enums", "encoding", "allocUnsafe", "seenFirstField", "invalid", "genVerifyKey", "genVerifyValue", "oneofProp", "substr", "Op", "noop", "State", "tail", "states", "writeByte", "VarintOp", "writeVarint64", "writeFixed32", "_push", "writeBytes", "reset", "BufferWriter_", "writeBytesBuffer", "copy", "writeStringBuffer", "byteLength", "$require", "$module", "amd", "isLong"], "mappings": ";;;;;;CAAA,SAAAA,IAAA,aAAA,IAAAC,EAAAC,EAAAC,EAcAC,EAdAH,EAiCA,CAAAI,EAAA,CAAA,SAAAC,EAAAC,GChCAA,EAAAC,QAmBA,SAAAC,EAAAC,GACA,IAAAC,EAAAC,MAAAC,UAAAC,OAAA,GACAC,EAAA,EACAC,EAAA,EACAC,GAAA,EACA,KAAAD,EAAAH,UAAAC,QACAH,EAAAI,KAAAF,UAAAG,KACA,OAAA,IAAAE,QAAA,SAAAC,EAAAC,GACAT,EAAAI,GAAA,SAAAM,GACA,GAAAJ,EAEA,GADAA,GAAA,EACAI,EACAD,EAAAC,OACA,CAGA,IAFA,IAAAV,EAAAC,MAAAC,UAAAC,OAAA,GACAC,EAAA,EACAA,EAAAJ,EAAAG,QACAH,EAAAI,KAAAF,UAAAE,GACAI,EAAAG,MAAA,KAAAX,KAIA,IACAF,EAAAa,MAAAZ,GAAA,KAAAC,GACA,MAAAU,GACAJ,IACAA,GAAA,EACAG,EAAAC,gCCxCA,IAAAE,EAAAf,EAOAe,EAAAT,OAAA,SAAAU,GACA,IAAAC,EAAAD,EAAAV,OACA,IAAAW,EACA,OAAA,EAEA,IADA,IAAAC,EAAA,EACA,IAAAD,EAAA,GAAA,MAAAD,EAAAG,OAAAF,MACAC,EACA,OAAAE,KAAAC,KAAA,EAAAL,EAAAV,QAAA,EAAAY,GAUA,IANA,IAAAI,EAAAlB,MAAA,IAGAmB,EAAAnB,MAAA,KAGAoB,EAAA,EAAAA,EAAA,IACAD,EAAAD,EAAAE,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,EAAAA,EAAA,GAAA,IAAAA,IASAT,EAAAU,OAAA,SAAAC,EAAAC,EAAAC,GAMA,IALA,IAIAC,EAJAC,EAAA,KACAC,EAAA,GACAP,EAAA,EACAQ,EAAA,EAEAL,EAAAC,GAAA,CACA,IAAAK,EAAAP,EAAAC,KACA,OAAAK,GACA,KAAA,EACAD,EAAAP,KAAAF,EAAAW,GAAA,GACAJ,GAAA,EAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,KAAAF,EAAAO,EAAAI,GAAA,GACAJ,GAAA,GAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,KAAAF,EAAAO,EAAAI,GAAA,GACAF,EAAAP,KAAAF,EAAA,GAAAW,GACAD,EAAA,EAGA,KAAAR,KACAM,IAAAA,EAAA,KAAAI,KAAAC,OAAAC,aAAAtB,MAAAqB,OAAAJ,IACAP,EAAA,GASA,OANAQ,IACAD,EAAAP,KAAAF,EAAAO,GACAE,EAAAP,KAAA,GACA,IAAAQ,IACAD,EAAAP,KAAA,KAEAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAAtB,MAAAqB,OAAAJ,EAAAM,MAAA,EAAAb,KACAM,EAAAQ,KAAA,KAEAH,OAAAC,aAAAtB,MAAAqB,OAAAJ,EAAAM,MAAA,EAAAb,KAGA,IAAAe,EAAA,mBAUAxB,EAAAyB,OAAA,SAAAxB,EAAAU,EAAAnB,GAIA,IAHA,IAEAsB,EAFAF,EAAApB,EACAyB,EAAA,EAEAR,EAAA,EAAAA,EAAAR,EAAAV,QAAA,CACA,IAAAmC,EAAAzB,EAAA0B,WAAAlB,KACA,GAAA,KAAAiB,GAAA,EAAAT,EACA,MACA,IAAAS,EAAAlB,EAAAkB,MAAAjD,GACA,MAAAmD,MAAAJ,GACA,OAAAP,GACA,KAAA,EACAH,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAnB,KAAAsB,GAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAnB,MAAA,GAAAsB,IAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAnB,MAAA,EAAAsB,IAAA,EAAAY,EACAT,EAAA,GAIA,GAAA,IAAAA,EACA,MAAAW,MAAAJ,GACA,OAAAhC,EAAAoB,GAQAZ,EAAA6B,KAAA,SAAA5B,GACA,MAAA,mEAAA4B,KAAA5B,0BC/HA,SAAA6B,EAAAC,EAAAC,GAGA,iBAAAD,IACAC,EAAAD,EACAA,EAAAtD,IAGA,IAAAwD,EAAA,GAYA,SAAAC,EAAAC,GAIA,GAAA,iBAAAA,EAAA,CACA,IAAAC,EAAAC,IAIA,GAHAP,EAAAQ,SACAC,QAAAC,IAAA,YAAAJ,GACAA,EAAA,UAAAA,EACAD,EAAA,CAKA,IAJA,IAAAM,EAAAC,OAAAC,KAAAR,GACAS,EAAAvD,MAAAoD,EAAAlD,OAAA,GACAsD,EAAAxD,MAAAoD,EAAAlD,QACAuD,EAAA,EACAA,EAAAL,EAAAlD,QACAqD,EAAAE,GAAAL,EAAAK,GACAD,EAAAC,GAAAX,EAAAM,EAAAK,MAGA,OADAF,EAAAE,GAAAV,EACAW,SAAAhD,MAAA,KAAA6C,GAAA7C,MAAA,KAAA8C,GAEA,OAAAE,SAAAX,EAAAW,GAMA,IAFA,IAAAC,EAAA3D,MAAAC,UAAAC,OAAA,GACA0D,EAAA,EACAA,EAAAD,EAAAzD,QACAyD,EAAAC,GAAA3D,YAAA2D,GAYA,GAXAA,EAAA,EACAd,EAAAA,EAAAe,QAAA,eAAA,SAAAC,EAAAC,GACA,IAAAC,EAAAL,EAAAC,KACA,OAAAG,GACA,IAAA,IAAA,IAAA,IAAA,OAAAC,EAAAjC,GACA,IAAA,IAAA,OAAAf,KAAAiD,MAAAD,GAAAjC,GACA,IAAA,IAAA,OAAAmC,KAAAC,UAAAH,GACA,IAAA,IAAA,OAAAA,EAAAjC,GAEA,MAAA,MAEA6B,IAAAD,EAAAzD,OACA,MAAAqC,MAAA,4BAEA,OADAK,EAAAd,KAAAgB,GACAD,EAGA,SAAAG,EAAAoB,GACA,MAAA,aAAAA,GAAAzB,GAAA,IAAA,KAAAD,GAAAA,EAAAR,KAAA,MAAA,IAAA,SAAAU,EAAAV,KAAA,QAAA,MAIA,OADAW,EAAAG,SAAAA,EACAH,GAhFAlD,EAAAC,QAAA6C,GAiGAQ,SAAA,wBCzFA,SAAAoB,IAOAC,KAAAC,EAAA,IAfA5E,EAAAC,QAAAyE,GAyBAG,UAAAC,GAAA,SAAAC,EAAA7E,EAAAC,GAKA,OAJAwE,KAAAC,EAAAG,KAAAJ,KAAAC,EAAAG,GAAA,KAAA5C,KAAA,CACAjC,GAAAA,EACAC,IAAAA,GAAAwE,OAEAA,MASAD,EAAAG,UAAAG,IAAA,SAAAD,EAAA7E,GACA,GAAA6E,IAAAtF,GACAkF,KAAAC,EAAA,QAEA,GAAA1E,IAAAT,GACAkF,KAAAC,EAAAG,GAAA,QAGA,IADA,IAAAE,EAAAN,KAAAC,EAAAG,GACAtD,EAAA,EAAAA,EAAAwD,EAAA1E,QACA0E,EAAAxD,GAAAvB,KAAAA,EACA+E,EAAAC,OAAAzD,EAAA,KAEAA,EAGA,OAAAkD,MASAD,EAAAG,UAAAM,KAAA,SAAAJ,GACA,IAAAE,EAAAN,KAAAC,EAAAG,GACA,GAAAE,EAAA,CAGA,IAFA,IAAAG,EAAA,GACA3D,EAAA,EACAA,EAAAnB,UAAAC,QACA6E,EAAAjD,KAAA7B,UAAAmB,MACA,IAAAA,EAAA,EAAAA,EAAAwD,EAAA1E,QACA0E,EAAAxD,GAAAvB,GAAAa,MAAAkE,EAAAxD,KAAAtB,IAAAiF,GAEA,OAAAT,4BCzEA3E,EAAAC,QAAAoF,EAEA,IAAAC,EAAAvF,EAAA,GAGAwF,EAFAxF,EAAA,EAEAyF,CAAA,MA2BA,SAAAH,EAAAI,EAAAC,EAAAC,GAOA,MANA,mBAAAD,GACAC,EAAAD,EACAA,EAAA,IACAA,IACAA,EAAA,IAEAC,GAIAD,EAAAE,KAAAL,GAAAA,EAAAM,SACAN,EAAAM,SAAAJ,EAAA,SAAA3E,EAAAgF,GACA,OAAAhF,GAAA,oBAAAiF,eACAV,EAAAO,IAAAH,EAAAC,EAAAC,GACA7E,EACA6E,EAAA7E,GACA6E,EAAA,KAAAD,EAAAM,OAAAF,EAAAA,EAAAzC,SAAA,WAIAgC,EAAAO,IAAAH,EAAAC,EAAAC,GAbAL,EAAAD,EAAAV,KAAAc,EAAAC,GAqCAL,EAAAO,IAAA,SAAAH,EAAAC,EAAAC,GACA,IAAAC,EAAA,IAAAG,eACAH,EAAAK,mBAAA,WAEA,GAAA,IAAAL,EAAAM,WACA,OAAAzG,GAKA,GAAA,IAAAmG,EAAAO,QAAA,MAAAP,EAAAO,OACA,OAAAR,EAAA/C,MAAA,UAAAgD,EAAAO,SAIA,GAAAT,EAAAM,OAAA,CACA,IAAArE,EAAAiE,EAAAQ,SACA,IAAAzE,EAAA,CACAA,EAAA,GACA,IAAA,IAAAF,EAAA,EAAAA,EAAAmE,EAAAS,aAAA9F,SAAAkB,EACAE,EAAAQ,KAAA,IAAAyD,EAAAS,aAAA1D,WAAAlB,IAEA,OAAAkE,EAAA,KAAA,oBAAAW,WAAA,IAAAA,WAAA3E,GAAAA,GAEA,OAAAgE,EAAA,KAAAC,EAAAS,eAGAX,EAAAM,SAEA,qBAAAJ,GACAA,EAAAW,iBAAA,sCACAX,EAAAY,aAAA,eAGAZ,EAAAa,KAAA,MAAAhB,GACAG,EAAAc,qCC1BA,SAAAC,EAAA1G,GAwNA,MArNA,oBAAA2G,aAAA,WAEA,IAAAC,EAAA,IAAAD,aAAA,EAAA,IACAE,EAAA,IAAAR,WAAAO,EAAAlF,QACAoF,EAAA,MAAAD,EAAA,GAEA,SAAAE,EAAAC,EAAAC,EAAAC,GACAN,EAAA,GAAAI,EACAC,EAAAC,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GAGA,SAAAM,EAAAH,EAAAC,EAAAC,GACAN,EAAA,GAAAI,EACAC,EAAAC,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GAQA,SAAAO,EAAAH,EAAAC,GAKA,OAJAL,EAAA,GAAAI,EAAAC,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAN,EAAA,GAGA,SAAAS,EAAAJ,EAAAC,GAKA,OAJAL,EAAA,GAAAI,EAAAC,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAN,EAAA,GAjBA5G,EAAAsH,aAAAR,EAAAC,EAAAI,EAEAnH,EAAAuH,aAAAT,EAAAK,EAAAJ,EAmBA/G,EAAAwH,YAAAV,EAAAM,EAAAC,EAEArH,EAAAyH,YAAAX,EAAAO,EAAAD,EA9CA,GAiDA,WAEA,SAAAM,EAAAC,EAAAX,EAAAC,EAAAC,GACA,IAAAU,EAAAZ,EAAA,EAAA,EAAA,EAGA,GAFAY,IACAZ,GAAAA,GACA,IAAAA,EACAW,EAAA,EAAA,EAAAX,EAAA,EAAA,WAAAC,EAAAC,QACA,GAAAW,MAAAb,GACAW,EAAA,WAAAV,EAAAC,QACA,GAAA,qBAAAF,EACAW,GAAAC,GAAA,GAAA,cAAA,EAAAX,EAAAC,QACA,GAAAF,EAAA,sBACAW,GAAAC,GAAA,GAAAxG,KAAA0G,MAAAd,EAAA,yBAAA,EAAAC,EAAAC,OACA,CACA,IAAAa,EAAA3G,KAAAiD,MAAAjD,KAAAmC,IAAAyD,GAAA5F,KAAA4G,KAEAL,GAAAC,GAAA,GAAAG,EAAA,KAAA,GADA,QAAA3G,KAAA0G,MAAAd,EAAA5F,KAAA6G,IAAA,GAAAF,GAAA,YACA,EAAAd,EAAAC,IAOA,SAAAgB,EAAAC,EAAAlB,EAAAC,GACA,IAAAkB,EAAAD,EAAAlB,EAAAC,GACAU,EAAA,GAAAQ,GAAA,IAAA,EACAL,EAAAK,IAAA,GAAA,IACAC,EAAA,QAAAD,EACA,OAAA,MAAAL,EACAM,EACAC,IACAV,GAAAW,EAAAA,GACA,IAAAR,EACA,qBAAAH,EAAAS,EACAT,EAAAxG,KAAA6G,IAAA,EAAAF,EAAA,MAAAM,EAAA,SAdArI,EAAAsH,aAAAI,EAAAc,KAAA,KAAAC,GACAzI,EAAAuH,aAAAG,EAAAc,KAAA,KAAAE,GAgBA1I,EAAAwH,YAAAU,EAAAM,KAAA,KAAAG,GACA3I,EAAAyH,YAAAS,EAAAM,KAAA,KAAAI,GAvCA,GA4CA,oBAAAC,aAAA,WAEA,IAAAC,EAAA,IAAAD,aAAA,EAAA,IACAhC,EAAA,IAAAR,WAAAyC,EAAApH,QACAoF,EAAA,MAAAD,EAAA,GAEA,SAAAkC,EAAA/B,EAAAC,EAAAC,GACA4B,EAAA,GAAA9B,EACAC,EAAAC,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GAGA,SAAAmC,EAAAhC,EAAAC,EAAAC,GACA4B,EAAA,GAAA9B,EACAC,EAAAC,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GAQA,SAAAoC,EAAAhC,EAAAC,GASA,OARAL,EAAA,GAAAI,EAAAC,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACA4B,EAAA,GAGA,SAAAI,EAAAjC,EAAAC,GASA,OARAL,EAAA,GAAAI,EAAAC,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACA4B,EAAA,GAzBA9I,EAAAmJ,cAAArC,EAAAiC,EAAAC,EAEAhJ,EAAAoJ,cAAAtC,EAAAkC,EAAAD,EA2BA/I,EAAAqJ,aAAAvC,EAAAmC,EAAAC,EAEAlJ,EAAAsJ,aAAAxC,EAAAoC,EAAAD,EA9DA,GAiEA,WAEA,SAAAM,EAAA5B,EAAA6B,EAAAC,EAAAzC,EAAAC,EAAAC,GACA,IAAAU,EAAAZ,EAAA,EAAA,EAAA,EAGA,GAFAY,IACAZ,GAAAA,GACA,IAAAA,EACAW,EAAA,EAAAV,EAAAC,EAAAsC,GACA7B,EAAA,EAAA,EAAAX,EAAA,EAAA,WAAAC,EAAAC,EAAAuC,QACA,GAAA5B,MAAAb,GACAW,EAAA,EAAAV,EAAAC,EAAAsC,GACA7B,EAAA,WAAAV,EAAAC,EAAAuC,QACA,GAAA,sBAAAzC,EACAW,EAAA,EAAAV,EAAAC,EAAAsC,GACA7B,GAAAC,GAAA,GAAA,cAAA,EAAAX,EAAAC,EAAAuC,OACA,CACA,IAAApB,EACA,GAAArB,EAAA,uBAEAW,GADAU,EAAArB,EAAA,UACA,EAAAC,EAAAC,EAAAsC,GACA7B,GAAAC,GAAA,GAAAS,EAAA,cAAA,EAAApB,EAAAC,EAAAuC,OACA,CACA,IAAA1B,EAAA3G,KAAAiD,MAAAjD,KAAAmC,IAAAyD,GAAA5F,KAAA4G,KACA,OAAAD,IACAA,EAAA,MAEAJ,EAAA,kBADAU,EAAArB,EAAA5F,KAAA6G,IAAA,GAAAF,MACA,EAAAd,EAAAC,EAAAsC,GACA7B,GAAAC,GAAA,GAAAG,EAAA,MAAA,GAAA,QAAAM,EAAA,WAAA,EAAApB,EAAAC,EAAAuC,KAQA,SAAAC,EAAAvB,EAAAqB,EAAAC,EAAAxC,EAAAC,GACA,IAAAyC,EAAAxB,EAAAlB,EAAAC,EAAAsC,GACAI,EAAAzB,EAAAlB,EAAAC,EAAAuC,GACA7B,EAAA,GAAAgC,GAAA,IAAA,EACA7B,EAAA6B,IAAA,GAAA,KACAvB,EAAA,YAAA,QAAAuB,GAAAD,EACA,OAAA,OAAA5B,EACAM,EACAC,IACAV,GAAAW,EAAAA,GACA,IAAAR,EACA,OAAAH,EAAAS,EACAT,EAAAxG,KAAA6G,IAAA,EAAAF,EAAA,OAAAM,EAAA,kBAfArI,EAAAmJ,cAAAI,EAAAf,KAAA,KAAAC,EAAA,EAAA,GACAzI,EAAAoJ,cAAAG,EAAAf,KAAA,KAAAE,EAAA,EAAA,GAiBA1I,EAAAqJ,aAAAK,EAAAlB,KAAA,KAAAG,EAAA,EAAA,GACA3I,EAAAsJ,aAAAI,EAAAlB,KAAA,KAAAI,EAAA,EAAA,GAnDA,GAuDA5I,EAKA,SAAAyI,EAAAzB,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAGA,SAAA0B,EAAA1B,EAAAC,EAAAC,GACAD,EAAAC,GAAAF,IAAA,GACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAA,IAAAF,EAGA,SAAA2B,EAAA1B,EAAAC,GACA,OAAAD,EAAAC,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,MAAA,EAGA,SAAA0B,EAAA3B,EAAAC,GACA,OAAAD,EAAAC,IAAA,GACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,MAAA,EA3UAnH,EAAAC,QAAA0G,EAAAA,2BCOA,SAAAnB,EAAAsE,GACA,IACA,IAAAC,EAAAC,KAAA,UAAAA,CAAAF,GACA,GAAAC,IAAAA,EAAAxJ,QAAAmD,OAAAC,KAAAoG,GAAAxJ,QACA,OAAAwJ,EACA,MAAAE,IACA,OAAA,KAdAjK,EAAAC,QAAAuF,0BCMA,IAAA0E,EAAAjK,EAEAkK,EAMAD,EAAAC,WAAA,SAAAD,GACA,MAAA,eAAArH,KAAAqH,IAGAE,EAMAF,EAAAE,UAAA,SAAAF,GAGA,IAAAnI,GAFAmI,EAAAA,EAAAhG,QAAA,MAAA,KACAA,QAAA,UAAA,MACAmG,MAAA,KACAC,EAAAH,EAAAD,GACAK,EAAA,GACAD,IACAC,EAAAxI,EAAAyI,QAAA,KACA,IAAA,IAAA/I,EAAA,EAAAA,EAAAM,EAAAxB,QACA,OAAAwB,EAAAN,GACA,EAAAA,GAAA,OAAAM,EAAAN,EAAA,GACAM,EAAAmD,SAAAzD,EAAA,GACA6I,EACAvI,EAAAmD,OAAAzD,EAAA,KAEAA,EACA,MAAAM,EAAAN,GACAM,EAAAmD,OAAAzD,EAAA,KAEAA,EAEA,OAAA8I,EAAAxI,EAAAQ,KAAA,MAUA2H,EAAAtJ,QAAA,SAAA6J,EAAAC,EAAAC,GAGA,OAFAA,IACAD,EAAAN,EAAAM,IACAP,EAAAO,GACAA,GACAC,IACAF,EAAAL,EAAAK,KACAA,EAAAA,EAAAvG,QAAA,iBAAA,KAAA3D,OAAA6J,EAAAK,EAAA,IAAAC,GAAAA,0BC9DA1K,EAAAC,QA6BA,SAAA2K,EAAAtI,EAAAuI,GACA,IAAAC,EAAAD,GAAA,KACAE,EAAAD,IAAA,EACAE,EAAA,KACAxK,EAAAsK,EACA,OAAA,SAAAD,GACA,GAAAA,EAAA,GAAAE,EAAAF,EACA,OAAAD,EAAAC,GACAC,EAAAtK,EAAAqK,IACAG,EAAAJ,EAAAE,GACAtK,EAAA,GAEA,IAAA0G,EAAA5E,EAAA2I,KAAAD,EAAAxK,EAAAA,GAAAqK,GAGA,OAFA,EAAArK,IACAA,EAAA,GAAA,EAAAA,IACA0G,6BCtCA,IAAAgE,EAAAjL,EAOAiL,EAAA3K,OAAA,SAAAU,GAGA,IAFA,IAAAkK,EAAA,EACAzI,EAAA,EACAjB,EAAA,EAAAA,EAAAR,EAAAV,SAAAkB,GACAiB,EAAAzB,EAAA0B,WAAAlB,IACA,IACA0J,GAAA,EACAzI,EAAA,KACAyI,GAAA,EACA,QAAA,MAAAzI,IAAA,QAAA,MAAAzB,EAAA0B,WAAAlB,EAAA,OACAA,EACA0J,GAAA,GAEAA,GAAA,EAEA,OAAAA,GAUAD,EAAAE,KAAA,SAAAzJ,EAAAC,EAAAC,GAEA,GADAA,EAAAD,EACA,EACA,MAAA,GAKA,IAJA,IAGAE,EAHAC,EAAA,KACAC,EAAA,GACAP,EAAA,EAEAG,EAAAC,IACAC,EAAAH,EAAAC,MACA,IACAI,EAAAP,KAAAK,EACA,IAAAA,GAAAA,EAAA,IACAE,EAAAP,MAAA,GAAAK,IAAA,EAAA,GAAAH,EAAAC,KACA,IAAAE,GAAAA,EAAA,KACAA,IAAA,EAAAA,IAAA,IAAA,GAAAH,EAAAC,OAAA,IAAA,GAAAD,EAAAC,OAAA,EAAA,GAAAD,EAAAC,MAAA,MACAI,EAAAP,KAAA,OAAAK,GAAA,IACAE,EAAAP,KAAA,OAAA,KAAAK,IAEAE,EAAAP,MAAA,GAAAK,IAAA,IAAA,GAAAH,EAAAC,OAAA,EAAA,GAAAD,EAAAC,KACA,KAAAH,KACAM,IAAAA,EAAA,KAAAI,KAAAC,OAAAC,aAAAtB,MAAAqB,OAAAJ,IACAP,EAAA,GAGA,OAAAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAAtB,MAAAqB,OAAAJ,EAAAM,MAAA,EAAAb,KACAM,EAAAQ,KAAA,KAEAH,OAAAC,aAAAtB,MAAAqB,OAAAJ,EAAAM,MAAA,EAAAb,KAUAyJ,EAAAG,MAAA,SAAApK,EAAAU,EAAAnB,GAIA,IAHA,IACA8K,EACAC,EAFA3J,EAAApB,EAGAiB,EAAA,EAAAA,EAAAR,EAAAV,SAAAkB,GACA6J,EAAArK,EAAA0B,WAAAlB,IACA,IACAE,EAAAnB,KAAA8K,GACAA,EAAA,KACA3J,EAAAnB,KAAA8K,GAAA,EAAA,KAEA,QAAA,MAAAA,IAAA,QAAA,OAAAC,EAAAtK,EAAA0B,WAAAlB,EAAA,MACA6J,EAAA,QAAA,KAAAA,IAAA,KAAA,KAAAC,KACA9J,EACAE,EAAAnB,KAAA8K,GAAA,GAAA,IACA3J,EAAAnB,KAAA8K,GAAA,GAAA,GAAA,KAIA3J,EAAAnB,KAAA8K,GAAA,GAAA,IAHA3J,EAAAnB,KAAA8K,GAAA,EAAA,GAAA,KANA3J,EAAAnB,KAAA,GAAA8K,EAAA,KAcA,OAAA9K,EAAAoB,0BCtGA5B,EAAAC,QAAAuL,EAEA,IA+DAC,EA/DAC,EAAA,QAsBA,SAAAF,EAAAG,EAAAC,GACAF,EAAA7I,KAAA8I,KACAA,EAAA,mBAAAA,EAAA,SACAC,EAAA,CAAAC,OAAA,CAAAC,OAAA,CAAAD,OAAA,CAAAhM,SAAA,CAAAgM,OAAAD,QAEAJ,EAAAG,GAAAC,EAYAJ,EAAA,MAAA,CAUAO,IAAA,CACAC,OAAA,CACAC,SAAA,CACAC,KAAA,SACAC,GAAA,GAEA9H,MAAA,CACA6H,KAAA,QACAC,GAAA,OAQAX,EAAA,WAAA,CAUAY,SAAAX,EAAA,CACAO,OAAA,CACAK,QAAA,CACAH,KAAA,QACAC,GAAA,GAEAG,MAAA,CACAJ,KAAA,QACAC,GAAA,OAMAX,EAAA,YAAA,CAUAe,UAAAd,IAGAD,EAAA,QAAA,CAOAgB,MAAA,CACAR,OAAA,MAIAR,EAAA,SAAA,CASAiB,OAAA,CACAT,OAAA,CACAA,OAAA,CACAU,QAAA,SACAR,KAAA,QACAC,GAAA,KAkBAQ,MAAA,CACAC,OAAA,CACAC,KAAA,CACAC,MAAA,CACA,YACA,cACA,cACA,YACA,cACA,eAIAd,OAAA,CACAe,UAAA,CACAb,KAAA,YACAC,GAAA,GAEAa,YAAA,CACAd,KAAA,SACAC,GAAA,GAEAc,YAAA,CACAf,KAAA,SACAC,GAAA,GAEAe,UAAA,CACAhB,KAAA,OACAC,GAAA,GAEAgB,YAAA,CACAjB,KAAA,SACAC,GAAA,GAEAiB,UAAA,CACAlB,KAAA,YACAC,GAAA,KAKAkB,UAAA,CACAC,OAAA,CACAC,WAAA,IAWAC,UAAA,CACAxB,OAAA,CACAsB,OAAA,CACAG,KAAA,WACAvB,KAAA,QACAC,GAAA,OAMAX,EAAA,WAAA,CASAkC,YAAA,CACA1B,OAAA,CACA3H,MAAA,CACA6H,KAAA,SACAC,GAAA,KAYAwB,WAAA,CACA3B,OAAA,CACA3H,MAAA,CACA6H,KAAA,QACAC,GAAA,KAYAyB,WAAA,CACA5B,OAAA,CACA3H,MAAA,CACA6H,KAAA,QACAC,GAAA,KAYA0B,YAAA,CACA7B,OAAA,CACA3H,MAAA,CACA6H,KAAA,SACAC,GAAA,KAYA2B,WAAA,CACA9B,OAAA,CACA3H,MAAA,CACA6H,KAAA,QACAC,GAAA,KAYA4B,YAAA,CACA/B,OAAA,CACA3H,MAAA,CACA6H,KAAA,SACAC,GAAA,KAYA6B,UAAA,CACAhC,OAAA,CACA3H,MAAA,CACA6H,KAAA,OACAC,GAAA,KAYA8B,YAAA,CACAjC,OAAA,CACA3H,MAAA,CACA6H,KAAA,SACAC,GAAA,KAYA+B,WAAA,CACAlC,OAAA,CACA3H,MAAA,CACA6H,KAAA,QACAC,GAAA,OAMAX,EAAA,aAAA,CASA2C,UAAA,CACAnC,OAAA,CACAoC,MAAA,CACAX,KAAA,WACAvB,KAAA,SACAC,GAAA,OAqBAX,EAAA6C,IAAA,SAAAC,GACA,OAAA9C,EAAA8C,IAAA,+BCxYA,IAAAC,EAAAtO,EAEAuO,EAAAzO,EAAA,IACA0O,EAAA1O,EAAA,IAWA,SAAA2O,EAAAC,EAAAC,EAAAC,EAAAC,GAEA,GAAAF,EAAAG,aACA,GAAAH,EAAAG,wBAAAP,EAAA,CAAAG,EACA,eAAAG,GACA,IAAA,IAAAxB,EAAAsB,EAAAG,aAAAzB,OAAA3J,EAAAD,OAAAC,KAAA2J,GAAA7L,EAAA,EAAAA,EAAAkC,EAAApD,SAAAkB,EACAmN,EAAAI,UAAA1B,EAAA3J,EAAAlC,MAAAmN,EAAAK,aAAAN,EACA,YACAA,EACA,UAAAhL,EAAAlC,GADAkN,CAEA,WAAArB,EAAA3J,EAAAlC,IAFAkN,CAGA,SAAAG,EAAAxB,EAAA3J,EAAAlC,IAHAkN,CAIA,SACAA,EACA,UACAA,EACA,4BAAAG,EADAH,CAEA,sBAAAC,EAAAM,SAAA,oBAFAP,CAGA,gCAAAG,EAAAD,EAAAC,OACA,CACA,IAAAK,GAAA,EACA,OAAAP,EAAA1C,MACA,IAAA,SACA,IAAA,QAAAyC,EACA,kBAAAG,EAAAA,GACA,MACA,IAAA,SACA,IAAA,UAAAH,EACA,cAAAG,EAAAA,GACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,WAAAH,EACA,YAAAG,EAAAA,GACA,MACA,IAAA,SACAK,GAAA,EAEA,IAAA,QACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAR,EACA,gBADAA,CAEA,6CAAAG,EAAAA,EAAAK,EAFAR,CAGA,iCAAAG,EAHAH,CAIA,uBAAAG,EAAAA,EAJAH,CAKA,iCAAAG,EALAH,CAMA,UAAAG,EAAAA,EANAH,CAOA,iCAAAG,EAPAH,CAQA,+DAAAG,EAAAA,EAAAA,EAAAK,EAAA,OAAA,IACA,MACA,IAAA,QAAAR,EACA,4BAAAG,EADAH,CAEA,wEAAAG,EAAAA,EAAAA,EAFAH,CAGA,sBAAAG,EAHAH,CAIA,UAAAG,EAAAA,GACA,MACA,IAAA,SAAAH,EACA,kBAAAG,EAAAA,GACA,MACA,IAAA,OAAAH,EACA,mBAAAG,EAAAA,IAOA,OAAAH,EAmEA,SAAAS,EAAAT,EAAAC,EAAAC,EAAAC,GAEA,GAAAF,EAAAG,aACAH,EAAAG,wBAAAP,EAAAG,EACA,iDAAAG,EAAAD,EAAAC,EAAAA,GACAH,EACA,gCAAAG,EAAAD,EAAAC,OACA,CACA,IAAAK,GAAA,EACA,OAAAP,EAAA1C,MACA,IAAA,SACA,IAAA,QAAAyC,EACA,6CAAAG,EAAAA,EAAAA,EAAAA,GACA,MACA,IAAA,SACAK,GAAA,EAEA,IAAA,QACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAR,EACA,4BAAAG,EADAH,CAEA,uCAAAG,EAAAA,EAAAA,EAFAH,CAGA,OAHAA,CAIA,4IAAAG,EAAAA,EAAAA,EAAAA,EAAAK,EAAA,OAAA,GAAAL,GACA,MACA,IAAA,QAAAH,EACA,gHAAAG,EAAAA,EAAAA,EAAAA,EAAAA,GACA,MACA,QAAAH,EACA,UAAAG,EAAAA,IAIA,OAAAH,EA5FAJ,EAAAc,WAAA,SAAAC,GAEA,IAAAtD,EAAAsD,EAAAC,YACAZ,EAAAF,EAAA3L,QAAA,CAAA,KAAAwM,EAAA3D,KAAA,cAAA8C,CACA,6BADAA,CAEA,YACA,IAAAzC,EAAAzL,OAAA,OAAAoO,EACA,wBACAA,EACA,uBACA,IAAA,IAAAlN,EAAA,EAAAA,EAAAuK,EAAAzL,SAAAkB,EAAA,CACA,IAAAmN,EAAA5C,EAAAvK,GAAAb,UACAkO,EAAAL,EAAAe,SAAAZ,EAAAjD,MAGAiD,EAAAa,KAAAd,EACA,WAAAG,EADAH,CAEA,4BAAAG,EAFAH,CAGA,sBAAAC,EAAAM,SAAA,oBAHAP,CAIA,SAAAG,EAJAH,CAKA,oDAAAG,GACAJ,EAAAC,EAAAC,EAAAnN,EAAAqN,EAAA,UAAAJ,CACA,IADAA,CAEA,MAGAE,EAAAI,UAAAL,EACA,WAAAG,EADAH,CAEA,0BAAAG,EAFAH,CAGA,sBAAAC,EAAAM,SAAA,mBAHAP,CAIA,SAAAG,EAJAH,CAKA,iCAAAG,GACAJ,EAAAC,EAAAC,EAAAnN,EAAAqN,EAAA,MAAAJ,CACA,IADAA,CAEA,OAIAE,EAAAG,wBAAAP,GAAAG,EACA,iBAAAG,GACAJ,EAAAC,EAAAC,EAAAnN,EAAAqN,GACAF,EAAAG,wBAAAP,GAAAG,EACA,MAEA,OAAAA,EACA,aAwDAJ,EAAAmB,SAAA,SAAAJ,GAEA,IAAAtD,EAAAsD,EAAAC,YAAAjN,QAAAqN,KAAAlB,EAAAmB,mBACA,IAAA5D,EAAAzL,OACA,OAAAkO,EAAA3L,SAAA2L,CAAA,aAUA,IATA,IAAAE,EAAAF,EAAA3L,QAAA,CAAA,IAAA,KAAAwM,EAAA3D,KAAA,YAAA8C,CACA,SADAA,CAEA,OAFAA,CAGA,YAEAoB,EAAA,GACAC,EAAA,GACAC,EAAA,GACAtO,EAAA,EACAA,EAAAuK,EAAAzL,SAAAkB,EACAuK,EAAAvK,GAAAuO,SACAhE,EAAAvK,GAAAb,UAAAoO,SAAAa,EACA7D,EAAAvK,GAAAgO,IAAAK,EACAC,GAAA5N,KAAA6J,EAAAvK,IAEA,GAAAoO,EAAAtP,OAAA,CAEA,IAFAoO,EACA,6BACAlN,EAAA,EAAAA,EAAAoO,EAAAtP,SAAAkB,EAAAkN,EACA,SAAAF,EAAAe,SAAAK,EAAApO,GAAAkK,OACAgD,EACA,KAGA,GAAAmB,EAAAvP,OAAA,CAEA,IAFAoO,EACA,8BACAlN,EAAA,EAAAA,EAAAqO,EAAAvP,SAAAkB,EAAAkN,EACA,SAAAF,EAAAe,SAAAM,EAAArO,GAAAkK,OACAgD,EACA,KAGA,GAAAoB,EAAAxP,OAAA,CAEA,IAFAoO,EACA,mBACAlN,EAAA,EAAAA,EAAAsO,EAAAxP,SAAAkB,EAAA,CACA,IAAAmN,EAAAmB,EAAAtO,GACAqN,EAAAL,EAAAe,SAAAZ,EAAAjD,MACA,GAAAiD,EAAAG,wBAAAP,EAAAG,EACA,6BAAAG,EAAAF,EAAAG,aAAAkB,WAAArB,EAAAK,aAAAL,EAAAK,kBACA,GAAAL,EAAAsB,KAAAvB,EACA,iBADAA,CAEA,gCAAAC,EAAAK,YAAAkB,IAAAvB,EAAAK,YAAAmB,KAAAxB,EAAAK,YAAAoB,SAFA1B,CAGA,oEAAAG,EAHAH,CAIA,QAJAA,CAKA,6BAAAG,EAAAF,EAAAK,YAAA5L,WAAAuL,EAAAK,YAAAqB,iBACA,GAAA1B,EAAA2B,MAAA,CACA,IAAAC,EAAA,IAAAnQ,MAAAwE,UAAAvC,MAAA2I,KAAA2D,EAAAK,aAAA1M,KAAA,KAAA,IACAoM,EACA,6BAAAG,EAAA1M,OAAAC,aAAAtB,MAAAqB,OAAAwM,EAAAK,aADAN,CAEA,QAFAA,CAGA,SAAAG,EAAA0B,EAHA7B,CAIA,6CAAAG,EAAAA,EAJAH,CAKA,UACAA,EACA,SAAAG,EAAAF,EAAAK,aACAN,EACA,KAEA,IAAA8B,GAAA,EACA,IAAAhP,EAAA,EAAAA,EAAAuK,EAAAzL,SAAAkB,EAAA,CACAmN,EAAA5C,EAAAvK,GAAA,IACAhB,EAAA6O,EAAAoB,EAAAC,QAAA/B,GACAE,EAAAL,EAAAe,SAAAZ,EAAAjD,MACAiD,EAAAa,KACAgB,IAAAA,GAAA,EAAA9B,EACA,YACAA,EACA,0CAAAG,EAAAA,EADAH,CAEA,SAAAG,EAFAH,CAGA,kCACAS,EAAAT,EAAAC,EAAAnO,EAAAqO,EAAA,WAAAM,CACA,MACAR,EAAAI,UAAAL,EACA,uBAAAG,EAAAA,EADAH,CAEA,SAAAG,EAFAH,CAGA,iCAAAG,GACAM,EAAAT,EAAAC,EAAAnO,EAAAqO,EAAA,MAAAM,CACA,OACAT,EACA,uCAAAG,EAAAF,EAAAjD,MACAyD,EAAAT,EAAAC,EAAAnO,EAAAqO,GACAF,EAAAoB,QAAArB,EACA,eADAA,CAEA,SAAAF,EAAAe,SAAAZ,EAAAoB,OAAArE,MAAAiD,EAAAjD,OAEAgD,EACA,KAEA,OAAAA,EACA,+CCjSA3O,EAAAC,QAeA,SAAAqP,GAEA,IAAAX,EAAAF,EAAA3L,QAAA,CAAA,IAAA,KAAAwM,EAAA3D,KAAA,UAAA8C,CACA,6BADAA,CAEA,qBAFAA,CAGA,qDAAAa,EAAAC,YAAAqB,OAAA,SAAAhC,GAAA,OAAAA,EAAAa,MAAAlP,OAAA,KAAA,IAHAkO,CAIA,kBAJAA,CAKA,oBACAa,EAAAuB,OAAAlC,EACA,gBADAA,CAEA,SACAA,EACA,kBAGA,IADA,IAAAlN,EAAA,EACAA,EAAA6N,EAAAC,YAAAhP,SAAAkB,EAAA,CACA,IAAAmN,EAAAU,EAAAoB,EAAAjP,GAAAb,UACAsL,EAAA0C,EAAAG,wBAAAP,EAAA,QAAAI,EAAA1C,KACA4E,EAAA,IAAArC,EAAAe,SAAAZ,EAAAjD,MAAAgD,EACA,WAAAC,EAAAzC,IAGAyC,EAAAa,KAAAd,EACA,iBADAA,CAEA,4BAAAmC,EAFAnC,CAGA,QAAAmC,EAHAnC,CAIA,WAAAC,EAAAlC,QAJAiC,CAKA,WACAoC,EAAAb,KAAAtB,EAAAlC,WAAAjN,GACAsR,EAAAC,MAAA9E,KAAAzM,GAAAkP,EACA,8EAAAmC,EAAArP,GACAkN,EACA,sDAAAmC,EAAA5E,GAEA6E,EAAAC,MAAA9E,KAAAzM,GAAAkP,EACA,uCAAAmC,EAAArP,GACAkN,EACA,eAAAmC,EAAA5E,IAIA0C,EAAAI,UAAAL,EAEA,uBAAAmC,EAAAA,EAFAnC,CAGA,QAAAmC,GAGAC,EAAAE,OAAA/E,KAAAzM,IAAAkP,EACA,iBADAA,CAEA,0BAFAA,CAGA,kBAHAA,CAIA,kBAAAmC,EAAA5E,EAJAyC,CAKA,SAGAoC,EAAAC,MAAA9E,KAAAzM,GAAAkP,EAAAC,EAAAG,aAAA8B,MACA,+BACA,0CAAAC,EAAArP,GACAkN,EACA,kBAAAmC,EAAA5E,IAGA6E,EAAAC,MAAA9E,KAAAzM,GAAAkP,EAAAC,EAAAG,aAAA8B,MACA,yBACA,oCAAAC,EAAArP,GACAkN,EACA,YAAAmC,EAAA5E,GACAyC,EACA,SAWA,IATAA,EACA,WADAA,CAEA,kBAFAA,CAGA,QAHAA,CAKA,IALAA,CAMA,KAGAlN,EAAA,EAAAA,EAAA6N,EAAAoB,EAAAnQ,SAAAkB,EAAA,CACA,IAAAyP,EAAA5B,EAAAoB,EAAAjP,GACAyP,EAAAC,UAAAxC,EACA,4BAAAuC,EAAAvF,KADAgD,CAEA,4CA3FA,qBA2FAuC,EA3FAvF,KAAA,KA8FA,OAAAgD,EACA,aApGA,IAAAH,EAAAzO,EAAA,IACAgR,EAAAhR,EAAA,IACA0O,EAAA1O,EAAA,4CCJAC,EAAAC,QA0BA,SAAAqP,GAWA,IATA,IAIAwB,EAJAnC,EAAAF,EAAA3L,QAAA,CAAA,IAAA,KAAAwM,EAAA3D,KAAA,UAAA8C,CACA,SADAA,CAEA,qBAKAzC,EAAAsD,EAAAC,YAAAjN,QAAAqN,KAAAlB,EAAAmB,mBAEAnO,EAAA,EAAAA,EAAAuK,EAAAzL,SAAAkB,EAAA,CACA,IAAAmN,EAAA5C,EAAAvK,GAAAb,UACAH,EAAA6O,EAAAoB,EAAAC,QAAA/B,GACA1C,EAAA0C,EAAAG,wBAAAP,EAAA,QAAAI,EAAA1C,KACAkF,EAAAL,EAAAC,MAAA9E,GACA4E,EAAA,IAAArC,EAAAe,SAAAZ,EAAAjD,MAGAiD,EAAAa,KACAd,EACA,sCAAAmC,EAAAlC,EAAAjD,KADAgD,CAEA,mDAAAmC,EAFAnC,CAGA,4CAAAC,EAAAzC,IAAA,EAAA,KAAA,EAAA,EAAA4E,EAAAM,OAAAzC,EAAAlC,SAAAkC,EAAAlC,SACA0E,IAAA3R,GAAAkP,EACA,oEAAAlO,EAAAqQ,GACAnC,EACA,qCAAA,GAAAyC,EAAAlF,EAAA4E,GACAnC,EACA,IADAA,CAEA,MAGAC,EAAAI,UAAAL,EACA,2BAAAmC,EAAAA,GAGAlC,EAAAqC,QAAAF,EAAAE,OAAA/E,KAAAzM,GAAAkP,EAEA,uBAAAC,EAAAzC,IAAA,EAAA,KAAA,EAFAwC,CAGA,+BAAAmC,EAHAnC,CAIA,cAAAzC,EAAA4E,EAJAnC,CAKA,eAGAA,EAEA,+BAAAmC,GACAM,IAAA3R,GACA6R,EAAA3C,EAAAC,EAAAnO,EAAAqQ,EAAA,OACAnC,EACA,0BAAAC,EAAAzC,IAAA,EAAAiF,KAAA,EAAAlF,EAAA4E,IAEAnC,EACA,OAIAC,EAAA2C,UAAA5C,EACA,qCAAAmC,EAAAlC,EAAAjD,MAEAyF,IAAA3R,GACA6R,EAAA3C,EAAAC,EAAAnO,EAAAqQ,GACAnC,EACA,uBAAAC,EAAAzC,IAAA,EAAAiF,KAAA,EAAAlF,EAAA4E,IAKA,OAAAnC,EACA,aA9FA,IAAAH,EAAAzO,EAAA,IACAgR,EAAAhR,EAAA,IACA0O,EAAA1O,EAAA,IAWA,SAAAuR,EAAA3C,EAAAC,EAAAC,EAAAiC,GACA,OAAAlC,EAAAG,aAAA8B,MACAlC,EAAA,+CAAAE,EAAAiC,GAAAlC,EAAAzC,IAAA,EAAA,KAAA,GAAAyC,EAAAzC,IAAA,EAAA,KAAA,GACAwC,EAAA,oDAAAE,EAAAiC,GAAAlC,EAAAzC,IAAA,EAAA,KAAA,4CClBAnM,EAAAC,QAAAuO,EAGA,IAAAgD,EAAAzR,EAAA,MACAyO,EAAA3J,UAAAnB,OAAA+N,OAAAD,EAAA3M,YAAA6M,YAAAlD,GAAAmD,UAAA,OAEA,IAAAC,EAAA7R,EAAA,IACA0O,EAAA1O,EAAA,IAaA,SAAAyO,EAAA7C,EAAA2B,EAAA5H,EAAAmM,EAAAC,GAGA,GAFAN,EAAAvG,KAAAtG,KAAAgH,EAAAjG,GAEA4H,GAAA,iBAAAA,EACA,MAAAyE,UAAA,4BAoCA,GA9BApN,KAAAsL,WAAA,GAMAtL,KAAA2I,OAAA5J,OAAA+N,OAAA9M,KAAAsL,YAMAtL,KAAAkN,QAAAA,EAMAlN,KAAAmN,SAAAA,GAAA,GAMAnN,KAAAqN,SAAAvS,GAMA6N,EACA,IAAA,IAAA3J,EAAAD,OAAAC,KAAA2J,GAAA7L,EAAA,EAAAA,EAAAkC,EAAApD,SAAAkB,EACA,iBAAA6L,EAAA3J,EAAAlC,MACAkD,KAAAsL,WAAAtL,KAAA2I,OAAA3J,EAAAlC,IAAA6L,EAAA3J,EAAAlC,KAAAkC,EAAAlC,IAiBA+M,EAAAyD,SAAA,SAAAtG,EAAAC,GACA,IAAAsG,EAAA,IAAA1D,EAAA7C,EAAAC,EAAA0B,OAAA1B,EAAAlG,QAAAkG,EAAAiG,QAAAjG,EAAAkG,UAEA,OADAI,EAAAF,SAAApG,EAAAoG,SACAE,GAQA1D,EAAA3J,UAAAsN,OAAA,SAAAC,GACA,IAAAC,IAAAD,KAAAA,EAAAC,aACA,OAAA5D,EAAAiB,SAAA,CACA,UAAA/K,KAAAe,QACA,SAAAf,KAAA2I,OACA,WAAA3I,KAAAqN,UAAArN,KAAAqN,SAAAzR,OAAAoE,KAAAqN,SAAAvS,GACA,UAAA4S,EAAA1N,KAAAkN,QAAApS,GACA,WAAA4S,EAAA1N,KAAAmN,SAAArS,MAaA+O,EAAA3J,UAAAyN,IAAA,SAAA3G,EAAAQ,EAAA0F,GAGA,IAAApD,EAAA8D,SAAA5G,GACA,MAAAoG,UAAA,yBAEA,IAAAtD,EAAA+D,UAAArG,GACA,MAAA4F,UAAA,yBAEA,GAAApN,KAAA2I,OAAA3B,KAAAlM,GACA,MAAAmD,MAAA,mBAAA+I,EAAA,QAAAhH,MAEA,GAAAA,KAAA8N,aAAAtG,GACA,MAAAvJ,MAAA,MAAAuJ,EAAA,mBAAAxH,MAEA,GAAAA,KAAA+N,eAAA/G,GACA,MAAA/I,MAAA,SAAA+I,EAAA,oBAAAhH,MAEA,GAAAA,KAAAsL,WAAA9D,KAAA1M,GAAA,CACA,IAAAkF,KAAAe,UAAAf,KAAAe,QAAAiN,YACA,MAAA/P,MAAA,gBAAAuJ,EAAA,OAAAxH,MACAA,KAAA2I,OAAA3B,GAAAQ,OAEAxH,KAAAsL,WAAAtL,KAAA2I,OAAA3B,GAAAQ,GAAAR,EAGA,OADAhH,KAAAmN,SAAAnG,GAAAkG,GAAA,KACAlN,MAUA6J,EAAA3J,UAAA+N,OAAA,SAAAjH,GAEA,IAAA8C,EAAA8D,SAAA5G,GACA,MAAAoG,UAAA,yBAEA,IAAA9K,EAAAtC,KAAA2I,OAAA3B,GACA,GAAA,MAAA1E,EACA,MAAArE,MAAA,SAAA+I,EAAA,uBAAAhH,MAMA,cAJAA,KAAAsL,WAAAhJ,UACAtC,KAAA2I,OAAA3B,UACAhH,KAAAmN,SAAAnG,GAEAhH,MAQA6J,EAAA3J,UAAA4N,aAAA,SAAAtG,GACA,OAAAyF,EAAAa,aAAA9N,KAAAqN,SAAA7F,IAQAqC,EAAA3J,UAAA6N,eAAA,SAAA/G,GACA,OAAAiG,EAAAc,eAAA/N,KAAAqN,SAAArG,4CClLA3L,EAAAC,QAAA4S,EAGA,IAAArB,EAAAzR,EAAA,MACA8S,EAAAhO,UAAAnB,OAAA+N,OAAAD,EAAA3M,YAAA6M,YAAAmB,GAAAlB,UAAA,QAEA,IAIAmB,EAJAtE,EAAAzO,EAAA,IACAgR,EAAAhR,EAAA,IACA0O,EAAA1O,EAAA,IAIAgT,EAAA,+BAyCA,SAAAF,EAAAlH,EAAAQ,EAAAD,EAAAuB,EAAAuF,EAAAtN,EAAAmM,GAcA,GAZApD,EAAAwE,SAAAxF,IACAoE,EAAAmB,EACAtN,EAAA+H,EACAA,EAAAuF,EAAAvT,IACAgP,EAAAwE,SAAAD,KACAnB,EAAAnM,EACAA,EAAAsN,EACAA,EAAAvT,IAGA+R,EAAAvG,KAAAtG,KAAAgH,EAAAjG,IAEA+I,EAAA+D,UAAArG,IAAAA,EAAA,EACA,MAAA4F,UAAA,qCAEA,IAAAtD,EAAA8D,SAAArG,GACA,MAAA6F,UAAA,yBAEA,GAAAtE,IAAAhO,KAAAsT,EAAAlQ,KAAA4K,EAAAA,EAAApK,WAAA6P,eACA,MAAAnB,UAAA,8BAEA,GAAAiB,IAAAvT,KAAAgP,EAAA8D,SAAAS,GACA,MAAAjB,UAAA,2BAMApN,KAAA8I,KAAAA,GAAA,aAAAA,EAAAA,EAAAhO,GAMAkF,KAAAuH,KAAAA,EAMAvH,KAAAwH,GAAAA,EAMAxH,KAAAqO,OAAAA,GAAAvT,GAMAkF,KAAAwM,SAAA,aAAA1D,EAMA9I,KAAA4M,UAAA5M,KAAAwM,SAMAxM,KAAAqK,SAAA,aAAAvB,EAMA9I,KAAA8K,KAAA,EAMA9K,KAAAwO,QAAA,KAMAxO,KAAAqL,OAAA,KAMArL,KAAAsK,YAAA,KAMAtK,KAAAyO,aAAA,KAMAzO,KAAAuL,OAAAzB,EAAA4E,MAAAtC,EAAAb,KAAAhE,KAAAzM,GAMAkF,KAAA4L,MAAA,UAAArE,EAMAvH,KAAAoK,aAAA,KAMApK,KAAA2O,eAAA,KAMA3O,KAAA4O,eAAA,KAOA5O,KAAA6O,EAAA,KAMA7O,KAAAkN,QAAAA,EA7JAgB,EAAAZ,SAAA,SAAAtG,EAAAC,GACA,OAAA,IAAAiH,EAAAlH,EAAAC,EAAAO,GAAAP,EAAAM,KAAAN,EAAA6B,KAAA7B,EAAAoH,OAAApH,EAAAlG,QAAAkG,EAAAiG,UAqKAnO,OAAA+P,eAAAZ,EAAAhO,UAAA,SAAA,CACAwJ,IAAA,WAIA,OAFA,OAAA1J,KAAA6O,IACA7O,KAAA6O,GAAA,IAAA7O,KAAA+O,UAAA,WACA/O,KAAA6O,KAOAX,EAAAhO,UAAA8O,UAAA,SAAAhI,EAAAtH,EAAAuP,GAGA,MAFA,WAAAjI,IACAhH,KAAA6O,EAAA,MACAhC,EAAA3M,UAAA8O,UAAA1I,KAAAtG,KAAAgH,EAAAtH,EAAAuP,IAwBAf,EAAAhO,UAAAsN,OAAA,SAAAC,GACA,IAAAC,IAAAD,KAAAA,EAAAC,aACA,OAAA5D,EAAAiB,SAAA,CACA,OAAA,aAAA/K,KAAA8I,MAAA9I,KAAA8I,MAAAhO,GACA,OAAAkF,KAAAuH,KACA,KAAAvH,KAAAwH,GACA,SAAAxH,KAAAqO,OACA,UAAArO,KAAAe,QACA,UAAA2M,EAAA1N,KAAAkN,QAAApS,MASAoT,EAAAhO,UAAAjE,QAAA,WAEA,GAAA+D,KAAAkP,SACA,OAAAlP,KA0BA,IAxBAA,KAAAsK,YAAA8B,EAAA+C,SAAAnP,KAAAuH,SAAAzM,KACAkF,KAAAoK,cAAApK,KAAA4O,eAAA5O,KAAA4O,eAAAQ,OAAApP,KAAAoP,QAAAC,iBAAArP,KAAAuH,MACAvH,KAAAoK,wBAAA+D,EACAnO,KAAAsK,YAAA,KAEAtK,KAAAsK,YAAAtK,KAAAoK,aAAAzB,OAAA5J,OAAAC,KAAAgB,KAAAoK,aAAAzB,QAAA,KAIA3I,KAAAe,SAAA,MAAAf,KAAAe,QAAA,UACAf,KAAAsK,YAAAtK,KAAAe,QAAA,QACAf,KAAAoK,wBAAAP,GAAA,iBAAA7J,KAAAsK,cACAtK,KAAAsK,YAAAtK,KAAAoK,aAAAzB,OAAA3I,KAAAsK,eAIAtK,KAAAe,WACA,IAAAf,KAAAe,QAAAuL,SAAAtM,KAAAe,QAAAuL,SAAAxR,KAAAkF,KAAAoK,cAAApK,KAAAoK,wBAAAP,WACA7J,KAAAe,QAAAuL,OACAvN,OAAAC,KAAAgB,KAAAe,SAAAnF,SACAoE,KAAAe,QAAAjG,KAIAkF,KAAAuL,KACAvL,KAAAsK,YAAAR,EAAA4E,KAAAY,WAAAtP,KAAAsK,YAAA,MAAAtK,KAAAuH,KAAA9K,OAAA,IAGAsC,OAAAwQ,QACAxQ,OAAAwQ,OAAAvP,KAAAsK,kBAEA,GAAAtK,KAAA4L,OAAA,iBAAA5L,KAAAsK,YAAA,CACA,IAAA/H,EACAuH,EAAAzN,OAAA6B,KAAA8B,KAAAsK,aACAR,EAAAzN,OAAAyB,OAAAkC,KAAAsK,YAAA/H,EAAAuH,EAAA0F,UAAA1F,EAAAzN,OAAAT,OAAAoE,KAAAsK,cAAA,GAEAR,EAAAvD,KAAAG,MAAA1G,KAAAsK,YAAA/H,EAAAuH,EAAA0F,UAAA1F,EAAAvD,KAAA3K,OAAAoE,KAAAsK,cAAA,GACAtK,KAAAsK,YAAA/H,EAeA,OAXAvC,KAAA8K,IACA9K,KAAAyO,aAAA3E,EAAA2F,YACAzP,KAAAqK,SACArK,KAAAyO,aAAA3E,EAAA4F,WAEA1P,KAAAyO,aAAAzO,KAAAsK,YAGAtK,KAAAoP,kBAAAjB,IACAnO,KAAAoP,OAAAO,KAAAzP,UAAAF,KAAAgH,MAAAhH,KAAAyO,cAEA5B,EAAA3M,UAAAjE,QAAAqK,KAAAtG,OAuBAkO,EAAA0B,EAAA,SAAAC,EAAAC,EAAAC,EAAAtB,GAUA,MAPA,mBAAAqB,EACAA,EAAAhG,EAAAkG,aAAAF,GAAA9I,KAGA8I,GAAA,iBAAAA,IACAA,EAAAhG,EAAAmG,aAAAH,GAAA9I,MAEA,SAAA9G,EAAAgQ,GACApG,EAAAkG,aAAA9P,EAAA6M,aACAY,IAAA,IAAAO,EAAAgC,EAAAL,EAAAC,EAAAC,EAAA,CAAAI,QAAA1B,OAkBAP,EAAAkC,EAAA,SAAAC,GACAlC,EAAAkC,iDChXA,IAAAnV,EAAAG,EAAAC,QAAAF,EAAA,IAEAF,EAAAoV,MAAA,QAoDApV,EAAAqV,KAjCA,SAAAzP,EAAA0P,EAAAxP,GAMA,MALA,mBAAAwP,GACAxP,EAAAwP,EACAA,EAAA,IAAAtV,EAAAuV,MACAD,IACAA,EAAA,IAAAtV,EAAAuV,MACAD,EAAAD,KAAAzP,EAAAE,IA2CA9F,EAAAwV,SANA,SAAA5P,EAAA0P,GAGA,OAFAA,IACAA,EAAA,IAAAtV,EAAAuV,MACAD,EAAAE,SAAA5P,IAMA5F,EAAAyV,QAAAvV,EAAA,IACAF,EAAA0V,QAAAxV,EAAA,IACAF,EAAA2V,SAAAzV,EAAA,IACAF,EAAA0O,UAAAxO,EAAA,IAGAF,EAAA2R,iBAAAzR,EAAA,IACAF,EAAA+R,UAAA7R,EAAA,IACAF,EAAAuV,KAAArV,EAAA,IACAF,EAAA2O,KAAAzO,EAAA,IACAF,EAAAiT,KAAA/S,EAAA,IACAF,EAAAgT,MAAA9S,EAAA,IACAF,EAAA4V,MAAA1V,EAAA,IACAF,EAAA6V,SAAA3V,EAAA,IACAF,EAAA8V,QAAA5V,EAAA,IACAF,EAAA+V,OAAA7V,EAAA,IAGAF,EAAAgW,QAAA9V,EAAA,IACAF,EAAAiW,SAAA/V,EAAA,IAGAF,EAAAkR,MAAAhR,EAAA,IACAF,EAAA4O,KAAA1O,EAAA,IAGAF,EAAA2R,iBAAAuD,EAAAlV,EAAAuV,MACAvV,EAAA+R,UAAAmD,EAAAlV,EAAAiT,KAAAjT,EAAA8V,QAAA9V,EAAA2O,MACA3O,EAAAuV,KAAAL,EAAAlV,EAAAiT,MACAjT,EAAAgT,MAAAkC,EAAAlV,EAAAiT,gJCtGA,IAAAjT,EAAAI,EA2BA,SAAA8V,IACAlW,EAAAmW,OAAAjB,EAAAlV,EAAAoW,cACApW,EAAA4O,KAAAsG,IArBAlV,EAAAoV,MAAA,UAGApV,EAAAqW,OAAAnW,EAAA,IACAF,EAAAsW,aAAApW,EAAA,IACAF,EAAAmW,OAAAjW,EAAA,IACAF,EAAAoW,aAAAlW,EAAA,IAGAF,EAAA4O,KAAA1O,EAAA,IACAF,EAAAuW,IAAArW,EAAA,IACAF,EAAAwW,MAAAtW,EAAA,IACAF,EAAAkW,UAAAA,EAaAlW,EAAAqW,OAAAnB,EAAAlV,EAAAsW,cACAJ,oEClCA,IAAAlW,EAAAG,EAAAC,QAAAF,EAAA,IAEAF,EAAAoV,MAAA,OAGApV,EAAAyW,SAAAvW,EAAA,IACAF,EAAA0W,MAAAxW,EAAA,IACAF,EAAA2L,OAAAzL,EAAA,IAGAF,EAAAuV,KAAAL,EAAAlV,EAAAiT,KAAAjT,EAAA0W,MAAA1W,EAAA2L,sDCVAxL,EAAAC,QAAAyV,EAGA,IAAA7C,EAAA9S,EAAA,MACA2V,EAAA7Q,UAAAnB,OAAA+N,OAAAoB,EAAAhO,YAAA6M,YAAAgE,GAAA/D,UAAA,WAEA,IAAAZ,EAAAhR,EAAA,IACA0O,EAAA1O,EAAA,IAcA,SAAA2V,EAAA/J,EAAAQ,EAAAO,EAAAR,EAAAxG,EAAAmM,GAIA,GAHAgB,EAAA5H,KAAAtG,KAAAgH,EAAAQ,EAAAD,EAAAzM,GAAAA,GAAAiG,EAAAmM,IAGApD,EAAA8D,SAAA7F,GACA,MAAAqF,UAAA,4BAMApN,KAAA+H,QAAAA,EAMA/H,KAAA6R,gBAAA,KAGA7R,KAAA8K,KAAA,EAwBAiG,EAAAzD,SAAA,SAAAtG,EAAAC,GACA,OAAA,IAAA8J,EAAA/J,EAAAC,EAAAO,GAAAP,EAAAc,QAAAd,EAAAM,KAAAN,EAAAlG,QAAAkG,EAAAiG,UAQA6D,EAAA7Q,UAAAsN,OAAA,SAAAC,GACA,IAAAC,IAAAD,KAAAA,EAAAC,aACA,OAAA5D,EAAAiB,SAAA,CACA,UAAA/K,KAAA+H,QACA,OAAA/H,KAAAuH,KACA,KAAAvH,KAAAwH,GACA,SAAAxH,KAAAqO,OACA,UAAArO,KAAAe,QACA,UAAA2M,EAAA1N,KAAAkN,QAAApS,MAOAiW,EAAA7Q,UAAAjE,QAAA,WACA,GAAA+D,KAAAkP,SACA,OAAAlP,KAGA,GAAAoM,EAAAM,OAAA1M,KAAA+H,WAAAjN,GACA,MAAAmD,MAAA,qBAAA+B,KAAA+H,SAEA,OAAAmG,EAAAhO,UAAAjE,QAAAqK,KAAAtG,OAaA+Q,EAAAnB,EAAA,SAAAC,EAAAiC,EAAAC,GAUA,MAPA,mBAAAA,EACAA,EAAAjI,EAAAkG,aAAA+B,GAAA/K,KAGA+K,GAAA,iBAAAA,IACAA,EAAAjI,EAAAmG,aAAA8B,GAAA/K,MAEA,SAAA9G,EAAAgQ,GACApG,EAAAkG,aAAA9P,EAAA6M,aACAY,IAAA,IAAAoD,EAAAb,EAAAL,EAAAiC,EAAAC,8CC1HA1W,EAAAC,QAAA4V,EAEA,IAAApH,EAAA1O,EAAA,IASA,SAAA8V,EAAAc,GAEA,GAAAA,EACA,IAAA,IAAAhT,EAAAD,OAAAC,KAAAgT,GAAAlV,EAAA,EAAAA,EAAAkC,EAAApD,SAAAkB,EACAkD,KAAAhB,EAAAlC,IAAAkV,EAAAhT,EAAAlC,IA0BAoU,EAAApE,OAAA,SAAAkF,GACA,OAAAhS,KAAAiS,MAAAnF,OAAAkF,IAWAd,EAAAnU,OAAA,SAAAyR,EAAA0D,GACA,OAAAlS,KAAAiS,MAAAlV,OAAAyR,EAAA0D,IAWAhB,EAAAiB,gBAAA,SAAA3D,EAAA0D,GACA,OAAAlS,KAAAiS,MAAAE,gBAAA3D,EAAA0D,IAYAhB,EAAApT,OAAA,SAAAsU,GACA,OAAApS,KAAAiS,MAAAnU,OAAAsU,IAYAlB,EAAAmB,gBAAA,SAAAD,GACA,OAAApS,KAAAiS,MAAAI,gBAAAD,IAUAlB,EAAAoB,OAAA,SAAA9D,GACA,OAAAxO,KAAAiS,MAAAK,OAAA9D,IAUA0C,EAAAxG,WAAA,SAAA6H,GACA,OAAAvS,KAAAiS,MAAAvH,WAAA6H,IAWArB,EAAAnG,SAAA,SAAAyD,EAAAzN,GACA,OAAAf,KAAAiS,MAAAlH,SAAAyD,EAAAzN,IAOAmQ,EAAAhR,UAAAsN,OAAA,WACA,OAAAxN,KAAAiS,MAAAlH,SAAA/K,KAAA8J,EAAA2D,4CCtIApS,EAAAC,QAAA2V,EAGA,IAAApE,EAAAzR,EAAA,MACA6V,EAAA/Q,UAAAnB,OAAA+N,OAAAD,EAAA3M,YAAA6M,YAAAkE,GAAAjE,UAAA,SAEA,IAAAlD,EAAA1O,EAAA,IAgBA,SAAA6V,EAAAjK,EAAAO,EAAAiL,EAAA3Q,EAAA4Q,EAAAC,EAAA3R,EAAAmM,GAYA,GATApD,EAAAwE,SAAAmE,IACA1R,EAAA0R,EACAA,EAAAC,EAAA5X,IACAgP,EAAAwE,SAAAoE,KACA3R,EAAA2R,EACAA,EAAA5X,IAIAyM,IAAAzM,KAAAgP,EAAA8D,SAAArG,GACA,MAAA6F,UAAA,yBAGA,IAAAtD,EAAA8D,SAAA4E,GACA,MAAApF,UAAA,gCAGA,IAAAtD,EAAA8D,SAAA/L,GACA,MAAAuL,UAAA,iCAEAP,EAAAvG,KAAAtG,KAAAgH,EAAAjG,GAMAf,KAAAuH,KAAAA,GAAA,MAMAvH,KAAAwS,YAAAA,EAMAxS,KAAAyS,gBAAAA,GAAA3X,GAMAkF,KAAA6B,aAAAA,EAMA7B,KAAA0S,iBAAAA,GAAA5X,GAMAkF,KAAA2S,oBAAA,KAMA3S,KAAA4S,qBAAA,KAMA5S,KAAAkN,QAAAA,EAqBA+D,EAAA3D,SAAA,SAAAtG,EAAAC,GACA,OAAA,IAAAgK,EAAAjK,EAAAC,EAAAM,KAAAN,EAAAuL,YAAAvL,EAAApF,aAAAoF,EAAAwL,cAAAxL,EAAAyL,eAAAzL,EAAAlG,QAAAkG,EAAAiG,UAQA+D,EAAA/Q,UAAAsN,OAAA,SAAAC,GACA,IAAAC,IAAAD,KAAAA,EAAAC,aACA,OAAA5D,EAAAiB,SAAA,CACA,OAAA,QAAA/K,KAAAuH,MAAAvH,KAAAuH,MAAAzM,GACA,cAAAkF,KAAAwS,YACA,gBAAAxS,KAAAyS,cACA,eAAAzS,KAAA6B,aACA,iBAAA7B,KAAA0S,eACA,UAAA1S,KAAAe,QACA,UAAA2M,EAAA1N,KAAAkN,QAAApS,MAOAmW,EAAA/Q,UAAAjE,QAAA,WAGA,OAAA+D,KAAAkP,SACAlP,MAEAA,KAAA2S,oBAAA3S,KAAAoP,OAAAyD,WAAA7S,KAAAwS,aACAxS,KAAA4S,qBAAA5S,KAAAoP,OAAAyD,WAAA7S,KAAA6B,cAEAgL,EAAA3M,UAAAjE,QAAAqK,KAAAtG,0CCpJA3E,EAAAC,QAAA2R,EAGA,IAAAJ,EAAAzR,EAAA,MACA6R,EAAA/M,UAAAnB,OAAA+N,OAAAD,EAAA3M,YAAA6M,YAAAE,GAAAD,UAAA,YAEA,IAGAmB,EACA6C,EACAnH,EALAqE,EAAA9S,EAAA,IACA0O,EAAA1O,EAAA,IAoCA,SAAA0X,EAAAC,EAAAtF,GACA,IAAAsF,IAAAA,EAAAnX,OACA,OAAAd,GAEA,IADA,IAAAkY,EAAA,GACAlW,EAAA,EAAAA,EAAAiW,EAAAnX,SAAAkB,EACAkW,EAAAD,EAAAjW,GAAAkK,MAAA+L,EAAAjW,GAAA0Q,OAAAC,GACA,OAAAuF,EA4CA,SAAA/F,EAAAjG,EAAAjG,GACA8L,EAAAvG,KAAAtG,KAAAgH,EAAAjG,GAMAf,KAAAkH,OAAApM,GAOAkF,KAAAiT,EAAA,KAGA,SAAAC,EAAAC,GAEA,OADAA,EAAAF,EAAA,KACAE,EAhFAlG,EAAAK,SAAA,SAAAtG,EAAAC,GACA,OAAA,IAAAgG,EAAAjG,EAAAC,EAAAlG,SAAAqS,QAAAnM,EAAAC,SAmBA+F,EAAA6F,YAAAA,EAQA7F,EAAAa,aAAA,SAAAT,EAAA7F,GACA,GAAA6F,EACA,IAAA,IAAAvQ,EAAA,EAAAA,EAAAuQ,EAAAzR,SAAAkB,EACA,GAAA,iBAAAuQ,EAAAvQ,IAAAuQ,EAAAvQ,GAAA,IAAA0K,GAAA6F,EAAAvQ,GAAA,IAAA0K,EACA,OAAA,EACA,OAAA,GASAyF,EAAAc,eAAA,SAAAV,EAAArG,GACA,GAAAqG,EACA,IAAA,IAAAvQ,EAAA,EAAAA,EAAAuQ,EAAAzR,SAAAkB,EACA,GAAAuQ,EAAAvQ,KAAAkK,EACA,OAAA,EACA,OAAA,GA0CAjI,OAAA+P,eAAA7B,EAAA/M,UAAA,cAAA,CACAwJ,IAAA,WACA,OAAA1J,KAAAiT,IAAAjT,KAAAiT,EAAAnJ,EAAAuJ,QAAArT,KAAAkH,YA6BA+F,EAAA/M,UAAAsN,OAAA,SAAAC,GACA,OAAA3D,EAAAiB,SAAA,CACA,UAAA/K,KAAAe,QACA,SAAA+R,EAAA9S,KAAAsT,YAAA7F,MASAR,EAAA/M,UAAAkT,QAAA,SAAAG,GAGA,GAAAA,EACA,IAAA,IAAArM,EAAAsM,EAAAzU,OAAAC,KAAAuU,GAAAzW,EAAA,EAAAA,EAAA0W,EAAA5X,SAAAkB,EACAoK,EAAAqM,EAAAC,EAAA1W,IAJAkD,KAKA2N,KACAzG,EAAAG,SAAAvM,GACAqT,EAAAb,SACApG,EAAAyB,SAAA7N,GACA+O,EAAAyD,SACApG,EAAAuM,UAAA3Y,GACAkW,EAAA1D,SACApG,EAAAM,KAAA1M,GACAoT,EAAAZ,SACAL,EAAAK,UAAAkG,EAAA1W,GAAAoK,IAIA,OAAAlH,MAQAiN,EAAA/M,UAAAwJ,IAAA,SAAA1C,GACA,OAAAhH,KAAAkH,QAAAlH,KAAAkH,OAAAF,IACA,MAUAiG,EAAA/M,UAAAwT,QAAA,SAAA1M,GACA,GAAAhH,KAAAkH,QAAAlH,KAAAkH,OAAAF,aAAA6C,EACA,OAAA7J,KAAAkH,OAAAF,GAAA2B,OACA,MAAA1K,MAAA,iBAAA+I,IAUAiG,EAAA/M,UAAAyN,IAAA,SAAA4E,GAEA,KAAAA,aAAArE,GAAAqE,EAAAlE,SAAAvT,IAAAyX,aAAApE,GAAAoE,aAAA1I,GAAA0I,aAAAvB,GAAAuB,aAAAtF,GACA,MAAAG,UAAA,wCAEA,GAAApN,KAAAkH,OAEA,CACA,IAAAyM,EAAA3T,KAAA0J,IAAA6I,EAAAvL,MACA,GAAA2M,EAAA,CACA,KAAAA,aAAA1G,GAAAsF,aAAAtF,IAAA0G,aAAAxF,GAAAwF,aAAA3C,EAWA,MAAA/S,MAAA,mBAAAsU,EAAAvL,KAAA,QAAAhH,MARA,IADA,IAAAkH,EAAAyM,EAAAL,YACAxW,EAAA,EAAAA,EAAAoK,EAAAtL,SAAAkB,EACAyV,EAAA5E,IAAAzG,EAAApK,IACAkD,KAAAiO,OAAA0F,GACA3T,KAAAkH,SACAlH,KAAAkH,OAAA,IACAqL,EAAAqB,WAAAD,EAAA5S,SAAA,SAZAf,KAAAkH,OAAA,GAoBA,OAFAlH,KAAAkH,OAAAqL,EAAAvL,MAAAuL,GACAsB,MAAA7T,MACAkT,EAAAlT,OAUAiN,EAAA/M,UAAA+N,OAAA,SAAAsE,GAEA,KAAAA,aAAA1F,GACA,MAAAO,UAAA,qCACA,GAAAmF,EAAAnD,SAAApP,KACA,MAAA/B,MAAAsU,EAAA,uBAAAvS,MAOA,cALAA,KAAAkH,OAAAqL,EAAAvL,MACAjI,OAAAC,KAAAgB,KAAAkH,QAAAtL,SACAoE,KAAAkH,OAAApM,IAEAyX,EAAAuB,SAAA9T,MACAkT,EAAAlT,OASAiN,EAAA/M,UAAA6T,OAAA,SAAAxO,EAAA0B,GAEA,GAAA6C,EAAA8D,SAAArI,GACAA,EAAAA,EAAAG,MAAA,UACA,IAAAhK,MAAAsY,QAAAzO,GACA,MAAA6H,UAAA,gBACA,GAAA7H,GAAAA,EAAA3J,QAAA,KAAA2J,EAAA,GACA,MAAAtH,MAAA,yBAGA,IADA,IAAAgW,EAAAjU,KACA,EAAAuF,EAAA3J,QAAA,CACA,IAAAsY,EAAA3O,EAAAM,QACA,GAAAoO,EAAA/M,QAAA+M,EAAA/M,OAAAgN,IAEA,MADAD,EAAAA,EAAA/M,OAAAgN,cACAjH,GACA,MAAAhP,MAAA,kDAEAgW,EAAAtG,IAAAsG,EAAA,IAAAhH,EAAAiH,IAIA,OAFAjN,GACAgN,EAAAb,QAAAnM,GACAgN,GAOAhH,EAAA/M,UAAAiU,WAAA,WAEA,IADA,IAAAjN,EAAAlH,KAAAsT,YAAAxW,EAAA,EACAA,EAAAoK,EAAAtL,QACAsL,EAAApK,aAAAmQ,EACA/F,EAAApK,KAAAqX,aAEAjN,EAAApK,KAAAb,UACA,OAAA+D,KAAA/D,WAUAgR,EAAA/M,UAAAkU,OAAA,SAAA7O,EAAA8O,EAAAC,GASA,GANA,kBAAAD,GACAC,EAAAD,EACAA,EAAAvZ,IACAuZ,IAAA3Y,MAAAsY,QAAAK,KACAA,EAAA,CAAAA,IAEAvK,EAAA8D,SAAArI,IAAAA,EAAA3J,OAAA,CACA,GAAA,MAAA2J,EACA,OAAAvF,KAAAwQ,KACAjL,EAAAA,EAAAG,MAAA,UACA,IAAAH,EAAA3J,OACA,OAAAoE,KAGA,GAAA,KAAAuF,EAAA,GACA,OAAAvF,KAAAwQ,KAAA4D,OAAA7O,EAAA5H,MAAA,GAAA0W,GAGA,IAAAE,EAAAvU,KAAA0J,IAAAnE,EAAA,IACA,GAAAgP,GACA,GAAA,IAAAhP,EAAA3J,QACA,IAAAyY,IAAA,EAAAA,EAAArI,QAAAuI,EAAAxH,aACA,OAAAwH,OACA,GAAAA,aAAAtH,IAAAsH,EAAAA,EAAAH,OAAA7O,EAAA5H,MAAA,GAAA0W,GAAA,IACA,OAAAE,OAIA,IAAA,IAAAzX,EAAA,EAAAA,EAAAkD,KAAAsT,YAAA1X,SAAAkB,EACA,GAAAkD,KAAAiT,EAAAnW,aAAAmQ,IAAAsH,EAAAvU,KAAAiT,EAAAnW,GAAAsX,OAAA7O,EAAA8O,GAAA,IACA,OAAAE,EAGA,OAAA,OAAAvU,KAAAoP,QAAAkF,EACA,KACAtU,KAAAoP,OAAAgF,OAAA7O,EAAA8O,IAqBApH,EAAA/M,UAAA2S,WAAA,SAAAtN,GACA,IAAAgP,EAAAvU,KAAAoU,OAAA7O,EAAA,CAAA4I,IACA,IAAAoG,EACA,MAAAtW,MAAA,iBAAAsH,GACA,OAAAgP,GAUAtH,EAAA/M,UAAAsU,WAAA,SAAAjP,GACA,IAAAgP,EAAAvU,KAAAoU,OAAA7O,EAAA,CAAAsE,IACA,IAAA0K,EACA,MAAAtW,MAAA,iBAAAsH,EAAA,QAAAvF,MACA,OAAAuU,GAUAtH,EAAA/M,UAAAmP,iBAAA,SAAA9J,GACA,IAAAgP,EAAAvU,KAAAoU,OAAA7O,EAAA,CAAA4I,EAAAtE,IACA,IAAA0K,EACA,MAAAtW,MAAA,yBAAAsH,EAAA,QAAAvF,MACA,OAAAuU,GAUAtH,EAAA/M,UAAAuU,cAAA,SAAAlP,GACA,IAAAgP,EAAAvU,KAAAoU,OAAA7O,EAAA,CAAAyL,IACA,IAAAuD,EACA,MAAAtW,MAAA,oBAAAsH,EAAA,QAAAvF,MACA,OAAAuU,GAIAtH,EAAAmD,EAAA,SAAAC,EAAAqE,EAAAC,GACAxG,EAAAkC,EACAW,EAAA0D,EACA7K,EAAA8K,4CC9aAtZ,EAAAC,QAAAuR,GAEAG,UAAA,mBAEA,IAEAyD,EAFA3G,EAAA1O,EAAA,IAYA,SAAAyR,EAAA7F,EAAAjG,GAEA,IAAA+I,EAAA8D,SAAA5G,GACA,MAAAoG,UAAA,yBAEA,GAAArM,IAAA+I,EAAAwE,SAAAvN,GACA,MAAAqM,UAAA,6BAMApN,KAAAe,QAAAA,EAMAf,KAAAgH,KAAAA,EAMAhH,KAAAoP,OAAA,KAMApP,KAAAkP,UAAA,EAMAlP,KAAAkN,QAAA,KAMAlN,KAAAc,SAAA,KAGA/B,OAAA6V,iBAAA/H,EAAA3M,UAAA,CAQAsQ,KAAA,CACA9G,IAAA,WAEA,IADA,IAAAuK,EAAAjU,KACA,OAAAiU,EAAA7E,QACA6E,EAAAA,EAAA7E,OACA,OAAA6E,IAUA1J,SAAA,CACAb,IAAA,WAGA,IAFA,IAAAnE,EAAA,CAAAvF,KAAAgH,MACAiN,EAAAjU,KAAAoP,OACA6E,GACA1O,EAAAsP,QAAAZ,EAAAjN,MACAiN,EAAAA,EAAA7E,OAEA,OAAA7J,EAAA3H,KAAA,SAUAiP,EAAA3M,UAAAsN,OAAA,WACA,MAAAvP,SAQA4O,EAAA3M,UAAA2T,MAAA,SAAAzE,GACApP,KAAAoP,QAAApP,KAAAoP,SAAAA,GACApP,KAAAoP,OAAAnB,OAAAjO,MACAA,KAAAoP,OAAAA,EACApP,KAAAkP,UAAA,EACA,IAAAsB,EAAApB,EAAAoB,KACAA,aAAAC,GACAD,EAAAsE,EAAA9U,OAQA6M,EAAA3M,UAAA4T,SAAA,SAAA1E,GACA,IAAAoB,EAAApB,EAAAoB,KACAA,aAAAC,GACAD,EAAAuE,EAAA/U,MACAA,KAAAoP,OAAA,KACApP,KAAAkP,UAAA,GAOArC,EAAA3M,UAAAjE,QAAA,WACA,OAAA+D,KAAAkP,UAEAlP,KAAAwQ,gBAAAC,IACAzQ,KAAAkP,UAAA,GAFAlP,MAWA6M,EAAA3M,UAAA6O,UAAA,SAAA/H,GACA,OAAAhH,KAAAe,QACAf,KAAAe,QAAAiG,GACAlM,IAUA+R,EAAA3M,UAAA8O,UAAA,SAAAhI,EAAAtH,EAAAuP,GAGA,OAFAA,GAAAjP,KAAAe,SAAAf,KAAAe,QAAAiG,KAAAlM,MACAkF,KAAAe,UAAAf,KAAAe,QAAA,KAAAiG,GAAAtH,GACAM,MASA6M,EAAA3M,UAAA0T,WAAA,SAAA7S,EAAAkO,GACA,GAAAlO,EACA,IAAA,IAAA/B,EAAAD,OAAAC,KAAA+B,GAAAjE,EAAA,EAAAA,EAAAkC,EAAApD,SAAAkB,EACAkD,KAAAgP,UAAAhQ,EAAAlC,GAAAiE,EAAA/B,EAAAlC,IAAAmS,GACA,OAAAjP,MAOA6M,EAAA3M,UAAAxB,SAAA,WACA,IAAAsO,EAAAhN,KAAA+M,YAAAC,UACAzC,EAAAvK,KAAAuK,SACA,OAAAA,EAAA3O,OACAoR,EAAA,IAAAzC,EACAyC,GAIAH,EAAAuD,EAAA,SAAA4E,GACAvE,EAAAuE,+BCrMA3Z,EAAAC,QAAAwV,EAGA,IAAAjE,EAAAzR,EAAA,MACA0V,EAAA5Q,UAAAnB,OAAA+N,OAAAD,EAAA3M,YAAA6M,YAAA+D,GAAA9D,UAAA,QAEA,IAAAkB,EAAA9S,EAAA,IACA0O,EAAA1O,EAAA,IAYA,SAAA0V,EAAA9J,EAAAiO,EAAAlU,EAAAmM,GAQA,GAPAxR,MAAAsY,QAAAiB,KACAlU,EAAAkU,EACAA,EAAAna,IAEA+R,EAAAvG,KAAAtG,KAAAgH,EAAAjG,GAGAkU,IAAAna,KAAAY,MAAAsY,QAAAiB,GACA,MAAA7H,UAAA,+BAMApN,KAAAmI,MAAA8M,GAAA,GAOAjV,KAAA4K,YAAA,GAMA5K,KAAAkN,QAAAA,EA0CA,SAAAgI,EAAA/M,GACA,GAAAA,EAAAiH,OACA,IAAA,IAAAtS,EAAA,EAAAA,EAAAqL,EAAAyC,YAAAhP,SAAAkB,EACAqL,EAAAyC,YAAA9N,GAAAsS,QACAjH,EAAAiH,OAAAzB,IAAAxF,EAAAyC,YAAA9N,IA7BAgU,EAAAxD,SAAA,SAAAtG,EAAAC,GACA,OAAA,IAAA6J,EAAA9J,EAAAC,EAAAkB,MAAAlB,EAAAlG,QAAAkG,EAAAiG,UAQA4D,EAAA5Q,UAAAsN,OAAA,SAAAC,GACA,IAAAC,IAAAD,KAAAA,EAAAC,aACA,OAAA5D,EAAAiB,SAAA,CACA,UAAA/K,KAAAe,QACA,QAAAf,KAAAmI,MACA,UAAAuF,EAAA1N,KAAAkN,QAAApS,MAuBAgW,EAAA5Q,UAAAyN,IAAA,SAAA1D,GAGA,KAAAA,aAAAiE,GACA,MAAAd,UAAA,yBAQA,OANAnD,EAAAmF,QAAAnF,EAAAmF,SAAApP,KAAAoP,QACAnF,EAAAmF,OAAAnB,OAAAhE,GACAjK,KAAAmI,MAAA3K,KAAAyM,EAAAjD,MACAhH,KAAA4K,YAAApN,KAAAyM,GAEAiL,EADAjL,EAAAoB,OAAArL,MAEAA,MAQA8Q,EAAA5Q,UAAA+N,OAAA,SAAAhE,GAGA,KAAAA,aAAAiE,GACA,MAAAd,UAAA,yBAEA,IAAAtR,EAAAkE,KAAA4K,YAAAoB,QAAA/B,GAGA,GAAAnO,EAAA,EACA,MAAAmC,MAAAgM,EAAA,uBAAAjK,MAUA,OARAA,KAAA4K,YAAArK,OAAAzE,EAAA,IAIA,GAHAA,EAAAkE,KAAAmI,MAAA6D,QAAA/B,EAAAjD,QAIAhH,KAAAmI,MAAA5H,OAAAzE,EAAA,GAEAmO,EAAAoB,OAAA,KACArL,MAMA8Q,EAAA5Q,UAAA2T,MAAA,SAAAzE,GACAvC,EAAA3M,UAAA2T,MAAAvN,KAAAtG,KAAAoP,GAGA,IAFA,IAEAtS,EAAA,EAAAA,EAAAkD,KAAAmI,MAAAvM,SAAAkB,EAAA,CACA,IAAAmN,EAAAmF,EAAA1F,IAAA1J,KAAAmI,MAAArL,IACAmN,IAAAA,EAAAoB,SACApB,EAAAoB,OALArL,MAMA4K,YAAApN,KAAAyM,GAIAiL,EAAAlV,OAMA8Q,EAAA5Q,UAAA4T,SAAA,SAAA1E,GACA,IAAA,IAAAnF,EAAAnN,EAAA,EAAAA,EAAAkD,KAAA4K,YAAAhP,SAAAkB,GACAmN,EAAAjK,KAAA4K,YAAA9N,IAAAsS,QACAnF,EAAAmF,OAAAnB,OAAAhE,GACA4C,EAAA3M,UAAA4T,SAAAxN,KAAAtG,KAAAoP,IAmBA0B,EAAAlB,EAAA,WAGA,IAFA,IAAAqF,EAAAvZ,MAAAC,UAAAC,QACAE,EAAA,EACAA,EAAAH,UAAAC,QACAqZ,EAAAnZ,GAAAH,UAAAG,KACA,OAAA,SAAAoE,EAAAiV,GACArL,EAAAkG,aAAA9P,EAAA6M,aACAY,IAAA,IAAAmD,EAAAqE,EAAAF,IACAlW,OAAA+P,eAAA5O,EAAAiV,EAAA,CACAzL,IAAAI,EAAAsL,YAAAH,GACAI,IAAAvL,EAAAwL,YAAAL,gDCtMA5Z,EAAAC,QAAAsW,GAEA9Q,SAAA,KACA8Q,EAAAzC,SAAA,CAAAoG,UAAA,GAEA,IAAA5D,EAAAvW,EAAA,IACAqV,EAAArV,EAAA,IACA+S,EAAA/S,EAAA,IACA8S,EAAA9S,EAAA,IACA2V,EAAA3V,EAAA,IACA0V,EAAA1V,EAAA,IACAyO,EAAAzO,EAAA,IACA4V,EAAA5V,EAAA,IACA6V,EAAA7V,EAAA,IACAgR,EAAAhR,EAAA,IACA0O,EAAA1O,EAAA,IAEAoa,EAAA,gBACAC,EAAA,kBACAC,EAAA,qBACAC,EAAA,uBACAC,EAAA,YACAC,EAAA,cACAC,EAAA,oDACAC,EAAA,2BACAC,EAAA,+DACAC,EAAA,kCAkCA,SAAArE,EAAAnT,EAAA+R,EAAAzP,GAEAyP,aAAAC,IACA1P,EAAAyP,EACAA,EAAA,IAAAC,GAEA1P,IACAA,EAAA6Q,EAAAzC,UAEA,IAQA+G,EACAC,EACAC,EACAC,EA0lBAC,EArmBAC,EAAA5E,EAAAlT,EAAAsC,EAAAyV,uBAAA,GACAC,EAAAF,EAAAE,KACAjZ,EAAA+Y,EAAA/Y,KACAkZ,EAAAH,EAAAG,KACAC,EAAAJ,EAAAI,KACAC,EAAAL,EAAAK,KAEAC,GAAA,EAKAC,GAAA,EAEA7C,EAAAzD,EAEAuG,EAAAhW,EAAAwU,SAAA,SAAAvO,GAAA,OAAAA,GAAA8C,EAAAkN,UAGA,SAAAC,EAAAX,EAAAtP,EAAAkQ,GACA,IAAApW,EAAA8Q,EAAA9Q,SAGA,OAFAoW,IACAtF,EAAA9Q,SAAA,MACA7C,MAAA,YAAA+I,GAAA,SAAA,KAAAsP,EAAA,OAAAxV,EAAAA,EAAA,KAAA,IAAA,QAAAyV,EAAAY,KAAA,KAGA,SAAAC,IACA,IACAd,EADA3N,EAAA,GAEA,EAAA,CAEA,GAAA,OAAA2N,EAAAG,MAAA,MAAAH,EACA,MAAAW,EAAAX,GAEA3N,EAAAnL,KAAAiZ,KACAE,EAAAL,GACAA,EAAAI,UACA,MAAAJ,GAAA,MAAAA,GACA,OAAA3N,EAAA/K,KAAA,IAGA,SAAAyZ,EAAAC,GACA,IAAAhB,EAAAG,IACA,OAAAH,GACA,IAAA,IACA,IAAA,IAEA,OADA9Y,EAAA8Y,GACAc,IACA,IAAA,OAAA,IAAA,OACA,OAAA,EACA,IAAA,QAAA,IAAA,QACA,OAAA,EAEA,IACA,OAuBA,SAAAd,EAAAY,GACA,IAAAhU,EAAA,EACA,MAAAoT,EAAA7Z,OAAA,KACAyG,GAAA,EACAoT,EAAAA,EAAAiB,UAAA,IAEA,OAAAjB,GACA,IAAA,MAAA,IAAA,MAAA,IAAA,MACA,OAAApT,GAAAW,EAAAA,GACA,IAAA,MAAA,IAAA,MAAA,IAAA,MAAA,IAAA,MACA,OAAAD,IACA,IAAA,IACA,OAAA,EAEA,GAAA4R,EAAAtX,KAAAoY,GACA,OAAApT,EAAAsU,SAAAlB,EAAA,IACA,GAAAZ,EAAAxX,KAAAoY,GACA,OAAApT,EAAAsU,SAAAlB,EAAA,IACA,GAAAV,EAAA1X,KAAAoY,GACA,OAAApT,EAAAsU,SAAAlB,EAAA,GAGA,GAAAR,EAAA5X,KAAAoY,GACA,OAAApT,EAAAuU,WAAAnB,GAGA,MAAAW,EAAAX,EAAA,SAAAY,GAjDAQ,CAAApB,GAAA,GACA,MAAAhR,GAGA,GAAAgS,GAAAtB,EAAA9X,KAAAoY,GACA,OAAAA,EAGA,MAAAW,EAAAX,EAAA,UAIA,SAAAqB,EAAAC,EAAAC,GAEA,IADA,IAAAvB,EAAArZ,GAEA4a,GAAA,OAAAvB,EAAAI,MAAA,MAAAJ,EAGAsB,EAAApa,KAAA,CAAAP,EAAA6a,EAAArB,KAAAE,EAAA,MAAA,GAAAmB,EAAArB,KAAAxZ,IAFA2a,EAAApa,KAAA4Z,KAGAT,EAAA,KAAA,KACAA,EAAA,KAgCA,SAAAmB,EAAAxB,EAAAyB,GACA,OAAAzB,GACA,IAAA,MAAA,IAAA,MAAA,IAAA,MACA,OAAA,UACA,IAAA,IACA,OAAA,EAIA,IAAAyB,GAAA,MAAAzB,EAAA7Z,OAAA,GACA,MAAAwa,EAAAX,EAAA,MAEA,GAAAb,EAAAvX,KAAAoY,GACA,OAAAkB,SAAAlB,EAAA,IACA,GAAAX,EAAAzX,KAAAoY,GACA,OAAAkB,SAAAlB,EAAA,IAGA,GAAAT,EAAA3X,KAAAoY,GACA,OAAAkB,SAAAlB,EAAA,GAGA,MAAAW,EAAAX,EAAA,MAGA,SAAA0B,IAGA,GAAA9B,IAAApb,GACA,MAAAmc,EAAA,WAKA,GAHAf,EAAAO,KAGAT,EAAA9X,KAAAgY,GACA,MAAAe,EAAAf,EAAA,QAEAjC,EAAAA,EAAAF,OAAAmC,GACAS,EAAA,KAGA,SAAAsB,IACA,IACAC,EADA5B,EAAAI,IAEA,OAAAJ,GACA,IAAA,OACA4B,EAAA9B,IAAAA,EAAA,IACAK,IACA,MACA,IAAA,SACAA,IAEA,QACAyB,EAAA/B,IAAAA,EAAA,IAGAG,EAAAc,IACAT,EAAA,KACAuB,EAAA1a,KAAA8Y,GAGA,SAAA6B,IAMA,GALAxB,EAAA,KACAN,EAAAe,MACAN,EAAA,WAAAT,IAGA,WAAAA,EACA,MAAAY,EAAAZ,EAAA,UAEAM,EAAA,KAGA,SAAAyB,EAAAhJ,EAAAkH,GACA,OAAAA,GAEA,IAAA,SAGA,OAFA+B,EAAAjJ,EAAAkH,GACAK,EAAA,MACA,EAEA,IAAA,UAEA,OAqCA,SAAAvH,EAAAkH,GAGA,IAAAP,EAAA7X,KAAAoY,EAAAG,KACA,MAAAQ,EAAAX,EAAA,aAEA,IAAA/O,EAAA,IAAA4G,EAAAmI,GACAgC,EAAA/Q,EAAA,SAAA+O,GACA,IAAA8B,EAAA7Q,EAAA+O,GAGA,OAAAA,GAEA,IAAA,OAoHA,SAAAlH,GACAuH,EAAA,KACA,IAAA5O,EAAA0O,IAGA,GAAArK,EAAAM,OAAA3E,KAAAjN,GACA,MAAAmc,EAAAlP,EAAA,QAEA4O,EAAA,KACA,IAAA4B,EAAA9B,IAGA,IAAAT,EAAA9X,KAAAqa,GACA,MAAAtB,EAAAsB,EAAA,QAEA5B,EAAA,KACA,IAAA3P,EAAAyP,IAGA,IAAAV,EAAA7X,KAAA8I,GACA,MAAAiQ,EAAAjQ,EAAA,QAEA2P,EAAA,KACA,IAAA1M,EAAA,IAAA8G,EAAAgG,EAAA/P,GAAA8Q,EAAArB,KAAA1O,EAAAwQ,GACAD,EAAArO,EAAA,SAAAqM,GAGA,GAAA,WAAAA,EAIA,MAAAW,EAAAX,GAHA+B,EAAApO,EAAAqM,GACAK,EAAA,MAIA,WACA6B,EAAAvO,KAEAmF,EAAAzB,IAAA1D,GAvJAwO,CAAAlR,GACA,MAEA,IAAA,WACA,IAAA,WACA,IAAA,WACAmR,EAAAnR,EAAA+O,GACA,MAEA,IAAA,SAiJA,SAAAlH,EAAAkH,GAGA,IAAAP,EAAA7X,KAAAoY,EAAAG,KACA,MAAAQ,EAAAX,EAAA,QAEA,IAAAnO,EAAA,IAAA2I,EAAAiG,EAAAT,IACAgC,EAAAnQ,EAAA,SAAAmO,GACA,WAAAA,GACA+B,EAAAlQ,EAAAmO,GACAK,EAAA,OAEAnZ,EAAA8Y,GACAoC,EAAAvQ,EAAA,eAGAiH,EAAAzB,IAAAxF,GAhKAwQ,CAAApR,EAAA+O,GACA,MAEA,IAAA,aACAqB,EAAApQ,EAAAqR,aAAArR,EAAAqR,WAAA,KACA,MAEA,IAAA,WACAjB,EAAApQ,EAAA8F,WAAA9F,EAAA8F,SAAA,KAAA,GACA,MAEA,QAEA,IAAAyJ,IAAAd,EAAA9X,KAAAoY,GACA,MAAAW,EAAAX,GAEA9Y,EAAA8Y,GACAoC,EAAAnR,EAAA,eAIA6H,EAAAzB,IAAApG,GAnFAsR,CAAAzJ,EAAAkH,IACA,EAEA,IAAA,OAEA,OA4NA,SAAAlH,EAAAkH,GAGA,IAAAP,EAAA7X,KAAAoY,EAAAG,KACA,MAAAQ,EAAAX,EAAA,QAEA,IAAA/I,EAAA,IAAA1D,EAAAyM,GACAgC,EAAA/K,EAAA,SAAA+I,GACA,OAAAA,GACA,IAAA,SACA+B,EAAA9K,EAAA+I,GACAK,EAAA,KACA,MAEA,IAAA,WACAgB,EAAApK,EAAAF,WAAAE,EAAAF,SAAA,KAAA,GACA,MAEA,SAOA,SAAA+B,EAAAkH,GAGA,IAAAP,EAAA7X,KAAAoY,GACA,MAAAW,EAAAX,EAAA,QAEAK,EAAA,KACA,IAAAjX,EAAAoY,EAAArB,KAAA,GACAqC,EAAA,GACAR,EAAAQ,EAAA,SAAAxC,GAGA,GAAA,WAAAA,EAIA,MAAAW,EAAAX,GAHA+B,EAAAS,EAAAxC,GACAK,EAAA,MAIA,WACA6B,EAAAM,KAEA1J,EAAAzB,IAAA2I,EAAA5W,EAAAoZ,EAAA5L,SA3BA6L,CAAAxL,EAAA+I,MAGAlH,EAAAzB,IAAAJ,GAnPAyL,CAAA5J,EAAAkH,IACA,EAEA,IAAA,UAEA,OAoUA,SAAAlH,EAAAkH,GAGA,IAAAP,EAAA7X,KAAAoY,EAAAG,KACA,MAAAQ,EAAAX,EAAA,gBAEA,IAAA2C,EAAA,IAAAjI,EAAAsF,GACAgC,EAAAW,EAAA,SAAA3C,GACA,IAAA8B,EAAAa,EAAA3C,GAAA,CAIA,GAAA,QAAAA,EAGA,MAAAW,EAAAX,IAKA,SAAAlH,EAAAkH,GACA,IAAA/O,EAAA+O,EAGA,IAAAP,EAAA7X,KAAAoY,EAAAG,KACA,MAAAQ,EAAAX,EAAA,QAEA,IACA9D,EAAAC,EACA5Q,EAAA6Q,EAFA1L,EAAAsP,EAIAK,EAAA,KACAA,EAAA,UAAA,KACAlE,GAAA,GAGA,IAAAuD,EAAA9X,KAAAoY,EAAAG,KACA,MAAAQ,EAAAX,GAEA9D,EAAA8D,EACAK,EAAA,KAAAA,EAAA,WAAAA,EAAA,KACAA,EAAA,UAAA,KACAjE,GAAA,GAGA,IAAAsD,EAAA9X,KAAAoY,EAAAG,KACA,MAAAQ,EAAAX,GAEAzU,EAAAyU,EACAK,EAAA,KAEA,IAAAuC,EAAA,IAAAjI,EAAAjK,EAAAO,EAAAiL,EAAA3Q,EAAA4Q,EAAAC,GACA4F,EAAAY,EAAA,SAAA5C,GAGA,GAAA,WAAAA,EAIA,MAAAW,EAAAX,GAHA+B,EAAAa,EAAA5C,GACAK,EAAA,OAKAvH,EAAAzB,IAAAuL,GAjDAC,CAAAF,EAAA3C,MAIAlH,EAAAzB,IAAAsL,GAtVAG,CAAAhK,EAAAkH,IACA,EAEA,IAAA,SAEA,OAiYA,SAAAlH,EAAAkH,GAGA,IAAAN,EAAA9X,KAAAoY,EAAAG,KACA,MAAAQ,EAAAX,EAAA,aAEA,IAAA+C,EAAA/C,EACAgC,EAAA,KAAA,SAAAhC,GACA,OAAAA,GAEA,IAAA,WACA,IAAA,WACA,IAAA,WACAoC,EAAAtJ,EAAAkH,EAAA+C,GACA,MAEA,QAEA,IAAAvC,IAAAd,EAAA9X,KAAAoY,GACA,MAAAW,EAAAX,GACA9Y,EAAA8Y,GACAoC,EAAAtJ,EAAA,WAAAiK,MAvZAC,CAAAlK,EAAAkH,IACA,EAEA,OAAA,EAGA,SAAAgC,EAAAtF,EAAAuG,EAAAC,GACA,IAAAC,EAAAlD,EAAAY,KAKA,GAJAnE,IACAA,EAAA9F,QAAA0J,IACA5D,EAAAlS,SAAA8Q,EAAA9Q,UAEA6V,EAAA,KAAA,GAAA,CAEA,IADA,IAAAL,EACA,OAAAA,EAAAG,MACA8C,EAAAjD,GACAK,EAAA,KAAA,QAEA6C,GACAA,IACA7C,EAAA,KACA3D,GAAA,iBAAAA,EAAA9F,UACA8F,EAAA9F,QAAA0J,EAAA6C,IAoDA,SAAAf,EAAAtJ,EAAAtG,EAAAuF,GACA,IAAA9G,EAAAkP,IACA,GAAA,UAAAlP,EAAA,CAMA,IAAAyO,EAAA9X,KAAAqJ,GACA,MAAA0P,EAAA1P,EAAA,QAEA,IAAAP,EAAAyP,IAGA,IAAAV,EAAA7X,KAAA8I,GACA,MAAAiQ,EAAAjQ,EAAA,QAEAA,EAAA+P,EAAA/P,GACA2P,EAAA,KAEA,IAAA1M,EAAA,IAAAiE,EAAAlH,EAAA8Q,EAAArB,KAAAlP,EAAAuB,EAAAuF,GACAiK,EAAArO,EAAA,SAAAqM,GAGA,GAAA,WAAAA,EAIA,MAAAW,EAAAX,GAHA+B,EAAApO,EAAAqM,GACAK,EAAA,MAIA,WACA6B,EAAAvO,KAEAmF,EAAAzB,IAAA1D,GAKA6M,IAAA7M,EAAAI,UAAA+B,EAAAE,OAAA/E,KAAAzM,IAAAsR,EAAAC,MAAA9E,KAAAzM,IACAmP,EAAA+E,UAAA,UAAA,GAAA,QAGA,SAAAI,EAAAtG,GACA,IAAA9B,EAAAyP,IAGA,IAAAV,EAAA7X,KAAA8I,GACA,MAAAiQ,EAAAjQ,EAAA,QAEA,IAAAkJ,EAAApG,EAAA4P,QAAA1S,GACAA,IAAAkJ,IACAlJ,EAAA8C,EAAA6P,QAAA3S,IACA2P,EAAA,KACA,IAAAnP,EAAAsQ,EAAArB,KACAlP,EAAA,IAAA4G,EAAAnH,GACAO,EAAA2E,OAAA,EACA,IAAAjC,EAAA,IAAAiE,EAAAgC,EAAA1I,EAAAR,EAAA8B,GACAmB,EAAAnJ,SAAA8Q,EAAA9Q,SACAwX,EAAA/Q,EAAA,SAAA+O,GACA,OAAAA,GAEA,IAAA,SACA+B,EAAA9Q,EAAA+O,GACAK,EAAA,KACA,MAEA,IAAA,WACA,IAAA,WACA,IAAA,WACA+B,EAAAnR,EAAA+O,GACA,MAGA,QACA,MAAAW,EAAAX,MAGAlH,EAAAzB,IAAApG,GACAoG,IAAA1D,GA3EA2P,CAAAxK,EAAAtG,GAyLA,SAAAuP,EAAAjJ,EAAAkH,GACA,IAAAuD,EAAAlD,EAAA,KAAA,GAGA,IAAAX,EAAA9X,KAAAoY,EAAAG,KACA,MAAAQ,EAAAX,EAAA,QAEA,IAAAtP,EAAAsP,EACAuD,IACAlD,EAAA,KACA3P,EAAA,IAAAA,EAAA,IACAsP,EAAAI,IACAT,EAAA/X,KAAAoY,KACAtP,GAAAsP,EACAG,MAGAE,EAAA,KAIA,SAAAmD,EAAA1K,EAAApI,GACA,GAAA2P,EAAA,KAAA,GACA,EAAA,CAEA,IAAAZ,EAAA7X,KAAAoY,EAAAG,KACA,MAAAQ,EAAAX,EAAA,QAEA,MAAAI,IACAoD,EAAA1K,EAAApI,EAAA,IAAAsP,IAEAK,EAAA,KACA,MAAAD,IACAoD,EAAA1K,EAAApI,EAAA,IAAAsP,GAEAtH,EAAAI,EAAApI,EAAA,IAAAsP,EAAAe,GAAA,KAEAV,EAAA,KAAA,UACAA,EAAA,KAAA,SAEA3H,EAAAI,EAAApI,EAAAqQ,GAAA,IAtBAyC,CAAA1K,EAAApI,GA0BA,SAAAgI,EAAAI,EAAApI,EAAAtH,GACA0P,EAAAJ,WACAI,EAAAJ,UAAAhI,EAAAtH,GAGA,SAAA8Y,EAAApJ,GACA,GAAAuH,EAAA,KAAA,GAAA,CACA,KACA0B,EAAAjJ,EAAA,UACAuH,EAAA,KAAA,KACAA,EAAA,KAEA,OAAAvH,EAgGA,KAAA,QAAAkH,EAAAG,MACA,OAAAH,GAEA,IAAA,UAGA,IAAAO,EACA,MAAAI,EAAAX,GAEA0B,IACA,MAEA,IAAA,SAGA,IAAAnB,EACA,MAAAI,EAAAX,GAEA2B,IACA,MAEA,IAAA,SAGA,IAAApB,EACA,MAAAI,EAAAX,GAEA6B,IACA,MAEA,IAAA,SAGA,IAAAtB,EACA,MAAAI,EAAAX,GAEA+B,EAAApE,EAAAqC,GACAK,EAAA,KACA,MAEA,QAGA,GAAAyB,EAAAnE,EAAAqC,GAAA,CACAO,GAAA,EACA,SAIA,MAAAI,EAAAX,GAKA,OADA1E,EAAA9Q,SAAA,KACA,CACAiZ,QAAA7D,EACAC,QAAAA,EACAC,YAAAA,EACAC,OAAAA,EACA7F,KAAAA,4FCtuBAnV,EAAAC,QAAA+V,EAEA,IAEAC,EAFAxH,EAAA1O,EAAA,IAIA4e,EAAAlQ,EAAAkQ,SACAzT,EAAAuD,EAAAvD,KAGA,SAAA0T,EAAA7H,EAAA8H,GACA,OAAAC,WAAA,uBAAA/H,EAAA5P,IAAA,OAAA0X,GAAA,GAAA,MAAA9H,EAAA5L,KASA,SAAA6K,EAAArU,GAMAgD,KAAAuC,IAAAvF,EAMAgD,KAAAwC,IAAA,EAMAxC,KAAAwG,IAAAxJ,EAAApB,OAGA,IAwCA8D,EAxCA0a,EAAA,oBAAAzY,WACA,SAAA3E,GACA,GAAAA,aAAA2E,YAAAjG,MAAAsY,QAAAhX,GACA,OAAA,IAAAqU,EAAArU,GACA,MAAAiB,MAAA,mBAGA,SAAAjB,GACA,GAAAtB,MAAAsY,QAAAhX,GACA,OAAA,IAAAqU,EAAArU,GACA,MAAAiB,MAAA,mBAkEA,SAAAoc,IAEA,IAAAC,EAAA,IAAAN,EAAA,EAAA,GACAld,EAAA,EACA,KAAA,EAAAkD,KAAAwG,IAAAxG,KAAAwC,KAaA,CACA,KAAA1F,EAAA,IAAAA,EAAA,CAEA,GAAAkD,KAAAwC,KAAAxC,KAAAwG,IACA,MAAAyT,EAAAja,MAGA,GADAsa,EAAArV,IAAAqV,EAAArV,IAAA,IAAAjF,KAAAuC,IAAAvC,KAAAwC,OAAA,EAAA1F,KAAA,EACAkD,KAAAuC,IAAAvC,KAAAwC,OAAA,IACA,OAAA8X,EAIA,OADAA,EAAArV,IAAAqV,EAAArV,IAAA,IAAAjF,KAAAuC,IAAAvC,KAAAwC,SAAA,EAAA1F,KAAA,EACAwd,EAxBA,KAAAxd,EAAA,IAAAA,EAGA,GADAwd,EAAArV,IAAAqV,EAAArV,IAAA,IAAAjF,KAAAuC,IAAAvC,KAAAwC,OAAA,EAAA1F,KAAA,EACAkD,KAAAuC,IAAAvC,KAAAwC,OAAA,IACA,OAAA8X,EAKA,GAFAA,EAAArV,IAAAqV,EAAArV,IAAA,IAAAjF,KAAAuC,IAAAvC,KAAAwC,OAAA,MAAA,EACA8X,EAAApV,IAAAoV,EAAApV,IAAA,IAAAlF,KAAAuC,IAAAvC,KAAAwC,OAAA,KAAA,EACAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IACA,OAAA8X,EAgBA,GAfAxd,EAAA,EAeA,EAAAkD,KAAAwG,IAAAxG,KAAAwC,KACA,KAAA1F,EAAA,IAAAA,EAGA,GADAwd,EAAApV,IAAAoV,EAAApV,IAAA,IAAAlF,KAAAuC,IAAAvC,KAAAwC,OAAA,EAAA1F,EAAA,KAAA,EACAkD,KAAAuC,IAAAvC,KAAAwC,OAAA,IACA,OAAA8X,OAGA,KAAAxd,EAAA,IAAAA,EAAA,CAEA,GAAAkD,KAAAwC,KAAAxC,KAAAwG,IACA,MAAAyT,EAAAja,MAGA,GADAsa,EAAApV,IAAAoV,EAAApV,IAAA,IAAAlF,KAAAuC,IAAAvC,KAAAwC,OAAA,EAAA1F,EAAA,KAAA,EACAkD,KAAAuC,IAAAvC,KAAAwC,OAAA,IACA,OAAA8X,EAIA,MAAArc,MAAA,2BAkCA,SAAAsc,EAAAhY,EAAArF,GACA,OAAAqF,EAAArF,EAAA,GACAqF,EAAArF,EAAA,IAAA,EACAqF,EAAArF,EAAA,IAAA,GACAqF,EAAArF,EAAA,IAAA,MAAA,EA+BA,SAAAsd,IAGA,GAAAxa,KAAAwC,IAAA,EAAAxC,KAAAwG,IACA,MAAAyT,EAAAja,KAAA,GAEA,OAAA,IAAAga,EAAAO,EAAAva,KAAAuC,IAAAvC,KAAAwC,KAAA,GAAA+X,EAAAva,KAAAuC,IAAAvC,KAAAwC,KAAA,IArLA6O,EAAAvE,OAAAhD,EAAA2Q,OACA,SAAAzd,GACA,OAAAqU,EAAAvE,OAAA,SAAA9P,GACA,OAAA8M,EAAA2Q,OAAAC,SAAA1d,GACA,IAAAsU,EAAAtU,GAEAod,EAAApd,KACAA,IAGAod,EAEA/I,EAAAnR,UAAAya,EAAA7Q,EAAApO,MAAAwE,UAAA0a,UAAA9Q,EAAApO,MAAAwE,UAAAvC,MAOA0T,EAAAnR,UAAA2a,QACAnb,EAAA,WACA,WACA,GAAAA,GAAA,IAAAM,KAAAuC,IAAAvC,KAAAwC,QAAA,EAAAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IAAA,OAAA9C,EACA,GAAAA,GAAAA,GAAA,IAAAM,KAAAuC,IAAAvC,KAAAwC,OAAA,KAAA,EAAAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IAAA,OAAA9C,EACA,GAAAA,GAAAA,GAAA,IAAAM,KAAAuC,IAAAvC,KAAAwC,OAAA,MAAA,EAAAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IAAA,OAAA9C,EACA,GAAAA,GAAAA,GAAA,IAAAM,KAAAuC,IAAAvC,KAAAwC,OAAA,MAAA,EAAAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IAAA,OAAA9C,EACA,GAAAA,GAAAA,GAAA,GAAAM,KAAAuC,IAAAvC,KAAAwC,OAAA,MAAA,EAAAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IAAA,OAAA9C,EAGA,IAAAM,KAAAwC,KAAA,GAAAxC,KAAAwG,IAEA,MADAxG,KAAAwC,IAAAxC,KAAAwG,IACAyT,EAAAja,KAAA,IAEA,OAAAN,IAQA2R,EAAAnR,UAAA4a,MAAA,WACA,OAAA,EAAA9a,KAAA6a,UAOAxJ,EAAAnR,UAAA6a,OAAA,WACA,IAAArb,EAAAM,KAAA6a,SACA,OAAAnb,IAAA,IAAA,EAAAA,GAAA,GAqFA2R,EAAAnR,UAAA8a,KAAA,WACA,OAAA,IAAAhb,KAAA6a,UAcAxJ,EAAAnR,UAAA+a,QAAA,WAGA,GAAAjb,KAAAwC,IAAA,EAAAxC,KAAAwG,IACA,MAAAyT,EAAAja,KAAA,GAEA,OAAAua,EAAAva,KAAAuC,IAAAvC,KAAAwC,KAAA,IAOA6O,EAAAnR,UAAAgb,SAAA,WAGA,GAAAlb,KAAAwC,IAAA,EAAAxC,KAAAwG,IACA,MAAAyT,EAAAja,KAAA,GAEA,OAAA,EAAAua,EAAAva,KAAAuC,IAAAvC,KAAAwC,KAAA,IAmCA6O,EAAAnR,UAAAib,MAAA,WAGA,GAAAnb,KAAAwC,IAAA,EAAAxC,KAAAwG,IACA,MAAAyT,EAAAja,KAAA,GAEA,IAAAN,EAAAoK,EAAAqR,MAAArY,YAAA9C,KAAAuC,IAAAvC,KAAAwC,KAEA,OADAxC,KAAAwC,KAAA,EACA9C,GAQA2R,EAAAnR,UAAAkb,OAAA,WAGA,GAAApb,KAAAwC,IAAA,EAAAxC,KAAAwG,IACA,MAAAyT,EAAAja,KAAA,GAEA,IAAAN,EAAAoK,EAAAqR,MAAAxW,aAAA3E,KAAAuC,IAAAvC,KAAAwC,KAEA,OADAxC,KAAAwC,KAAA,EACA9C,GAOA2R,EAAAnR,UAAA0L,MAAA,WACA,IAAAhQ,EAAAoE,KAAA6a,SACA5d,EAAA+C,KAAAwC,IACAtF,EAAA8C,KAAAwC,IAAA5G,EAGA,GAAAsB,EAAA8C,KAAAwG,IACA,MAAAyT,EAAAja,KAAApE,GAGA,OADAoE,KAAAwC,KAAA5G,EACAF,MAAAsY,QAAAhU,KAAAuC,KACAvC,KAAAuC,IAAA5E,MAAAV,EAAAC,GACAD,IAAAC,EACA,IAAA8C,KAAAuC,IAAAwK,YAAA,GACA/M,KAAA2a,EAAArU,KAAAtG,KAAAuC,IAAAtF,EAAAC,IAOAmU,EAAAnR,UAAA5D,OAAA,WACA,IAAAsP,EAAA5L,KAAA4L,QACA,OAAArF,EAAAE,KAAAmF,EAAA,EAAAA,EAAAhQ,SAQAyV,EAAAnR,UAAAyW,KAAA,SAAA/a,GACA,GAAA,iBAAAA,EAAA,CAEA,GAAAoE,KAAAwC,IAAA5G,EAAAoE,KAAAwG,IACA,MAAAyT,EAAAja,KAAApE,GACAoE,KAAAwC,KAAA5G,OAEA,GAEA,GAAAoE,KAAAwC,KAAAxC,KAAAwG,IACA,MAAAyT,EAAAja,YACA,IAAAA,KAAAuC,IAAAvC,KAAAwC,QAEA,OAAAxC,MAQAqR,EAAAnR,UAAAmb,SAAA,SAAA5O,GACA,OAAAA,GACA,KAAA,EACAzM,KAAA2W,OACA,MACA,KAAA,EACA3W,KAAA2W,KAAA,GACA,MACA,KAAA,EACA3W,KAAA2W,KAAA3W,KAAA6a,UACA,MACA,KAAA,EACA,KAAA,IAAApO,EAAA,EAAAzM,KAAA6a,WACA7a,KAAAqb,SAAA5O,GAEA,MACA,KAAA,EACAzM,KAAA2W,KAAA,GACA,MAGA,QACA,MAAA1Y,MAAA,qBAAAwO,EAAA,cAAAzM,KAAAwC,KAEA,OAAAxC,MAGAqR,EAAAjB,EAAA,SAAAkL,GACAhK,EAAAgK,EAEA,IAAA/f,EAAAuO,EAAA4E,KAAA,SAAA,WACA5E,EAAAyR,MAAAlK,EAAAnR,UAAA,CAEAsb,MAAA,WACA,OAAAnB,EAAA/T,KAAAtG,MAAAzE,IAAA,IAGAkgB,OAAA,WACA,OAAApB,EAAA/T,KAAAtG,MAAAzE,IAAA,IAGAmgB,OAAA,WACA,OAAArB,EAAA/T,KAAAtG,MAAA2b,WAAApgB,IAAA,IAGAqgB,QAAA,WACA,OAAApB,EAAAlU,KAAAtG,MAAAzE,IAAA,IAGAsgB,SAAA,WACA,OAAArB,EAAAlU,KAAAtG,MAAAzE,IAAA,mCC/YAF,EAAAC,QAAAgW,EAGA,IAAAD,EAAAjW,EAAA,KACAkW,EAAApR,UAAAnB,OAAA+N,OAAAuE,EAAAnR,YAAA6M,YAAAuE,EAEA,IAAAxH,EAAA1O,EAAA,IASA,SAAAkW,EAAAtU,GACAqU,EAAA/K,KAAAtG,KAAAhD,GAUA8M,EAAA2Q,SACAnJ,EAAApR,UAAAya,EAAA7Q,EAAA2Q,OAAAva,UAAAvC,OAKA2T,EAAApR,UAAA5D,OAAA,WACA,IAAAkK,EAAAxG,KAAA6a,SACA,OAAA7a,KAAAuC,IAAAuZ,UAAA9b,KAAAwC,IAAAxC,KAAAwC,IAAA9F,KAAAqf,IAAA/b,KAAAwC,IAAAgE,EAAAxG,KAAAwG,yCClCAnL,EAAAC,QAAAmV,EAGA,IAAAxD,EAAA7R,EAAA,MACAqV,EAAAvQ,UAAAnB,OAAA+N,OAAAG,EAAA/M,YAAA6M,YAAA0D,GAAAzD,UAAA,OAEA,IAKAmB,EACAyD,EACA/K,EAPAqH,EAAA9S,EAAA,IACAyO,EAAAzO,EAAA,IACA0V,EAAA1V,EAAA,IACA0O,EAAA1O,EAAA,IAaA,SAAAqV,EAAA1P,GACAkM,EAAA3G,KAAAtG,KAAA,GAAAe,GAMAf,KAAAgc,SAAA,GAMAhc,KAAAic,MAAA,GA6BA,SAAAC,KApBAzL,EAAAnD,SAAA,SAAArG,EAAAuJ,GAKA,OAJAA,IACAA,EAAA,IAAAC,GACAxJ,EAAAlG,SACAyP,EAAAoD,WAAA3M,EAAAlG,SACAyP,EAAA4C,QAAAnM,EAAAC,SAWAuJ,EAAAvQ,UAAAic,YAAArS,EAAAvE,KAAAtJ,QAaAwU,EAAAvQ,UAAAqQ,KAAA,SAAAA,EAAAzP,EAAAC,EAAAC,GACA,mBAAAD,IACAC,EAAAD,EACAA,EAAAjG,IAEA,IAAAshB,EAAApc,KACA,IAAAgB,EACA,OAAA8I,EAAAnJ,UAAA4P,EAAA6L,EAAAtb,EAAAC,GAEA,IAAAsb,EAAArb,IAAAkb,EAGA,SAAAI,EAAAngB,EAAAqU,GAEA,GAAAxP,EAAA,CAEA,IAAAub,EAAAvb,EAEA,GADAA,EAAA,KACAqb,EACA,MAAAlgB,EACAogB,EAAApgB,EAAAqU,IAIA,SAAAgM,EAAA1b,EAAArC,GACA,IAGA,GAFAqL,EAAA8D,SAAAnP,IAAA,MAAAA,EAAAhC,OAAA,KACAgC,EAAAmB,KAAAgS,MAAAnT,IACAqL,EAAA8D,SAAAnP,GAEA,CACAmT,EAAA9Q,SAAAA,EACA,IACAoO,EADAuN,EAAA7K,EAAAnT,EAAA2d,EAAArb,GAEAjE,EAAA,EACA,GAAA2f,EAAAtG,QACA,KAAArZ,EAAA2f,EAAAtG,QAAAva,SAAAkB,GACAoS,EAAAkN,EAAAD,YAAArb,EAAA2b,EAAAtG,QAAArZ,MACA4D,EAAAwO,GACA,GAAAuN,EAAArG,YACA,IAAAtZ,EAAA,EAAAA,EAAA2f,EAAArG,YAAAxa,SAAAkB,GACAoS,EAAAkN,EAAAD,YAAArb,EAAA2b,EAAArG,YAAAtZ,MACA4D,EAAAwO,GAAA,QAbAkN,EAAAxI,WAAAnV,EAAAsC,SAAAqS,QAAA3U,EAAAyI,QAeA,MAAA/K,GACAmgB,EAAAngB,GAEAkgB,GAAAK,GACAJ,EAAA,KAAAF,GAIA,SAAA1b,EAAAI,EAAA6b,GAGA,IAAAC,EAAA9b,EAAA+b,YAAA,oBACA,IAAA,EAAAD,EAAA,CACA,IAAAE,EAAAhc,EAAAyW,UAAAqF,GACAE,KAAAjW,IACA/F,EAAAgc,GAIA,MAAA,EAAAV,EAAAH,MAAAjQ,QAAAlL,IAKA,GAHAsb,EAAAH,MAAAze,KAAAsD,GAGAA,KAAA+F,EACAwV,EACAG,EAAA1b,EAAA+F,EAAA/F,OAEA4b,EACAK,WAAA,aACAL,EACAF,EAAA1b,EAAA+F,EAAA/F,YAOA,GAAAub,EAAA,CACA,IAAA5d,EACA,IACAA,EAAAqL,EAAAlJ,GAAAoc,aAAAlc,GAAApC,SAAA,QACA,MAAAvC,GAGA,YAFAwgB,GACAL,EAAAngB,IAGAqgB,EAAA1b,EAAArC,SAEAie,EACA5S,EAAApJ,MAAAI,EAAA,SAAA3E,EAAAsC,KACAie,EAEA1b,IAEA7E,EAEAwgB,EAEAD,GACAJ,EAAA,KAAAF,GAFAE,EAAAngB,GAKAqgB,EAAA1b,EAAArC,MAIA,IAAAie,EAAA,EAIA5S,EAAA8D,SAAA9M,KACAA,EAAA,CAAAA,IACA,IAAA,IAAAoO,EAAApS,EAAA,EAAAA,EAAAgE,EAAAlF,SAAAkB,GACAoS,EAAAkN,EAAAD,YAAA,GAAArb,EAAAhE,MACA4D,EAAAwO,GAEA,OAAAmN,EACAD,GACAM,GACAJ,EAAA,KAAAF,GACAthB,KAgCA2V,EAAAvQ,UAAAwQ,SAAA,SAAA5P,EAAAC,GACA,IAAA+I,EAAAmT,OACA,MAAAhf,MAAA,iBACA,OAAA+B,KAAAuQ,KAAAzP,EAAAC,EAAAmb,IAMAzL,EAAAvQ,UAAAiU,WAAA,WACA,GAAAnU,KAAAgc,SAAApgB,OACA,MAAAqC,MAAA,4BAAA+B,KAAAgc,SAAAlR,IAAA,SAAAb,GACA,MAAA,WAAAA,EAAAoE,OAAA,QAAApE,EAAAmF,OAAA7E,WACA3M,KAAA,OACA,OAAAqP,EAAA/M,UAAAiU,WAAA7N,KAAAtG,OAIA,IAAAkd,EAAA,SAUA,SAAAC,EAAA3M,EAAAvG,GACA,IAAAmT,EAAAnT,EAAAmF,OAAAgF,OAAAnK,EAAAoE,QACA,GAAA+O,EAAA,CACA,IAAAC,EAAA,IAAAnP,EAAAjE,EAAAM,SAAAN,EAAAzC,GAAAyC,EAAA1C,KAAA0C,EAAAnB,KAAAhO,GAAAmP,EAAAlJ,SAIA,OAHAsc,EAAAzO,eAAA3E,GACA0E,eAAA0O,EACAD,EAAAzP,IAAA0P,IACA,EAEA,OAAA,EASA5M,EAAAvQ,UAAA4U,EAAA,SAAAvC,GACA,GAAAA,aAAArE,EAEAqE,EAAAlE,SAAAvT,IAAAyX,EAAA5D,gBACAwO,EAAAnd,EAAAuS,IACAvS,KAAAgc,SAAAxe,KAAA+U,QAEA,GAAAA,aAAA1I,EAEAqT,EAAAhf,KAAAqU,EAAAvL,QACAuL,EAAAnD,OAAAmD,EAAAvL,MAAAuL,EAAA5J,aAEA,KAAA4J,aAAAzB,GAAA,CAEA,GAAAyB,aAAApE,EACA,IAAA,IAAArR,EAAA,EAAAA,EAAAkD,KAAAgc,SAAApgB,QACAuhB,EAAAnd,EAAAA,KAAAgc,SAAAlf,IACAkD,KAAAgc,SAAAzb,OAAAzD,EAAA,KAEAA,EACA,IAAA,IAAAQ,EAAA,EAAAA,EAAAiV,EAAAe,YAAA1X,SAAA0B,EACA0C,KAAA8U,EAAAvC,EAAAU,EAAA3V,IACA4f,EAAAhf,KAAAqU,EAAAvL,QACAuL,EAAAnD,OAAAmD,EAAAvL,MAAAuL,KAcA9B,EAAAvQ,UAAA6U,EAAA,SAAAxC,GACA,GAAAA,aAAArE,GAEA,GAAAqE,EAAAlE,SAAAvT,GACA,GAAAyX,EAAA5D,eACA4D,EAAA5D,eAAAS,OAAAnB,OAAAsE,EAAA5D,gBACA4D,EAAA5D,eAAA,SACA,CACA,IAAA7S,EAAAkE,KAAAgc,SAAAhQ,QAAAuG,IAEA,EAAAzW,GACAkE,KAAAgc,SAAAzb,OAAAzE,EAAA,SAIA,GAAAyW,aAAA1I,EAEAqT,EAAAhf,KAAAqU,EAAAvL,cACAuL,EAAAnD,OAAAmD,EAAAvL,WAEA,GAAAuL,aAAAtF,EAAA,CAEA,IAAA,IAAAnQ,EAAA,EAAAA,EAAAyV,EAAAe,YAAA1X,SAAAkB,EACAkD,KAAA+U,EAAAxC,EAAAU,EAAAnW,IAEAogB,EAAAhf,KAAAqU,EAAAvL,cACAuL,EAAAnD,OAAAmD,EAAAvL,QAMAyJ,EAAAL,EAAA,SAAAC,EAAAiN,EAAAC,GACApP,EAAAkC,EACAuB,EAAA0L,EACAzW,EAAA0W,uDC5VAliB,EAAAC,QAAA,4BCKAA,EA6BA0V,QAAA5V,EAAA,gCClCAC,EAAAC,QAAA0V,EAEA,IAAAlH,EAAA1O,EAAA,IAsCA,SAAA4V,EAAAwM,EAAAC,EAAAC,GAEA,GAAA,mBAAAF,EACA,MAAApQ,UAAA,8BAEAtD,EAAA/J,aAAAuG,KAAAtG,MAMAA,KAAAwd,QAAAA,EAMAxd,KAAAyd,mBAAAA,EAMAzd,KAAA0d,oBAAAA,IA1DA1M,EAAA9Q,UAAAnB,OAAA+N,OAAAhD,EAAA/J,aAAAG,YAAA6M,YAAAiE,GAwEA9Q,UAAAyd,QAAA,SAAAA,EAAAzE,EAAA0E,EAAAC,EAAAC,EAAA9c,GAEA,IAAA8c,EACA,MAAA1Q,UAAA,6BAEA,IAAAgP,EAAApc,KACA,IAAAgB,EACA,OAAA8I,EAAAnJ,UAAAgd,EAAAvB,EAAAlD,EAAA0E,EAAAC,EAAAC,GAEA,IAAA1B,EAAAoB,QAEA,OADAT,WAAA,WAAA/b,EAAA/C,MAAA,mBAAA,GACAnD,GAGA,IACA,OAAAshB,EAAAoB,QACAtE,EACA0E,EAAAxB,EAAAqB,iBAAA,kBAAA,UAAAK,GAAAxB,SACA,SAAAngB,EAAAsF,GAEA,GAAAtF,EAEA,OADAigB,EAAA5b,KAAA,QAAArE,EAAA+c,GACAlY,EAAA7E,GAGA,GAAA,OAAAsF,EAEA,OADA2a,EAAAlf,KAAA,GACApC,GAGA,KAAA2G,aAAAoc,GACA,IACApc,EAAAoc,EAAAzB,EAAAsB,kBAAA,kBAAA,UAAAjc,GACA,MAAAtF,GAEA,OADAigB,EAAA5b,KAAA,QAAArE,EAAA+c,GACAlY,EAAA7E,GAKA,OADAigB,EAAA5b,KAAA,OAAAiB,EAAAyX,GACAlY,EAAA,KAAAS,KAGA,MAAAtF,GAGA,OAFAigB,EAAA5b,KAAA,QAAArE,EAAA+c,GACA6D,WAAA,WAAA/b,EAAA7E,IAAA,GACArB,KASAkW,EAAA9Q,UAAAhD,IAAA,SAAA6gB,GAOA,OANA/d,KAAAwd,UACAO,GACA/d,KAAAwd,QAAA,KAAA,KAAA,MACAxd,KAAAwd,QAAA,KACAxd,KAAAQ,KAAA,OAAAH,OAEAL,kCC3IA3E,EAAAC,QAAA0V,EAGA,IAAA/D,EAAA7R,EAAA,MACA4V,EAAA9Q,UAAAnB,OAAA+N,OAAAG,EAAA/M,YAAA6M,YAAAiE,GAAAhE,UAAA,UAEA,IAAAiE,EAAA7V,EAAA,IACA0O,EAAA1O,EAAA,IACAqW,EAAArW,EAAA,IAWA,SAAA4V,EAAAhK,EAAAjG,GACAkM,EAAA3G,KAAAtG,KAAAgH,EAAAjG,GAMAf,KAAAyT,QAAA,GAOAzT,KAAAge,EAAA,KAyDA,SAAA9K,EAAA+F,GAEA,OADAA,EAAA+E,EAAA,KACA/E,EA1CAjI,EAAA1D,SAAA,SAAAtG,EAAAC,GACA,IAAAgS,EAAA,IAAAjI,EAAAhK,EAAAC,EAAAlG,SAEA,GAAAkG,EAAAwM,QACA,IAAA,IAAAD,EAAAzU,OAAAC,KAAAiI,EAAAwM,SAAA3W,EAAA,EAAAA,EAAA0W,EAAA5X,SAAAkB,EACAmc,EAAAtL,IAAAsD,EAAA3D,SAAAkG,EAAA1W,GAAAmK,EAAAwM,QAAAD,EAAA1W,MAIA,OAHAmK,EAAAC,QACA+R,EAAA7F,QAAAnM,EAAAC,QACA+R,EAAA/L,QAAAjG,EAAAiG,QACA+L,GAQAjI,EAAA9Q,UAAAsN,OAAA,SAAAC,GACA,IAAAwQ,EAAAhR,EAAA/M,UAAAsN,OAAAlH,KAAAtG,KAAAyN,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA5D,EAAAiB,SAAA,CACA,UAAAkT,GAAAA,EAAAld,SAAAjG,GACA,UAAAmS,EAAA6F,YAAA9S,KAAAke,aAAAzQ,IAAA,GACA,SAAAwQ,GAAAA,EAAA/W,QAAApM,GACA,UAAA4S,EAAA1N,KAAAkN,QAAApS,MAUAiE,OAAA+P,eAAAkC,EAAA9Q,UAAA,eAAA,CACAwJ,IAAA,WACA,OAAA1J,KAAAge,IAAAhe,KAAAge,EAAAlU,EAAAuJ,QAAArT,KAAAyT,aAYAzC,EAAA9Q,UAAAwJ,IAAA,SAAA1C,GACA,OAAAhH,KAAAyT,QAAAzM,IACAiG,EAAA/M,UAAAwJ,IAAApD,KAAAtG,KAAAgH,IAMAgK,EAAA9Q,UAAAiU,WAAA,WAEA,IADA,IAAAV,EAAAzT,KAAAke,aACAphB,EAAA,EAAAA,EAAA2W,EAAA7X,SAAAkB,EACA2W,EAAA3W,GAAAb,UACA,OAAAgR,EAAA/M,UAAAjE,QAAAqK,KAAAtG,OAMAgR,EAAA9Q,UAAAyN,IAAA,SAAA4E,GAGA,GAAAvS,KAAA0J,IAAA6I,EAAAvL,MACA,MAAA/I,MAAA,mBAAAsU,EAAAvL,KAAA,QAAAhH,MAEA,OAAAuS,aAAAtB,EAGAiC,GAFAlT,KAAAyT,QAAAlB,EAAAvL,MAAAuL,GACAnD,OAAApP,MAGAiN,EAAA/M,UAAAyN,IAAArH,KAAAtG,KAAAuS,IAMAvB,EAAA9Q,UAAA+N,OAAA,SAAAsE,GACA,GAAAA,aAAAtB,EAAA,CAGA,GAAAjR,KAAAyT,QAAAlB,EAAAvL,QAAAuL,EACA,MAAAtU,MAAAsU,EAAA,uBAAAvS,MAIA,cAFAA,KAAAyT,QAAAlB,EAAAvL,MACAuL,EAAAnD,OAAA,KACA8D,EAAAlT,MAEA,OAAAiN,EAAA/M,UAAA+N,OAAA3H,KAAAtG,KAAAuS,IAUAvB,EAAA9Q,UAAA4M,OAAA,SAAA0Q,EAAAC,EAAAC,GAEA,IADA,IACAxE,EADAiF,EAAA,IAAA1M,EAAAT,QAAAwM,EAAAC,EAAAC,GACA5gB,EAAA,EAAAA,EAAAkD,KAAAke,aAAAtiB,SAAAkB,EAAA,CACA,IAAAshB,EAAAtU,EAAA4P,SAAAR,EAAAlZ,KAAAge,EAAAlhB,IAAAb,UAAA+K,MAAAzH,QAAA,WAAA,IACA4e,EAAAC,GAAAtU,EAAA3L,QAAA,CAAA,IAAA,KAAA2L,EAAAuU,WAAAD,GAAAA,EAAA,IAAAA,EAAAtU,CAAA,iCAAAA,CAAA,CACAwU,EAAApF,EACAqF,EAAArF,EAAAvG,oBAAAhD,KACA6O,EAAAtF,EAAAtG,qBAAAjD,OAGA,OAAAwO,iDCpKA9iB,EAAAC,QAAAqW,EAEA,IAAA8M,EAAA,uBACAC,EAAA,kCACAC,EAAA,kCAEAC,EAAA,aACAC,EAAA,aACAC,EAAA,MACAC,EAAA,KACAC,EAAA,UAEAC,EAAA,CACAC,EAAA,KACAC,EAAA,KACA3iB,EAAA,KACAW,EAAA,MAUA,SAAAiiB,EAAAC,GACA,OAAAA,EAAA9f,QAAAyf,EAAA,SAAAxf,EAAAC,GACA,OAAAA,GACA,IAAA,KACA,IAAA,GACA,OAAAA,EACA,QACA,OAAAwf,EAAAxf,IAAA,MAgEA,SAAAkS,EAAAlT,EAAA+X,GAEA/X,EAAAA,EAAAC,WAEA,IAAA7C,EAAA,EACAD,EAAA6C,EAAA7C,OACAub,EAAA,EACAmI,EAAA,KACAC,EAAA,KACAC,EAAA,EACAC,GAAA,EAEAC,EAAA,GAEAC,EAAA,KASA,SAAA1I,EAAA2I,GACA,OAAA3hB,MAAA,WAAA2hB,EAAA,UAAAzI,EAAA,KA0BA,SAAA1a,EAAA+F,GACA,OAAA/D,EAAAhC,OAAA+F,GAUA,SAAAqd,EAAA5iB,EAAAC,GACAoiB,EAAA7gB,EAAAhC,OAAAQ,KACAuiB,EAAArI,EACAsI,GAAA,EAOA,IACA1hB,EADA+hB,EAAA7iB,GALAuZ,EACA,EAEA,GAIA,GACA,KAAAsJ,EAAA,GACA,QAAA/hB,EAAAU,EAAAhC,OAAAqjB,IAAA,CACAL,GAAA,EACA,aAEA,MAAA1hB,GAAA,OAAAA,GAIA,IAHA,IAAAgiB,EAAAthB,EACA8Y,UAAAta,EAAAC,GACAwI,MAAAoZ,GACAhiB,EAAA,EAAAA,EAAAijB,EAAAnkB,SAAAkB,EACAijB,EAAAjjB,GAAAijB,EAAAjjB,GACAyC,QAAAiX,EAAAqI,EAAAD,EAAA,IACAoB,OACAT,EAAAQ,EACAniB,KAAA,MACAoiB,OAGA,SAAAC,EAAAC,GACA,IAAAC,EAAAC,EAAAF,GAGAG,EAAA5hB,EAAA8Y,UAAA2I,EAAAC,GAIA,MADA,cAAAjiB,KAAAmiB,GAIA,SAAAD,EAAAE,GAGA,IADA,IAAAH,EAAAG,EACAH,EAAAvkB,GAAA,OAAAa,EAAA0jB,IACAA,IAEA,OAAAA,EAQA,SAAA1J,IACA,GAAA,EAAAiJ,EAAA9jB,OACA,OAAA8jB,EAAA7Z,QACA,GAAA8Z,EACA,OAzFA,WACA,IAAAY,EAAA,MAAAZ,EAAAhB,EAAAD,EACA6B,EAAAC,UAAA3kB,EAAA,EACA,IAAA4kB,EAAAF,EAAAG,KAAAjiB,GACA,IAAAgiB,EACA,MAAAxJ,EAAA,UAIA,OAHApb,EAAA0kB,EAAAC,UACAhjB,EAAAmiB,GACAA,EAAA,KACAP,EAAAqB,EAAA,IAgFArJ,GACA,IAAAuJ,EACAhN,EACAiN,EACA3jB,EACA4jB,EACA,EAAA,CACA,GAAAhlB,IAAAD,EACA,OAAA,KAEA,IADA+kB,GAAA,EACA5B,EAAA7gB,KAAA0iB,EAAAnkB,EAAAZ,KAGA,GAFA,OAAA+kB,KACAzJ,IACAtb,IAAAD,EACA,OAAA,KAGA,GAAA,MAAAa,EAAAZ,GAAA,CACA,KAAAA,IAAAD,EACA,MAAAqb,EAAA,WAEA,GAAA,MAAAxa,EAAAZ,GACA,GAAA2a,EAeA,CAIA,GADAqK,GAAA,EACAZ,EAFAhjB,EAAApB,GAEA,CACAglB,GAAA,EACA,EAAA,CAEA,IADAhlB,EAAAukB,EAAAvkB,MACAD,EACA,MAEAC,UACAokB,EAAApkB,SAEAA,EAAAa,KAAAqf,IAAAngB,EAAAwkB,EAAAvkB,GAAA,GAEAglB,GACAhB,EAAA5iB,EAAApB,GAEAsb,IACAwJ,GAAA,MAnCA,CAIA,IAFAE,EAAA,MAAApkB,EAAAQ,EAAApB,EAAA,GAEA,OAAAY,IAAAZ,IACA,GAAAA,IAAAD,EACA,OAAA,OAGAC,EACAglB,GACAhB,EAAA5iB,EAAApB,EAAA,KAEAsb,EACAwJ,GAAA,MAuBA,CAAA,GAAA,OAAAC,EAAAnkB,EAAAZ,IAoBA,MAAA,IAlBAoB,EAAApB,EAAA,EACAglB,EAAArK,GAAA,MAAA/Z,EAAAQ,GACA,EAAA,CAIA,GAHA,OAAA2jB,KACAzJ,IAEAtb,IAAAD,EACA,MAAAqb,EAAA,WAEAtD,EAAAiN,EACAA,EAAAnkB,EAAAZ,SACA,MAAA8X,GAAA,MAAAiN,KACA/kB,EACAglB,GACAhB,EAAA5iB,EAAApB,EAAA,GAEA8kB,GAAA,UAKAA,GAIA,IAAAzjB,EAAArB,EAGA,GAFA4iB,EAAA+B,UAAA,GACA/B,EAAAvgB,KAAAzB,EAAAS,MAEA,KAAAA,EAAAtB,IAAA6iB,EAAAvgB,KAAAzB,EAAAS,OACAA,EACA,IAAAoZ,EAAA7X,EAAA8Y,UAAA1b,EAAAA,EAAAqB,GAGA,MAFA,MAAAoZ,GAAA,MAAAA,IACAqJ,EAAArJ,GACAA,EASA,SAAA9Y,EAAA8Y,GACAoJ,EAAAliB,KAAA8Y,GAQA,SAAAI,IACA,IAAAgJ,EAAA9jB,OAAA,CACA,IAAA0a,EAAAG,IACA,GAAA,OAAAH,EACA,OAAA,KACA9Y,EAAA8Y,GAEA,OAAAoJ,EAAA,GA+CA,OAAA3gB,OAAA+P,eAAA,CACA2H,KAAAA,EACAC,KAAAA,EACAlZ,KAAAA,EACAmZ,KAxCA,SAAAmK,EAAAlU,GACA,IAAAmU,EAAArK,IAEA,GADAqK,IAAAD,EAGA,OADArK,KACA,EAEA,IAAA7J,EACA,MAAAqK,EAAA,UAAA8J,EAAA,OAAAD,EAAA,cACA,OAAA,GAgCAlK,KAvBA,SAAA6C,GACA,IAAAuH,EAAA,KAcA,OAbAvH,IAAA3e,GACA0kB,IAAArI,EAAA,IAAAX,GAAA,MAAA8I,GAAAG,KACAuB,EAAAzB,IAIAC,EAAA/F,GACA/C,IAEA8I,IAAA/F,GAAAgG,IAAAjJ,GAAA,MAAA8I,IACA0B,EAAAzB,IAGAyB,IASA,OAAA,CACAtX,IAAA,WAAA,OAAAyN,KAlWAxF,EAAAyN,SAAAA,yBCtCA/jB,EAAAC,QAAA6S,EAGA,IAAAlB,EAAA7R,EAAA,MACA+S,EAAAjO,UAAAnB,OAAA+N,OAAAG,EAAA/M,YAAA6M,YAAAoB,GAAAnB,UAAA,OAEA,IAAAnD,EAAAzO,EAAA,IACA0V,EAAA1V,EAAA,IACA8S,EAAA9S,EAAA,IACA2V,EAAA3V,EAAA,IACA4V,EAAA5V,EAAA,IACA8V,EAAA9V,EAAA,IACAiW,EAAAjW,EAAA,IACAmW,EAAAnW,EAAA,IACA0O,EAAA1O,EAAA,IACAuV,EAAAvV,EAAA,IACAwV,EAAAxV,EAAA,IACAyV,EAAAzV,EAAA,IACAwO,EAAAxO,EAAA,IACA+V,EAAA/V,EAAA,IAUA,SAAA+S,EAAAnH,EAAAjG,GACAkM,EAAA3G,KAAAtG,KAAAgH,EAAAjG,GAMAf,KAAAqH,OAAA,GAMArH,KAAAiI,OAAAnN,GAMAkF,KAAA4Y,WAAA9d,GAMAkF,KAAAqN,SAAAvS,GAMAkF,KAAAkM,MAAApR,GAOAkF,KAAAihB,EAAA,KAOAjhB,KAAA+L,EAAA,KAOA/L,KAAAkhB,EAAA,KAOAlhB,KAAAmhB,EAAA,KA0HA,SAAAjO,EAAA3L,GAKA,OAJAA,EAAA0Z,EAAA1Z,EAAAwE,EAAAxE,EAAA2Z,EAAA,YACA3Z,EAAAxK,cACAwK,EAAAzJ,cACAyJ,EAAA+K,OACA/K,EA5HAxI,OAAA6V,iBAAAzG,EAAAjO,UAAA,CAQAkhB,WAAA,CACA1X,IAAA,WAGA,GAAA1J,KAAAihB,EACA,OAAAjhB,KAAAihB,EAEAjhB,KAAAihB,EAAA,GACA,IAAA,IAAAzN,EAAAzU,OAAAC,KAAAgB,KAAAqH,QAAAvK,EAAA,EAAAA,EAAA0W,EAAA5X,SAAAkB,EAAA,CACA,IAAAmN,EAAAjK,KAAAqH,OAAAmM,EAAA1W,IACA0K,EAAAyC,EAAAzC,GAGA,GAAAxH,KAAAihB,EAAAzZ,GACA,MAAAvJ,MAAA,gBAAAuJ,EAAA,OAAAxH,MAEAA,KAAAihB,EAAAzZ,GAAAyC,EAEA,OAAAjK,KAAAihB,IAUArW,YAAA,CACAlB,IAAA,WACA,OAAA1J,KAAA+L,IAAA/L,KAAA+L,EAAAjC,EAAAuJ,QAAArT,KAAAqH,WAUAga,YAAA,CACA3X,IAAA,WACA,OAAA1J,KAAAkhB,IAAAlhB,KAAAkhB,EAAApX,EAAAuJ,QAAArT,KAAAiI,WAUA0H,KAAA,CACAjG,IAAA,WACA,OAAA1J,KAAAmhB,IAAAnhB,KAAA2P,KAAAxB,EAAAmT,oBAAAthB,KAAAmO,KAEAkH,IAAA,SAAA1F,GAGA,IAAAzP,EAAAyP,EAAAzP,UACAA,aAAAgR,KACAvB,EAAAzP,UAAA,IAAAgR,GAAAnE,YAAA4C,EACA7F,EAAAyR,MAAA5L,EAAAzP,UAAAA,IAIAyP,EAAAsC,MAAAtC,EAAAzP,UAAA+R,MAAAjS,KAGA8J,EAAAyR,MAAA5L,EAAAuB,GAAA,GAEAlR,KAAAmhB,EAAAxR,EAIA,IADA,IAAA7S,EAAA,EACAA,EAAAkD,KAAA4K,YAAAhP,SAAAkB,EACAkD,KAAA+L,EAAAjP,GAAAb,UAGA,IAAAslB,EAAA,GACA,IAAAzkB,EAAA,EAAAA,EAAAkD,KAAAqhB,YAAAzlB,SAAAkB,EACAykB,EAAAvhB,KAAAkhB,EAAApkB,GAAAb,UAAA+K,MAAA,CACA0C,IAAAI,EAAAsL,YAAApV,KAAAkhB,EAAApkB,GAAAqL,OACAkN,IAAAvL,EAAAwL,YAAAtV,KAAAkhB,EAAApkB,GAAAqL,QAEArL,GACAiC,OAAA6V,iBAAAjF,EAAAzP,UAAAqhB,OAUApT,EAAAmT,oBAAA,SAAA3W,GAIA,IAFA,IAEAV,EAFAD,EAAAF,EAAA3L,QAAA,CAAA,KAAAwM,EAAA3D,MAEAlK,EAAA,EAAAA,EAAA6N,EAAAC,YAAAhP,SAAAkB,GACAmN,EAAAU,EAAAoB,EAAAjP,IAAAgO,IAAAd,EACA,YAAAF,EAAAe,SAAAZ,EAAAjD,OACAiD,EAAAI,UAAAL,EACA,YAAAF,EAAAe,SAAAZ,EAAAjD,OACA,OAAAgD,EACA,wEADAA,CAEA,yBA6BAmE,EAAAb,SAAA,SAAAtG,EAAAC,GACA,IAAAM,EAAA,IAAA4G,EAAAnH,EAAAC,EAAAlG,SACAwG,EAAAqR,WAAA3R,EAAA2R,WACArR,EAAA8F,SAAApG,EAAAoG,SAGA,IAFA,IAAAmG,EAAAzU,OAAAC,KAAAiI,EAAAI,QACAvK,EAAA,EACAA,EAAA0W,EAAA5X,SAAAkB,EACAyK,EAAAoG,UACA,IAAA1G,EAAAI,OAAAmM,EAAA1W,IAAAiL,QACAgJ,EAAAzD,SACAY,EAAAZ,UAAAkG,EAAA1W,GAAAmK,EAAAI,OAAAmM,EAAA1W,MAEA,GAAAmK,EAAAgB,OACA,IAAAuL,EAAAzU,OAAAC,KAAAiI,EAAAgB,QAAAnL,EAAA,EAAAA,EAAA0W,EAAA5X,SAAAkB,EACAyK,EAAAoG,IAAAmD,EAAAxD,SAAAkG,EAAA1W,GAAAmK,EAAAgB,OAAAuL,EAAA1W,MACA,GAAAmK,EAAAC,OACA,IAAAsM,EAAAzU,OAAAC,KAAAiI,EAAAC,QAAApK,EAAA,EAAAA,EAAA0W,EAAA5X,SAAAkB,EAAA,CACA,IAAAoK,EAAAD,EAAAC,OAAAsM,EAAA1W,IACAyK,EAAAoG,KACAzG,EAAAM,KAAA1M,GACAoT,EAAAZ,SACApG,EAAAG,SAAAvM,GACAqT,EAAAb,SACApG,EAAAyB,SAAA7N,GACA+O,EAAAyD,SACApG,EAAAuM,UAAA3Y,GACAkW,EAAA1D,SACAL,EAAAK,UAAAkG,EAAA1W,GAAAoK,IAWA,OARAD,EAAA2R,YAAA3R,EAAA2R,WAAAhd,SACA2L,EAAAqR,WAAA3R,EAAA2R,YACA3R,EAAAoG,UAAApG,EAAAoG,SAAAzR,SACA2L,EAAA8F,SAAApG,EAAAoG,UACApG,EAAAiF,QACA3E,EAAA2E,OAAA,GACAjF,EAAAiG,UACA3F,EAAA2F,QAAAjG,EAAAiG,SACA3F,GAQA4G,EAAAjO,UAAAsN,OAAA,SAAAC,GACA,IAAAwQ,EAAAhR,EAAA/M,UAAAsN,OAAAlH,KAAAtG,KAAAyN,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAA5D,EAAAiB,SAAA,CACA,UAAAkT,GAAAA,EAAAld,SAAAjG,GACA,SAAAmS,EAAA6F,YAAA9S,KAAAqhB,YAAA5T,GACA,SAAAR,EAAA6F,YAAA9S,KAAA4K,YAAAqB,OAAA,SAAA+G,GAAA,OAAAA,EAAApE,iBAAAnB,IAAA,GACA,aAAAzN,KAAA4Y,YAAA5Y,KAAA4Y,WAAAhd,OAAAoE,KAAA4Y,WAAA9d,GACA,WAAAkF,KAAAqN,UAAArN,KAAAqN,SAAAzR,OAAAoE,KAAAqN,SAAAvS,GACA,QAAAkF,KAAAkM,OAAApR,GACA,SAAAmjB,GAAAA,EAAA/W,QAAApM,GACA,UAAA4S,EAAA1N,KAAAkN,QAAApS,MAOAqT,EAAAjO,UAAAiU,WAAA,WAEA,IADA,IAAA9M,EAAArH,KAAA4K,YAAA9N,EAAA,EACAA,EAAAuK,EAAAzL,QACAyL,EAAAvK,KAAAb,UACA,IAAAgM,EAAAjI,KAAAqhB,YACA,IADAvkB,EAAA,EACAA,EAAAmL,EAAArM,QACAqM,EAAAnL,KAAAb,UACA,OAAAgR,EAAA/M,UAAAiU,WAAA7N,KAAAtG,OAMAmO,EAAAjO,UAAAwJ,IAAA,SAAA1C,GACA,OAAAhH,KAAAqH,OAAAL,IACAhH,KAAAiI,QAAAjI,KAAAiI,OAAAjB,IACAhH,KAAAkH,QAAAlH,KAAAkH,OAAAF,IACA,MAUAmH,EAAAjO,UAAAyN,IAAA,SAAA4E,GAEA,GAAAvS,KAAA0J,IAAA6I,EAAAvL,MACA,MAAA/I,MAAA,mBAAAsU,EAAAvL,KAAA,QAAAhH,MAEA,GAAAuS,aAAArE,GAAAqE,EAAAlE,SAAAvT,GAAA,CAMA,GAAAkF,KAAAihB,EAAAjhB,KAAAihB,EAAA1O,EAAA/K,IAAAxH,KAAAohB,WAAA7O,EAAA/K,IACA,MAAAvJ,MAAA,gBAAAsU,EAAA/K,GAAA,OAAAxH,MACA,GAAAA,KAAA8N,aAAAyE,EAAA/K,IACA,MAAAvJ,MAAA,MAAAsU,EAAA/K,GAAA,mBAAAxH,MACA,GAAAA,KAAA+N,eAAAwE,EAAAvL,MACA,MAAA/I,MAAA,SAAAsU,EAAAvL,KAAA,oBAAAhH,MAOA,OALAuS,EAAAnD,QACAmD,EAAAnD,OAAAnB,OAAAsE,IACAvS,KAAAqH,OAAAkL,EAAAvL,MAAAuL,GACA/D,QAAAxO,KACAuS,EAAAsB,MAAA7T,MACAkT,EAAAlT,MAEA,OAAAuS,aAAAzB,GACA9Q,KAAAiI,SACAjI,KAAAiI,OAAA,KACAjI,KAAAiI,OAAAsK,EAAAvL,MAAAuL,GACAsB,MAAA7T,MACAkT,EAAAlT,OAEAiN,EAAA/M,UAAAyN,IAAArH,KAAAtG,KAAAuS,IAUApE,EAAAjO,UAAA+N,OAAA,SAAAsE,GACA,GAAAA,aAAArE,GAAAqE,EAAAlE,SAAAvT,GAAA,CAIA,IAAAkF,KAAAqH,QAAArH,KAAAqH,OAAAkL,EAAAvL,QAAAuL,EACA,MAAAtU,MAAAsU,EAAA,uBAAAvS,MAKA,cAHAA,KAAAqH,OAAAkL,EAAAvL,MACAuL,EAAAnD,OAAA,KACAmD,EAAAuB,SAAA9T,MACAkT,EAAAlT,MAEA,GAAAuS,aAAAzB,EAAA,CAGA,IAAA9Q,KAAAiI,QAAAjI,KAAAiI,OAAAsK,EAAAvL,QAAAuL,EACA,MAAAtU,MAAAsU,EAAA,uBAAAvS,MAKA,cAHAA,KAAAiI,OAAAsK,EAAAvL,MACAuL,EAAAnD,OAAA,KACAmD,EAAAuB,SAAA9T,MACAkT,EAAAlT,MAEA,OAAAiN,EAAA/M,UAAA+N,OAAA3H,KAAAtG,KAAAuS,IAQApE,EAAAjO,UAAA4N,aAAA,SAAAtG,GACA,OAAAyF,EAAAa,aAAA9N,KAAAqN,SAAA7F,IAQA2G,EAAAjO,UAAA6N,eAAA,SAAA/G,GACA,OAAAiG,EAAAc,eAAA/N,KAAAqN,SAAArG,IAQAmH,EAAAjO,UAAA4M,OAAA,SAAAkF,GACA,OAAA,IAAAhS,KAAA2P,KAAAqC,IAOA7D,EAAAjO,UAAAshB,MAAA,WAMA,IAFA,IAAAjX,EAAAvK,KAAAuK,SACA6B,EAAA,GACAtP,EAAA,EAAAA,EAAAkD,KAAA4K,YAAAhP,SAAAkB,EACAsP,EAAA5O,KAAAwC,KAAA+L,EAAAjP,GAAAb,UAAAmO,cAGApK,KAAAjD,OAAA4T,EAAA3Q,KAAA2Q,CAAA,CACAY,OAAAA,EACAnF,MAAAA,EACAtC,KAAAA,IAEA9J,KAAAlC,OAAA8S,EAAA5Q,KAAA4Q,CAAA,CACAS,OAAAA,EACAjF,MAAAA,EACAtC,KAAAA,IAEA9J,KAAAsS,OAAAzB,EAAA7Q,KAAA6Q,CAAA,CACAzE,MAAAA,EACAtC,KAAAA,IAEA9J,KAAA0K,WAAAd,EAAAc,WAAA1K,KAAA4J,CAAA,CACAwC,MAAAA,EACAtC,KAAAA,IAEA9J,KAAA+K,SAAAnB,EAAAmB,SAAA/K,KAAA4J,CAAA,CACAwC,MAAAA,EACAtC,KAAAA,IAIA,IAAA2X,EAAAtQ,EAAA5G,GACA,GAAAkX,EAAA,CACA,IAAAC,EAAA3iB,OAAA+N,OAAA9M,MAEA0hB,EAAAhX,WAAA1K,KAAA0K,WACA1K,KAAA0K,WAAA+W,EAAA/W,WAAA5G,KAAA4d,GAGAA,EAAA3W,SAAA/K,KAAA+K,SACA/K,KAAA+K,SAAA0W,EAAA1W,SAAAjH,KAAA4d,GAIA,OAAA1hB,MASAmO,EAAAjO,UAAAnD,OAAA,SAAAyR,EAAA0D,GACA,OAAAlS,KAAAwhB,QAAAzkB,OAAAyR,EAAA0D,IASA/D,EAAAjO,UAAAiS,gBAAA,SAAA3D,EAAA0D,GACA,OAAAlS,KAAAjD,OAAAyR,EAAA0D,GAAAA,EAAA1L,IAAA0L,EAAAyP,OAAAzP,GAAA0P,UAWAzT,EAAAjO,UAAApC,OAAA,SAAAsU,EAAAxW,GACA,OAAAoE,KAAAwhB,QAAA1jB,OAAAsU,EAAAxW,IAUAuS,EAAAjO,UAAAmS,gBAAA,SAAAD,GAGA,OAFAA,aAAAf,IACAe,EAAAf,EAAAvE,OAAAsF,IACApS,KAAAlC,OAAAsU,EAAAA,EAAAyI,WAQA1M,EAAAjO,UAAAoS,OAAA,SAAA9D,GACA,OAAAxO,KAAAwhB,QAAAlP,OAAA9D,IAQAL,EAAAjO,UAAAwK,WAAA,SAAA6H,GACA,OAAAvS,KAAAwhB,QAAA9W,WAAA6H,IA4BApE,EAAAjO,UAAA6K,SAAA,SAAAyD,EAAAzN,GACA,OAAAf,KAAAwhB,QAAAzW,SAAAyD,EAAAzN,IAkBAoN,EAAAyB,EAAA,SAAAiS,GACA,OAAA,SAAAjK,GACA9N,EAAAkG,aAAA4H,EAAAiK,uHCpkBA,IAAAzV,EAAA9Q,EAEAwO,EAAA1O,EAAA,IAEAojB,EAAA,CACA,SACA,QACA,QACA,SACA,SACA,UACA,WACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,SAGA,SAAAsD,EAAAnZ,EAAA9M,GACA,IAAAiB,EAAA,EAAAilB,EAAA,GAEA,IADAlmB,GAAA,EACAiB,EAAA6L,EAAA/M,QAAAmmB,EAAAvD,EAAA1hB,EAAAjB,IAAA8M,EAAA7L,KACA,OAAAilB,EAuBA3V,EAAAC,MAAAyV,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,IAwBA1V,EAAA+C,SAAA2S,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,GACA,EACA,GACAhY,EAAA4F,WACA,OAaAtD,EAAAb,KAAAuW,EAAA,CACA,EACA,EACA,EACA,EACA,GACA,GAmBA1V,EAAAM,OAAAoV,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,GACA,GAoBA1V,EAAAE,OAAAwV,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,gCC5LA,IAIA3T,EACAtE,EALAC,EAAAzO,EAAAC,QAAAF,EAAA,IAEAsW,EAAAtW,EAAA,IAKA0O,EAAA3L,QAAA/C,EAAA,GACA0O,EAAApJ,MAAAtF,EAAA,GACA0O,EAAAvE,KAAAnK,EAAA,GAMA0O,EAAAlJ,GAAAkJ,EAAAjJ,QAAA,MAOAiJ,EAAAuJ,QAAA,SAAAd,GACA,GAAAA,EAAA,CAIA,IAHA,IAAAvT,EAAAD,OAAAC,KAAAuT,GACAQ,EAAArX,MAAAsD,EAAApD,QACAE,EAAA,EACAA,EAAAkD,EAAApD,QACAmX,EAAAjX,GAAAyW,EAAAvT,EAAAlD,MACA,OAAAiX,EAEA,MAAA,IAQAjJ,EAAAiB,SAAA,SAAAgI,GAGA,IAFA,IAAAR,EAAA,GACAzW,EAAA,EACAA,EAAAiX,EAAAnX,QAAA,CACA,IAAAomB,EAAAjP,EAAAjX,KACAwG,EAAAyQ,EAAAjX,KACAwG,IAAAxH,KACAyX,EAAAyP,GAAA1f,GAEA,OAAAiQ,GAGA,IAAA0P,EAAA,MACAC,EAAA,KAOApY,EAAAuU,WAAA,SAAArX,GACA,MAAA,uTAAA9I,KAAA8I,IAQA8C,EAAAe,SAAA,SAAAV,GACA,OAAA,YAAAjM,KAAAiM,IAAAL,EAAAuU,WAAAlU,GACA,KAAAA,EAAA5K,QAAA0iB,EAAA,QAAA1iB,QAAA2iB,EAAA,OAAA,KACA,IAAA/X,GAQAL,EAAA6P,QAAA,SAAA0F,GACA,OAAAA,EAAA5iB,OAAA,GAAA0lB,cAAA9C,EAAA9H,UAAA,IAGA,IAAA6K,EAAA,YAOAtY,EAAAkN,UAAA,SAAAqI,GACA,OAAAA,EAAA9H,UAAA,EAAA,GACA8H,EAAA9H,UAAA,GACAhY,QAAA6iB,EAAA,SAAA5iB,EAAAC,GAAA,OAAAA,EAAA0iB,iBASArY,EAAAmB,kBAAA,SAAAoX,EAAA9kB,GACA,OAAA8kB,EAAA7a,GAAAjK,EAAAiK,IAWAsC,EAAAkG,aAAA,SAAAL,EAAAkS,GAGA,GAAAlS,EAAAsC,MAMA,OALA4P,GAAAlS,EAAAsC,MAAAjL,OAAA6a,IACA/X,EAAAwY,aAAArU,OAAA0B,EAAAsC,OACAtC,EAAAsC,MAAAjL,KAAA6a,EACA/X,EAAAwY,aAAA3U,IAAAgC,EAAAsC,QAEAtC,EAAAsC,MAIA9D,IACAA,EAAA/S,EAAA,KAEA,IAAAmM,EAAA,IAAA4G,EAAA0T,GAAAlS,EAAA3I,MAKA,OAJA8C,EAAAwY,aAAA3U,IAAApG,GACAA,EAAAoI,KAAAA,EACA5Q,OAAA+P,eAAAa,EAAA,QAAA,CAAAjQ,MAAA6H,EAAAgb,YAAA,IACAxjB,OAAA+P,eAAAa,EAAAzP,UAAA,QAAA,CAAAR,MAAA6H,EAAAgb,YAAA,IACAhb,GAGA,IAAAib,EAAA,EAOA1Y,EAAAmG,aAAA,SAAAsC,GAGA,GAAAA,EAAAN,MACA,OAAAM,EAAAN,MAGApI,IACAA,EAAAzO,EAAA,KAEA,IAAAmS,EAAA,IAAA1D,EAAA,OAAA2Y,IAAAjQ,GAGA,OAFAzI,EAAAwY,aAAA3U,IAAAJ,GACAxO,OAAA+P,eAAAyD,EAAA,QAAA,CAAA7S,MAAA6N,EAAAgV,YAAA,IACAhV,GASAxO,OAAA+P,eAAAhF,EAAA,eAAA,CACAJ,IAAA,WACA,OAAAgI,EAAA,YAAAA,EAAA,UAAA,IAAAtW,EAAA,yEC9KAC,EAAAC,QAAA0e,EAEA,IAAAlQ,EAAA1O,EAAA,IAUA,SAAA4e,EAAA/U,EAAAC,GASAlF,KAAAiF,GAAAA,IAAA,EAMAjF,KAAAkF,GAAAA,IAAA,EAQA,IAAAud,EAAAzI,EAAAyI,KAAA,IAAAzI,EAAA,EAAA,GAEAyI,EAAA9W,SAAA,WAAA,OAAA,GACA8W,EAAAC,SAAAD,EAAA9G,SAAA,WAAA,OAAA3b,MACAyiB,EAAA7mB,OAAA,WAAA,OAAA,GAOA,IAAA+mB,EAAA3I,EAAA2I,SAAA,mBAOA3I,EAAA1K,WAAA,SAAA5P,GACA,GAAA,IAAAA,EACA,OAAA+iB,EACA,IAAAvf,EAAAxD,EAAA,EACAwD,IACAxD,GAAAA,GACA,IAAAuF,EAAAvF,IAAA,EACAwF,GAAAxF,EAAAuF,GAAA,aAAA,EAUA,OATA/B,IACAgC,GAAAA,IAAA,EACAD,GAAAA,IAAA,EACA,aAAAA,IACAA,EAAA,EACA,aAAAC,IACAA,EAAA,KAGA,IAAA8U,EAAA/U,EAAAC,IAQA8U,EAAA4I,KAAA,SAAAljB,GACA,GAAA,iBAAAA,EACA,OAAAsa,EAAA1K,WAAA5P,GACA,GAAAoK,EAAA8D,SAAAlO,GAAA,CAEA,IAAAoK,EAAA4E,KAGA,OAAAsL,EAAA1K,WAAAkI,SAAA9X,EAAA,KAFAA,EAAAoK,EAAA4E,KAAAmU,WAAAnjB,GAIA,OAAAA,EAAA8L,KAAA9L,EAAA+L,KAAA,IAAAuO,EAAAta,EAAA8L,MAAA,EAAA9L,EAAA+L,OAAA,GAAAgX,GAQAzI,EAAA9Z,UAAAyL,SAAA,SAAAD,GACA,IAAAA,GAAA1L,KAAAkF,KAAA,GAAA,CACA,IAAAD,EAAA,GAAAjF,KAAAiF,KAAA,EACAC,GAAAlF,KAAAkF,KAAA,EAGA,OAFAD,IACAC,EAAAA,EAAA,IAAA,KACAD,EAAA,WAAAC,GAEA,OAAAlF,KAAAiF,GAAA,WAAAjF,KAAAkF,IAQA8U,EAAA9Z,UAAA4iB,OAAA,SAAApX,GACA,OAAA5B,EAAA4E,KACA,IAAA5E,EAAA4E,KAAA,EAAA1O,KAAAiF,GAAA,EAAAjF,KAAAkF,KAAAwG,GAEA,CAAAF,IAAA,EAAAxL,KAAAiF,GAAAwG,KAAA,EAAAzL,KAAAkF,GAAAwG,WAAAA,IAGA,IAAA1N,EAAAP,OAAAyC,UAAAlC,WAOAgc,EAAA+I,SAAA,SAAAC,GACA,OAAAA,IAAAL,EACAF,EACA,IAAAzI,GACAhc,EAAAsI,KAAA0c,EAAA,GACAhlB,EAAAsI,KAAA0c,EAAA,IAAA,EACAhlB,EAAAsI,KAAA0c,EAAA,IAAA,GACAhlB,EAAAsI,KAAA0c,EAAA,IAAA,MAAA,GAEAhlB,EAAAsI,KAAA0c,EAAA,GACAhlB,EAAAsI,KAAA0c,EAAA,IAAA,EACAhlB,EAAAsI,KAAA0c,EAAA,IAAA,GACAhlB,EAAAsI,KAAA0c,EAAA,IAAA,MAAA,IAQAhJ,EAAA9Z,UAAA+iB,OAAA,WACA,OAAAxlB,OAAAC,aACA,IAAAsC,KAAAiF,GACAjF,KAAAiF,KAAA,EAAA,IACAjF,KAAAiF,KAAA,GAAA,IACAjF,KAAAiF,KAAA,GACA,IAAAjF,KAAAkF,GACAlF,KAAAkF,KAAA,EAAA,IACAlF,KAAAkF,KAAA,GAAA,IACAlF,KAAAkF,KAAA,KAQA8U,EAAA9Z,UAAAwiB,SAAA,WACA,IAAAQ,EAAAljB,KAAAkF,IAAA,GAGA,OAFAlF,KAAAkF,KAAAlF,KAAAkF,IAAA,EAAAlF,KAAAiF,KAAA,IAAAie,KAAA,EACAljB,KAAAiF,IAAAjF,KAAAiF,IAAA,EAAAie,KAAA,EACAljB,MAOAga,EAAA9Z,UAAAyb,SAAA,WACA,IAAAuH,IAAA,EAAAljB,KAAAiF,IAGA,OAFAjF,KAAAiF,KAAAjF,KAAAiF,KAAA,EAAAjF,KAAAkF,IAAA,IAAAge,KAAA,EACAljB,KAAAkF,IAAAlF,KAAAkF,KAAA,EAAAge,KAAA,EACAljB,MAOAga,EAAA9Z,UAAAtE,OAAA,WACA,IAAAunB,EAAAnjB,KAAAiF,GACAme,GAAApjB,KAAAiF,KAAA,GAAAjF,KAAAkF,IAAA,KAAA,EACAme,EAAArjB,KAAAkF,KAAA,GACA,OAAA,IAAAme,EACA,IAAAD,EACAD,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,IAAA,EAAA,kCCrMA,IAAAvZ,EAAAxO,EAoOA,SAAAigB,EAAA+H,EAAAC,EAAAtU,GACA,IAAA,IAAAjQ,EAAAD,OAAAC,KAAAukB,GAAAzmB,EAAA,EAAAA,EAAAkC,EAAApD,SAAAkB,EACAwmB,EAAAtkB,EAAAlC,MAAAhC,IAAAmU,IACAqU,EAAAtkB,EAAAlC,IAAAymB,EAAAvkB,EAAAlC,KACA,OAAAwmB,EAoBA,SAAAE,EAAAxc,GAEA,SAAAyc,EAAAjV,EAAAwD,GAEA,KAAAhS,gBAAAyjB,GACA,OAAA,IAAAA,EAAAjV,EAAAwD,GAKAjT,OAAA+P,eAAA9O,KAAA,UAAA,CAAA0J,IAAA,WAAA,OAAA8E,KAGAvQ,MAAAylB,kBACAzlB,MAAAylB,kBAAA1jB,KAAAyjB,GAEA1kB,OAAA+P,eAAA9O,KAAA,QAAA,CAAAN,MAAAzB,QAAAyhB,OAAA,KAEA1N,GACAuJ,EAAAvb,KAAAgS,GAWA,OARAyR,EAAAvjB,UAAAnB,OAAA+N,OAAA7O,MAAAiC,YAAA6M,YAAA0W,EAEA1kB,OAAA+P,eAAA2U,EAAAvjB,UAAA,OAAA,CAAAwJ,IAAA,WAAA,OAAA1C,KAEAyc,EAAAvjB,UAAAxB,SAAA,WACA,OAAAsB,KAAAgH,KAAA,KAAAhH,KAAAwO,SAGAiV,EAvRA3Z,EAAAnJ,UAAAvF,EAAA,GAGA0O,EAAAzN,OAAAjB,EAAA,GAGA0O,EAAA/J,aAAA3E,EAAA,GAGA0O,EAAAqR,MAAA/f,EAAA,GAGA0O,EAAAjJ,QAAAzF,EAAA,GAGA0O,EAAAvD,KAAAnL,EAAA,IAGA0O,EAAA6Z,KAAAvoB,EAAA,GAGA0O,EAAAkQ,SAAA5e,EAAA,IAGA0O,EAAA8Z,OAAA,oBAAAC,QAAAA,QACA,oBAAAD,QAAAA,QACA,oBAAAxH,MAAAA,MACApc,KAQA8J,EAAA4F,WAAA3Q,OAAAwQ,OAAAxQ,OAAAwQ,OAAA,IAAA,GAOAzF,EAAA2F,YAAA1Q,OAAAwQ,OAAAxQ,OAAAwQ,OAAA,IAAA,GAQAzF,EAAAmT,UAAAnT,EAAA8Z,OAAApH,SAAA1S,EAAA8Z,OAAApH,QAAAsH,UAAAha,EAAA8Z,OAAApH,QAAAsH,SAAAC,MAQAja,EAAA+D,UAAAmW,OAAAnW,WAAA,SAAAnO,GACA,MAAA,iBAAAA,GAAAukB,SAAAvkB,IAAAhD,KAAAiD,MAAAD,KAAAA,GAQAoK,EAAA8D,SAAA,SAAAlO,GACA,MAAA,iBAAAA,GAAAA,aAAAjC,QAQAqM,EAAAwE,SAAA,SAAA5O,GACA,OAAAA,GAAA,iBAAAA,GAWAoK,EAAAoa,MAQApa,EAAAqa,MAAA,SAAAnR,EAAA7I,GACA,IAAAzK,EAAAsT,EAAA7I,GACA,QAAA,MAAAzK,IAAAsT,EAAAoR,eAAAja,MACA,iBAAAzK,GAAA,GAAAhE,MAAAsY,QAAAtU,GAAAA,EAAA9D,OAAAmD,OAAAC,KAAAU,GAAA9D,UAeAkO,EAAA2Q,OAAA,WACA,IACA,IAAAA,EAAA3Q,EAAAjJ,QAAA,UAAA4Z,OAEA,OAAAA,EAAAva,UAAAmkB,UAAA5J,EAAA,KACA,MAAAnV,GAEA,OAAA,MAPA,GAYAwE,EAAAwa,EAAA,KAGAxa,EAAAya,EAAA,KAOAza,EAAA0F,UAAA,SAAAgV,GAEA,MAAA,iBAAAA,EACA1a,EAAA2Q,OACA3Q,EAAAya,EAAAC,GACA,IAAA1a,EAAApO,MAAA8oB,GACA1a,EAAA2Q,OACA3Q,EAAAwa,EAAAE,GACA,oBAAA7iB,WACA6iB,EACA,IAAA7iB,WAAA6iB,IAOA1a,EAAApO,MAAA,oBAAAiG,WAAAA,WAAAjG,MAeAoO,EAAA4E,KAAA5E,EAAA8Z,OAAAa,SAAA3a,EAAA8Z,OAAAa,QAAA/V,MACA5E,EAAA8Z,OAAAlV,MACA5E,EAAAjJ,QAAA,QAOAiJ,EAAA4a,OAAA,mBAOA5a,EAAA6a,QAAA,wBAOA7a,EAAA8a,QAAA,6CAOA9a,EAAA+a,WAAA,SAAAnlB,GACA,OAAAA,EACAoK,EAAAkQ,SAAA4I,KAAAljB,GAAAujB,SACAnZ,EAAAkQ,SAAA2I,UASA7Y,EAAAgb,aAAA,SAAA9B,EAAAtX,GACA,IAAA4O,EAAAxQ,EAAAkQ,SAAA+I,SAAAC,GACA,OAAAlZ,EAAA4E,KACA5E,EAAA4E,KAAAqW,SAAAzK,EAAArV,GAAAqV,EAAApV,GAAAwG,GACA4O,EAAA3O,WAAAD,IAkBA5B,EAAAyR,MAAAA,EAOAzR,EAAA4P,QAAA,SAAA2F,GACA,OAAAA,EAAA5iB,OAAA,GAAA8R,cAAA8Q,EAAA9H,UAAA,IA0CAzN,EAAA0Z,SAAAA,EAmBA1Z,EAAAkb,cAAAxB,EAAA,iBAoBA1Z,EAAAsL,YAAA,SAAAH,GAEA,IADA,IAAAgQ,EAAA,GACAnoB,EAAA,EAAAA,EAAAmY,EAAArZ,SAAAkB,EACAmoB,EAAAhQ,EAAAnY,IAAA,EAOA,OAAA,WACA,IAAA,IAAAkC,EAAAD,OAAAC,KAAAgB,MAAAlD,EAAAkC,EAAApD,OAAA,GAAA,EAAAkB,IAAAA,EACA,GAAA,IAAAmoB,EAAAjmB,EAAAlC,KAAAkD,KAAAhB,EAAAlC,MAAAhC,IAAA,OAAAkF,KAAAhB,EAAAlC,IACA,OAAAkC,EAAAlC,KAiBAgN,EAAAwL,YAAA,SAAAL,GAQA,OAAA,SAAAjO,GACA,IAAA,IAAAlK,EAAA,EAAAA,EAAAmY,EAAArZ,SAAAkB,EACAmY,EAAAnY,KAAAkK,UACAhH,KAAAiV,EAAAnY,MAoBAgN,EAAA2D,cAAA,CACAyX,MAAAznB,OACA0nB,MAAA1nB,OACAmO,MAAAnO,OACAwJ,MAAA,GAIA6C,EAAAsG,EAAA,WACA,IAAAqK,EAAA3Q,EAAA2Q,OAEAA,GAMA3Q,EAAAwa,EAAA7J,EAAAmI,OAAAjhB,WAAAihB,MAAAnI,EAAAmI,MAEA,SAAAljB,EAAA0lB,GACA,OAAA,IAAA3K,EAAA/a,EAAA0lB,IAEAtb,EAAAya,EAAA9J,EAAA4K,aAEA,SAAAnf,GACA,OAAA,IAAAuU,EAAAvU,KAbA4D,EAAAwa,EAAAxa,EAAAya,EAAA,gEC7YAlpB,EAAAC,QAwHA,SAAAqP,GAGA,IAAAX,EAAAF,EAAA3L,QAAA,CAAA,KAAAwM,EAAA3D,KAAA,UAAA8C,CACA,oCADAA,CAEA,WAAA,mBACA7B,EAAA0C,EAAA0W,YACAiE,EAAA,GACArd,EAAArM,QAAAoO,EACA,YAEA,IAAA,IAAAlN,EAAA,EAAAA,EAAA6N,EAAAC,YAAAhP,SAAAkB,EAAA,CACA,IAAAmN,EAAAU,EAAAoB,EAAAjP,GAAAb,UACAkQ,EAAA,IAAArC,EAAAe,SAAAZ,EAAAjD,MAMA,GAJAiD,EAAA2C,UAAA5C,EACA,sCAAAmC,EAAAlC,EAAAjD,MAGAiD,EAAAa,IAAAd,EACA,yBAAAmC,EADAnC,CAEA,WAAAub,EAAAtb,EAAA,UAFAD,CAGA,wBAAAmC,EAHAnC,CAIA,gCACAwb,EAAAxb,EAAAC,EAAA,QACAwb,EAAAzb,EAAAC,EAAAnN,EAAAqP,EAAA,SAAAsZ,CACA,UAGA,GAAAxb,EAAAI,SAAAL,EACA,yBAAAmC,EADAnC,CAEA,WAAAub,EAAAtb,EAAA,SAFAD,CAGA,gCAAAmC,GACAsZ,EAAAzb,EAAAC,EAAAnN,EAAAqP,EAAA,MAAAsZ,CACA,SAGA,CACA,GAAAxb,EAAAoB,OAAA,CACA,IAAAqa,EAAA5b,EAAAe,SAAAZ,EAAAoB,OAAArE,MACA,IAAAse,EAAArb,EAAAoB,OAAArE,OAAAgD,EACA,cAAA0b,EADA1b,CAEA,WAAAC,EAAAoB,OAAArE,KAAA,qBACAse,EAAArb,EAAAoB,OAAArE,MAAA,EACAgD,EACA,QAAA0b,GAEAD,EAAAzb,EAAAC,EAAAnN,EAAAqP,GAEAlC,EAAA2C,UAAA5C,EACA,KAEA,OAAAA,EACA,gBA3KA,IAAAH,EAAAzO,EAAA,IACA0O,EAAA1O,EAAA,IAEA,SAAAmqB,EAAAtb,EAAA6W,GACA,OAAA7W,EAAAjD,KAAA,KAAA8Z,GAAA7W,EAAAI,UAAA,UAAAyW,EAAA,KAAA7W,EAAAa,KAAA,WAAAgW,EAAA,MAAA7W,EAAAlC,QAAA,IAAA,IAAA,YAYA,SAAA0d,EAAAzb,EAAAC,EAAAC,EAAAiC,GAEA,GAAAlC,EAAAG,aACA,GAAAH,EAAAG,wBAAAP,EAAA,CAAAG,EACA,cAAAmC,EADAnC,CAEA,WAFAA,CAGA,WAAAub,EAAAtb,EAAA,eACA,IAAA,IAAAjL,EAAAD,OAAAC,KAAAiL,EAAAG,aAAAzB,QAAArL,EAAA,EAAAA,EAAA0B,EAAApD,SAAA0B,EAAA0M,EACA,WAAAC,EAAAG,aAAAzB,OAAA3J,EAAA1B,KACA0M,EACA,QADAA,CAEA,UAEAA,EACA,IADAA,CAEA,8BAAAE,EAAAiC,EAFAnC,CAGA,QAHAA,CAIA,aAAAC,EAAAjD,KAAA,IAJAgD,CAKA,UAGA,OAAAC,EAAA1C,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAyC,EACA,0BAAAmC,EADAnC,CAEA,WAAAub,EAAAtb,EAAA,YACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAD,EACA,kFAAAmC,EAAAA,EAAAA,EAAAA,EADAnC,CAEA,WAAAub,EAAAtb,EAAA,iBACA,MACA,IAAA,QACA,IAAA,SAAAD,EACA,2BAAAmC,EADAnC,CAEA,WAAAub,EAAAtb,EAAA,WACA,MACA,IAAA,OAAAD,EACA,4BAAAmC,EADAnC,CAEA,WAAAub,EAAAtb,EAAA,YACA,MACA,IAAA,SAAAD,EACA,yBAAAmC,EADAnC,CAEA,WAAAub,EAAAtb,EAAA,WACA,MACA,IAAA,QAAAD,EACA,4DAAAmC,EAAAA,EAAAA,EADAnC,CAEA,WAAAub,EAAAtb,EAAA,WAIA,OAAAD,EAYA,SAAAwb,EAAAxb,EAAAC,EAAAkC,GAEA,OAAAlC,EAAAlC,SACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAiC,EACA,6BAAAmC,EADAnC,CAEA,WAAAub,EAAAtb,EAAA,gBACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAD,EACA,6BAAAmC,EADAnC,CAEA,WAAAub,EAAAtb,EAAA,qBACA,MACA,IAAA,OAAAD,EACA,4BAAAmC,EADAnC,CAEA,WAAAub,EAAAtb,EAAA,gBAGA,OAAAD,uCCzGA,IAAAmH,EAAA7V,EAEA4V,EAAA9V,EAAA,IA6BA+V,EAAA,wBAAA,CAEAzG,WAAA,SAAA6H,GAGA,GAAAA,GAAAA,EAAA,SAAA,CACA,IAAAhL,EAAAvH,KAAAoU,OAAA7B,EAAA,UAEA,GAAAhL,EAAA,CAEA,IAAAD,EAAA,MAAAiL,EAAA,SAAA9V,OAAA,GACA8V,EAAA,SAAAoT,OAAA,GAAApT,EAAA,SAEA,OAAAvS,KAAA8M,OAAA,CACAxF,SAAA,IAAAA,EACA5H,MAAA6H,EAAAxK,OAAAwK,EAAAmD,WAAA6H,IAAA+J,YAKA,OAAAtc,KAAA0K,WAAA6H,IAGAxH,SAAA,SAAAyD,EAAAzN,GAGA,GAAAA,GAAAA,EAAAkG,MAAAuH,EAAAlH,UAAAkH,EAAA9O,MAAA,CAEA,IAAAsH,EAAAwH,EAAAlH,SAAAiQ,UAAA/I,EAAAlH,SAAAuV,YAAA,KAAA,GACAtV,EAAAvH,KAAAoU,OAAApN,GAEAO,IACAiH,EAAAjH,EAAAzJ,OAAA0Q,EAAA9O,QAIA,KAAA8O,aAAAxO,KAAA2P,OAAAnB,aAAA0C,EAAA,CACA,IAAAqB,EAAA/D,EAAAyD,MAAAlH,SAAAyD,EAAAzN,GAEA,OADAwR,EAAA,SAAA/D,EAAAyD,MAAA1H,SACAgI,EAGA,OAAAvS,KAAA+K,SAAAyD,EAAAzN,iCC/EA1F,EAAAC,QAAAiW,EAEA,IAEAC,EAFA1H,EAAA1O,EAAA,IAIA4e,EAAAlQ,EAAAkQ,SACA3d,EAAAyN,EAAAzN,OACAkK,EAAAuD,EAAAvD,KAWA,SAAAqf,EAAArqB,EAAAiL,EAAAlE,GAMAtC,KAAAzE,GAAAA,EAMAyE,KAAAwG,IAAAA,EAMAxG,KAAAyW,KAAA3b,GAMAkF,KAAAsC,IAAAA,EAIA,SAAAujB,KAUA,SAAAC,EAAA5T,GAMAlS,KAAA6W,KAAA3E,EAAA2E,KAMA7W,KAAA+lB,KAAA7T,EAAA6T,KAMA/lB,KAAAwG,IAAA0L,EAAA1L,IAMAxG,KAAAyW,KAAAvE,EAAA8T,OAQA,SAAAzU,IAMAvR,KAAAwG,IAAA,EAMAxG,KAAA6W,KAAA,IAAA+O,EAAAC,EAAA,EAAA,GAMA7lB,KAAA+lB,KAAA/lB,KAAA6W,KAMA7W,KAAAgmB,OAAA,KAqDA,SAAAC,EAAA3jB,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EAoBA,SAAA4jB,EAAA1f,EAAAlE,GACAtC,KAAAwG,IAAAA,EACAxG,KAAAyW,KAAA3b,GACAkF,KAAAsC,IAAAA,EA8CA,SAAA6jB,EAAA7jB,EAAAC,EAAAC,GACA,KAAAF,EAAA4C,IACA3C,EAAAC,KAAA,IAAAF,EAAA2C,GAAA,IACA3C,EAAA2C,IAAA3C,EAAA2C,KAAA,EAAA3C,EAAA4C,IAAA,MAAA,EACA5C,EAAA4C,MAAA,EAEA,KAAA,IAAA5C,EAAA2C,IACA1C,EAAAC,KAAA,IAAAF,EAAA2C,GAAA,IACA3C,EAAA2C,GAAA3C,EAAA2C,KAAA,EAEA1C,EAAAC,KAAAF,EAAA2C,GA2CA,SAAAmhB,EAAA9jB,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAtKAiP,EAAAzE,OAAAhD,EAAA2Q,OACA,WACA,OAAAlJ,EAAAzE,OAAA,WACA,OAAA,IAAA0E,OAIA,WACA,OAAA,IAAAD,GAQAA,EAAAtL,MAAA,SAAAC,GACA,OAAA,IAAA4D,EAAApO,MAAAwK,IAKA4D,EAAApO,QAAAA,QACA6V,EAAAtL,MAAA6D,EAAA6Z,KAAApS,EAAAtL,MAAA6D,EAAApO,MAAAwE,UAAA0a,WAUArJ,EAAArR,UAAAmmB,EAAA,SAAA9qB,EAAAiL,EAAAlE,GAGA,OAFAtC,KAAA+lB,KAAA/lB,KAAA+lB,KAAAtP,KAAA,IAAAmP,EAAArqB,EAAAiL,EAAAlE,GACAtC,KAAAwG,KAAAA,EACAxG,OA8BAkmB,EAAAhmB,UAAAnB,OAAA+N,OAAA8Y,EAAA1lB,YACA3E,GAxBA,SAAA+G,EAAAC,EAAAC,GACA,KAAA,IAAAF,GACAC,EAAAC,KAAA,IAAAF,EAAA,IACAA,KAAA,EAEAC,EAAAC,GAAAF,GA0BAiP,EAAArR,UAAA2a,OAAA,SAAAnb,GAWA,OARAM,KAAAwG,MAAAxG,KAAA+lB,KAAA/lB,KAAA+lB,KAAAtP,KAAA,IAAAyP,GACAxmB,KAAA,GACA,IAAA,EACAA,EAAA,MAAA,EACAA,EAAA,QAAA,EACAA,EAAA,UAAA,EACA,EACAA,IAAA8G,IACAxG,MASAuR,EAAArR,UAAA4a,MAAA,SAAApb,GACA,OAAAA,EAAA,EACAM,KAAAqmB,EAAAF,EAAA,GAAAnM,EAAA1K,WAAA5P,IACAM,KAAA6a,OAAAnb,IAQA6R,EAAArR,UAAA6a,OAAA,SAAArb,GACA,OAAAM,KAAA6a,QAAAnb,GAAA,EAAAA,GAAA,MAAA,IAkCA6R,EAAArR,UAAAsb,MAZAjK,EAAArR,UAAAub,OAAA,SAAA/b,GACA,IAAA4a,EAAAN,EAAA4I,KAAAljB,GACA,OAAAM,KAAAqmB,EAAAF,EAAA7L,EAAA1e,SAAA0e,IAkBA/I,EAAArR,UAAAwb,OAAA,SAAAhc,GACA,IAAA4a,EAAAN,EAAA4I,KAAAljB,GAAAgjB,WACA,OAAA1iB,KAAAqmB,EAAAF,EAAA7L,EAAA1e,SAAA0e,IAQA/I,EAAArR,UAAA8a,KAAA,SAAAtb,GACA,OAAAM,KAAAqmB,EAAAJ,EAAA,EAAAvmB,EAAA,EAAA,IAyBA6R,EAAArR,UAAAgb,SAVA3J,EAAArR,UAAA+a,QAAA,SAAAvb,GACA,OAAAM,KAAAqmB,EAAAD,EAAA,EAAA1mB,IAAA,IA6BA6R,EAAArR,UAAA2b,SAZAtK,EAAArR,UAAA0b,QAAA,SAAAlc,GACA,IAAA4a,EAAAN,EAAA4I,KAAAljB,GACA,OAAAM,KAAAqmB,EAAAD,EAAA,EAAA9L,EAAArV,IAAAohB,EAAAD,EAAA,EAAA9L,EAAApV,KAkBAqM,EAAArR,UAAAib,MAAA,SAAAzb,GACA,OAAAM,KAAAqmB,EAAAvc,EAAAqR,MAAAvY,aAAA,EAAAlD,IASA6R,EAAArR,UAAAkb,OAAA,SAAA1b,GACA,OAAAM,KAAAqmB,EAAAvc,EAAAqR,MAAA1W,cAAA,EAAA/E,IAGA,IAAA4mB,EAAAxc,EAAApO,MAAAwE,UAAAmV,IACA,SAAA/S,EAAAC,EAAAC,GACAD,EAAA8S,IAAA/S,EAAAE,IAGA,SAAAF,EAAAC,EAAAC,GACA,IAAA,IAAA1F,EAAA,EAAAA,EAAAwF,EAAA1G,SAAAkB,EACAyF,EAAAC,EAAA1F,GAAAwF,EAAAxF,IAQAyU,EAAArR,UAAA0L,MAAA,SAAAlM,GACA,IAAA8G,EAAA9G,EAAA9D,SAAA,EACA,IAAA4K,EACA,OAAAxG,KAAAqmB,EAAAJ,EAAA,EAAA,GACA,GAAAnc,EAAA8D,SAAAlO,GAAA,CACA,IAAA6C,EAAAgP,EAAAtL,MAAAO,EAAAnK,EAAAT,OAAA8D,IACArD,EAAAyB,OAAA4B,EAAA6C,EAAA,GACA7C,EAAA6C,EAEA,OAAAvC,KAAA6a,OAAArU,GAAA6f,EAAAC,EAAA9f,EAAA9G,IAQA6R,EAAArR,UAAA5D,OAAA,SAAAoD,GACA,IAAA8G,EAAAD,EAAA3K,OAAA8D,GACA,OAAA8G,EACAxG,KAAA6a,OAAArU,GAAA6f,EAAA9f,EAAAG,MAAAF,EAAA9G,GACAM,KAAAqmB,EAAAJ,EAAA,EAAA,IAQA1U,EAAArR,UAAAyhB,KAAA,WAIA,OAHA3hB,KAAAgmB,OAAA,IAAAF,EAAA9lB,MACAA,KAAA6W,KAAA7W,KAAA+lB,KAAA,IAAAH,EAAAC,EAAA,EAAA,GACA7lB,KAAAwG,IAAA,EACAxG,MAOAuR,EAAArR,UAAAqmB,MAAA,WAUA,OATAvmB,KAAAgmB,QACAhmB,KAAA6W,KAAA7W,KAAAgmB,OAAAnP,KACA7W,KAAA+lB,KAAA/lB,KAAAgmB,OAAAD,KACA/lB,KAAAwG,IAAAxG,KAAAgmB,OAAAxf,IACAxG,KAAAgmB,OAAAhmB,KAAAgmB,OAAAvP,OAEAzW,KAAA6W,KAAA7W,KAAA+lB,KAAA,IAAAH,EAAAC,EAAA,EAAA,GACA7lB,KAAAwG,IAAA,GAEAxG,MAOAuR,EAAArR,UAAA0hB,OAAA,WACA,IAAA/K,EAAA7W,KAAA6W,KACAkP,EAAA/lB,KAAA+lB,KACAvf,EAAAxG,KAAAwG,IAOA,OANAxG,KAAAumB,QAAA1L,OAAArU,GACAA,IACAxG,KAAA+lB,KAAAtP,KAAAI,EAAAJ,KACAzW,KAAA+lB,KAAAA,EACA/lB,KAAAwG,KAAAA,GAEAxG,MAOAuR,EAAArR,UAAAoc,OAAA,WAIA,IAHA,IAAAzF,EAAA7W,KAAA6W,KAAAJ,KACAlU,EAAAvC,KAAA+M,YAAA9G,MAAAjG,KAAAwG,KACAhE,EAAA,EACAqU,GACAA,EAAAtb,GAAAsb,EAAAvU,IAAAC,EAAAC,GACAA,GAAAqU,EAAArQ,IACAqQ,EAAAA,EAAAJ,KAGA,OAAAlU,GAGAgP,EAAAnB,EAAA,SAAAoW,GACAhV,EAAAgV,+BCxcAnrB,EAAAC,QAAAkW,EAGA,IAAAD,EAAAnW,EAAA,KACAoW,EAAAtR,UAAAnB,OAAA+N,OAAAyE,EAAArR,YAAA6M,YAAAyE,EAEA,IAAA1H,EAAA1O,EAAA,IAEAqf,EAAA3Q,EAAA2Q,OAQA,SAAAjJ,IACAD,EAAAjL,KAAAtG,MAQAwR,EAAAvL,MAAA,SAAAC,GACA,OAAAsL,EAAAvL,MAAA6D,EAAAya,GAAAre,IAGA,IAAAugB,EAAAhM,GAAAA,EAAAva,qBAAAyB,YAAA,QAAA8Y,EAAAva,UAAAmV,IAAArO,KACA,SAAA1E,EAAAC,EAAAC,GACAD,EAAA8S,IAAA/S,EAAAE,IAIA,SAAAF,EAAAC,EAAAC,GACA,GAAAF,EAAAokB,KACApkB,EAAAokB,KAAAnkB,EAAAC,EAAA,EAAAF,EAAA1G,aACA,IAAA,IAAAkB,EAAA,EAAAA,EAAAwF,EAAA1G,QACA2G,EAAAC,KAAAF,EAAAxF,MAgBA,SAAA6pB,EAAArkB,EAAAC,EAAAC,GACAF,EAAA1G,OAAA,GACAkO,EAAAvD,KAAAG,MAAApE,EAAAC,EAAAC,GAEAD,EAAA8hB,UAAA/hB,EAAAE,GAdAgP,EAAAtR,UAAA0L,MAAA,SAAAlM,GACAoK,EAAA8D,SAAAlO,KACAA,EAAAoK,EAAAwa,EAAA5kB,EAAA,WACA,IAAA8G,EAAA9G,EAAA9D,SAAA,EAIA,OAHAoE,KAAA6a,OAAArU,GACAA,GACAxG,KAAAqmB,EAAAI,EAAAjgB,EAAA9G,GACAM,MAaAwR,EAAAtR,UAAA5D,OAAA,SAAAoD,GACA,IAAA8G,EAAAiU,EAAAmM,WAAAlnB,GAIA,OAHAM,KAAA6a,OAAArU,GACAA,GACAxG,KAAAqmB,EAAAM,EAAAngB,EAAA9G,GACAM,uB3CvEAhF,KAAAC,OAcAC,EAPA,SAAA2rB,EAAA7f,GACA,IAAA8f,EAAA9rB,EAAAgM,GAGA,OAFA8f,GACA/rB,EAAAiM,GAAA,GAAAV,KAAAwgB,EAAA9rB,EAAAgM,GAAA,CAAA1L,QAAA,IAAAurB,EAAAC,EAAAA,EAAAxrB,SACAwrB,EAAAxrB,QAGAurB,CAAA5rB,EAAA,IAGAC,EAAA4O,KAAA8Z,OAAA1oB,SAAAA,EAGA,mBAAA6Y,QAAAA,OAAAgT,KACAhT,OAAA,CAAA,QAAA,SAAArF,GAKA,OAJAA,GAAAA,EAAAsY,SACA9rB,EAAA4O,KAAA4E,KAAAA,EACAxT,EAAAkW,aAEAlW,IAIA,iBAAAG,QAAAA,QAAAA,OAAAC,UACAD,OAAAC,QAAAJ,GA/BA", "file": "protobuf.min.js", "sourcesContent": ["(function prelude(modules, cache, entries) {\r\n\r\n    // This is the prelude used to bundle protobuf.js for the browser. Wraps up the CommonJS\r\n    // sources through a conflict-free require shim and is again wrapped within an iife that\r\n    // provides a minification-friendly `undefined` var plus a global \"use strict\" directive\r\n    // so that minification can remove the directives of each module.\r\n\r\n    function $require(name) {\r\n        var $module = cache[name];\r\n        if (!$module)\r\n            modules[name][0].call($module = cache[name] = { exports: {} }, $require, $module, $module.exports);\r\n        return $module.exports;\r\n    }\r\n\r\n    var protobuf = $require(entries[0]);\r\n\r\n    // Expose globally\r\n    protobuf.util.global.protobuf = protobuf;\r\n\r\n    // Be nice to AMD\r\n    if (typeof define === \"function\" && define.amd)\r\n        define([\"long\"], function(Long) {\r\n            if (Long && Long.isLong) {\r\n                protobuf.util.Long = Long;\r\n                protobuf.configure();\r\n            }\r\n            return protobuf;\r\n        });\r\n\r\n    // Be nice to CommonJS\r\n    if (typeof module === \"object\" && module && module.exports)\r\n        module.exports = protobuf;\r\n\r\n})/* end of prelude */", "\"use strict\";\r\nmodule.exports = asPromise;\r\n\r\n/**\r\n * Callback as used by {@link util.asPromise}.\r\n * @typedef asPromiseCallback\r\n * @type {function}\r\n * @param {Error|null} error Error, if any\r\n * @param {...*} params Additional arguments\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Returns a promise from a node-style callback function.\r\n * @memberof util\r\n * @param {asPromiseCallback} fn Function to call\r\n * @param {*} ctx Function context\r\n * @param {...*} params Function arguments\r\n * @returns {Promise<*>} Promisified function\r\n */\r\nfunction asPromise(fn, ctx/*, varargs */) {\r\n    var params  = new Array(arguments.length - 1),\r\n        offset  = 0,\r\n        index   = 2,\r\n        pending = true;\r\n    while (index < arguments.length)\r\n        params[offset++] = arguments[index++];\r\n    return new Promise(function executor(resolve, reject) {\r\n        params[offset] = function callback(err/*, varargs */) {\r\n            if (pending) {\r\n                pending = false;\r\n                if (err)\r\n                    reject(err);\r\n                else {\r\n                    var params = new Array(arguments.length - 1),\r\n                        offset = 0;\r\n                    while (offset < params.length)\r\n                        params[offset++] = arguments[offset];\r\n                    resolve.apply(null, params);\r\n                }\r\n            }\r\n        };\r\n        try {\r\n            fn.apply(ctx || null, params);\r\n        } catch (err) {\r\n            if (pending) {\r\n                pending = false;\r\n                reject(err);\r\n            }\r\n        }\r\n    });\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal base64 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar base64 = exports;\r\n\r\n/**\r\n * Calculates the byte length of a base64 encoded string.\r\n * @param {string} string Base64 encoded string\r\n * @returns {number} Byte length\r\n */\r\nbase64.length = function length(string) {\r\n    var p = string.length;\r\n    if (!p)\r\n        return 0;\r\n    var n = 0;\r\n    while (--p % 4 > 1 && string.charAt(p) === \"=\")\r\n        ++n;\r\n    return Math.ceil(string.length * 3) / 4 - n;\r\n};\r\n\r\n// Base64 encoding table\r\nvar b64 = new Array(64);\r\n\r\n// Base64 decoding table\r\nvar s64 = new Array(123);\r\n\r\n// 65..90, 97..122, 48..57, 43, 47\r\nfor (var i = 0; i < 64;)\r\n    s64[b64[i] = i < 26 ? i + 65 : i < 52 ? i + 71 : i < 62 ? i - 4 : i - 59 | 43] = i++;\r\n\r\n/**\r\n * Encodes a buffer to a base64 encoded string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} Base64 encoded string\r\n */\r\nbase64.encode = function encode(buffer, start, end) {\r\n    var parts = null,\r\n        chunk = [];\r\n    var i = 0, // output index\r\n        j = 0, // goto index\r\n        t;     // temporary\r\n    while (start < end) {\r\n        var b = buffer[start++];\r\n        switch (j) {\r\n            case 0:\r\n                chunk[i++] = b64[b >> 2];\r\n                t = (b & 3) << 4;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                chunk[i++] = b64[t | b >> 4];\r\n                t = (b & 15) << 2;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                chunk[i++] = b64[t | b >> 6];\r\n                chunk[i++] = b64[b & 63];\r\n                j = 0;\r\n                break;\r\n        }\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (j) {\r\n        chunk[i++] = b64[t];\r\n        chunk[i++] = 61;\r\n        if (j === 1)\r\n            chunk[i++] = 61;\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\nvar invalidEncoding = \"invalid encoding\";\r\n\r\n/**\r\n * Decodes a base64 encoded string to a buffer.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Number of bytes written\r\n * @throws {Error} If encoding is invalid\r\n */\r\nbase64.decode = function decode(string, buffer, offset) {\r\n    var start = offset;\r\n    var j = 0, // goto index\r\n        t;     // temporary\r\n    for (var i = 0; i < string.length;) {\r\n        var c = string.charCodeAt(i++);\r\n        if (c === 61 && j > 1)\r\n            break;\r\n        if ((c = s64[c]) === undefined)\r\n            throw Error(invalidEncoding);\r\n        switch (j) {\r\n            case 0:\r\n                t = c;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                buffer[offset++] = t << 2 | (c & 48) >> 4;\r\n                t = c;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                buffer[offset++] = (t & 15) << 4 | (c & 60) >> 2;\r\n                t = c;\r\n                j = 3;\r\n                break;\r\n            case 3:\r\n                buffer[offset++] = (t & 3) << 6 | c;\r\n                j = 0;\r\n                break;\r\n        }\r\n    }\r\n    if (j === 1)\r\n        throw Error(invalidEncoding);\r\n    return offset - start;\r\n};\r\n\r\n/**\r\n * Tests if the specified string appears to be base64 encoded.\r\n * @param {string} string String to test\r\n * @returns {boolean} `true` if probably base64 encoded, otherwise false\r\n */\r\nbase64.test = function test(string) {\r\n    return /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(string);\r\n};\r\n", "\"use strict\";\r\nmodule.exports = codegen;\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @param {string[]} functionParams Function parameter names\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n */\r\nfunction codegen(functionParams, functionName) {\r\n\r\n    /* istanbul ignore if */\r\n    if (typeof functionParams === \"string\") {\r\n        functionName = functionParams;\r\n        functionParams = undefined;\r\n    }\r\n\r\n    var body = [];\r\n\r\n    /**\r\n     * Appends code to the function's body or finishes generation.\r\n     * @typedef Codegen\r\n     * @type {function}\r\n     * @param {string|Object.<string,*>} [formatStringOrScope] Format string or, to finish the function, an object of additional scope variables, if any\r\n     * @param {...*} [formatParams] Format parameters\r\n     * @returns {Codegen|Function} Itself or the generated function if finished\r\n     * @throws {Error} If format parameter counts do not match\r\n     */\r\n\r\n    function Codegen(formatStringOrScope) {\r\n        // note that explicit array handling below makes this ~50% faster\r\n\r\n        // finish the function\r\n        if (typeof formatStringOrScope !== \"string\") {\r\n            var source = toString();\r\n            if (codegen.verbose)\r\n                console.log(\"codegen: \" + source); // eslint-disable-line no-console\r\n            source = \"return \" + source;\r\n            if (formatStringOrScope) {\r\n                var scopeKeys   = Object.keys(formatStringOrScope),\r\n                    scopeParams = new Array(scopeKeys.length + 1),\r\n                    scopeValues = new Array(scopeKeys.length),\r\n                    scopeOffset = 0;\r\n                while (scopeOffset < scopeKeys.length) {\r\n                    scopeParams[scopeOffset] = scopeKeys[scopeOffset];\r\n                    scopeValues[scopeOffset] = formatStringOrScope[scopeKeys[scopeOffset++]];\r\n                }\r\n                scopeParams[scopeOffset] = source;\r\n                return Function.apply(null, scopeParams).apply(null, scopeValues); // eslint-disable-line no-new-func\r\n            }\r\n            return Function(source)(); // eslint-disable-line no-new-func\r\n        }\r\n\r\n        // otherwise append to body\r\n        var formatParams = new Array(arguments.length - 1),\r\n            formatOffset = 0;\r\n        while (formatOffset < formatParams.length)\r\n            formatParams[formatOffset] = arguments[++formatOffset];\r\n        formatOffset = 0;\r\n        formatStringOrScope = formatStringOrScope.replace(/%([%dfijs])/g, function replace($0, $1) {\r\n            var value = formatParams[formatOffset++];\r\n            switch ($1) {\r\n                case \"d\": case \"f\": return String(Number(value));\r\n                case \"i\": return String(Math.floor(value));\r\n                case \"j\": return JSON.stringify(value);\r\n                case \"s\": return String(value);\r\n            }\r\n            return \"%\";\r\n        });\r\n        if (formatOffset !== formatParams.length)\r\n            throw Error(\"parameter count mismatch\");\r\n        body.push(formatStringOrScope);\r\n        return Codegen;\r\n    }\r\n\r\n    function toString(functionNameOverride) {\r\n        return \"function \" + (functionNameOverride || functionName || \"\") + \"(\" + (functionParams && functionParams.join(\",\") || \"\") + \"){\\n  \" + body.join(\"\\n  \") + \"\\n}\";\r\n    }\r\n\r\n    Codegen.toString = toString;\r\n    return Codegen;\r\n}\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @function codegen\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * When set to `true`, codegen will log generated code to console. Useful for debugging.\r\n * @name util.codegen.verbose\r\n * @type {boolean}\r\n */\r\ncodegen.verbose = false;\r\n", "\"use strict\";\r\nmodule.exports = EventEmitter;\r\n\r\n/**\r\n * Constructs a new event emitter instance.\r\n * @classdesc A minimal event emitter.\r\n * @memberof util\r\n * @constructor\r\n */\r\nfunction EventEmitter() {\r\n\r\n    /**\r\n     * Registered listeners.\r\n     * @type {Object.<string,*>}\r\n     * @private\r\n     */\r\n    this._listeners = {};\r\n}\r\n\r\n/**\r\n * Registers an event listener.\r\n * @param {string} evt Event name\r\n * @param {function} fn Listener\r\n * @param {*} [ctx] Listener context\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.on = function on(evt, fn, ctx) {\r\n    (this._listeners[evt] || (this._listeners[evt] = [])).push({\r\n        fn  : fn,\r\n        ctx : ctx || this\r\n    });\r\n    return this;\r\n};\r\n\r\n/**\r\n * Removes an event listener or any matching listeners if arguments are omitted.\r\n * @param {string} [evt] Event name. Removes all listeners if omitted.\r\n * @param {function} [fn] Listener to remove. Removes all listeners of `evt` if omitted.\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.off = function off(evt, fn) {\r\n    if (evt === undefined)\r\n        this._listeners = {};\r\n    else {\r\n        if (fn === undefined)\r\n            this._listeners[evt] = [];\r\n        else {\r\n            var listeners = this._listeners[evt];\r\n            for (var i = 0; i < listeners.length;)\r\n                if (listeners[i].fn === fn)\r\n                    listeners.splice(i, 1);\r\n                else\r\n                    ++i;\r\n        }\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Emits an event by calling its listeners with the specified arguments.\r\n * @param {string} evt Event name\r\n * @param {...*} args Arguments\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.emit = function emit(evt) {\r\n    var listeners = this._listeners[evt];\r\n    if (listeners) {\r\n        var args = [],\r\n            i = 1;\r\n        for (; i < arguments.length;)\r\n            args.push(arguments[i++]);\r\n        for (i = 0; i < listeners.length;)\r\n            listeners[i].fn.apply(listeners[i++].ctx, args);\r\n    }\r\n    return this;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = fetch;\r\n\r\nvar asPromise = require(1),\r\n    inquire   = require(7);\r\n\r\nvar fs = inquire(\"fs\");\r\n\r\n/**\r\n * Node-style callback as used by {@link util.fetch}.\r\n * @typedef FetchCallback\r\n * @type {function}\r\n * @param {?Error} error Error, if any, otherwise `null`\r\n * @param {string} [contents] File contents, if there hasn't been an error\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Options as used by {@link util.fetch}.\r\n * @typedef FetchOptions\r\n * @type {Object}\r\n * @property {boolean} [binary=false] Whether expecting a binary response\r\n * @property {boolean} [xhr=false] If `true`, forces the use of XMLHttpRequest\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @memberof util\r\n * @param {string} filename File path or url\r\n * @param {FetchOptions} options Fetch options\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n */\r\nfunction fetch(filename, options, callback) {\r\n    if (typeof options === \"function\") {\r\n        callback = options;\r\n        options = {};\r\n    } else if (!options)\r\n        options = {};\r\n\r\n    if (!callback)\r\n        return asPromise(fetch, this, filename, options); // eslint-disable-line no-invalid-this\r\n\r\n    // if a node-like filesystem is present, try it first but fall back to XHR if nothing is found.\r\n    if (!options.xhr && fs && fs.readFile)\r\n        return fs.readFile(filename, function fetchReadFileCallback(err, contents) {\r\n            return err && typeof XMLHttpRequest !== \"undefined\"\r\n                ? fetch.xhr(filename, options, callback)\r\n                : err\r\n                ? callback(err)\r\n                : callback(null, options.binary ? contents : contents.toString(\"utf8\"));\r\n        });\r\n\r\n    // use the XHR version otherwise.\r\n    return fetch.xhr(filename, options, callback);\r\n}\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchOptions} [options] Fetch options\r\n * @returns {Promise<string|Uint8Array>} Promise\r\n * @variation 3\r\n */\r\n\r\n/**/\r\nfetch.xhr = function fetch_xhr(filename, options, callback) {\r\n    var xhr = new XMLHttpRequest();\r\n    xhr.onreadystatechange /* works everywhere */ = function fetchOnReadyStateChange() {\r\n\r\n        if (xhr.readyState !== 4)\r\n            return undefined;\r\n\r\n        // local cors security errors return status 0 / empty string, too. afaik this cannot be\r\n        // reliably distinguished from an actually empty file for security reasons. feel free\r\n        // to send a pull request if you are aware of a solution.\r\n        if (xhr.status !== 0 && xhr.status !== 200)\r\n            return callback(Error(\"status \" + xhr.status));\r\n\r\n        // if binary data is expected, make sure that some sort of array is returned, even if\r\n        // ArrayBuffers are not supported. the binary string fallback, however, is unsafe.\r\n        if (options.binary) {\r\n            var buffer = xhr.response;\r\n            if (!buffer) {\r\n                buffer = [];\r\n                for (var i = 0; i < xhr.responseText.length; ++i)\r\n                    buffer.push(xhr.responseText.charCodeAt(i) & 255);\r\n            }\r\n            return callback(null, typeof Uint8Array !== \"undefined\" ? new Uint8Array(buffer) : buffer);\r\n        }\r\n        return callback(null, xhr.responseText);\r\n    };\r\n\r\n    if (options.binary) {\r\n        // ref: https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/Sending_and_Receiving_Binary_Data#Receiving_binary_data_in_older_browsers\r\n        if (\"overrideMimeType\" in xhr)\r\n            xhr.overrideMimeType(\"text/plain; charset=x-user-defined\");\r\n        xhr.responseType = \"arraybuffer\";\r\n    }\r\n\r\n    xhr.open(\"GET\", filename);\r\n    xhr.send();\r\n};\r\n", "\"use strict\";\r\n\r\nmodule.exports = factory(factory);\r\n\r\n/**\r\n * Reads / writes floats / doubles from / to buffers.\r\n * @name util.float\r\n * @namespace\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using little endian byte order.\r\n * @name util.float.writeFloatLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using big endian byte order.\r\n * @name util.float.writeFloatBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using little endian byte order.\r\n * @name util.float.readFloatLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using big endian byte order.\r\n * @name util.float.readFloatBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using little endian byte order.\r\n * @name util.float.writeDoubleLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using big endian byte order.\r\n * @name util.float.writeDoubleBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using little endian byte order.\r\n * @name util.float.readDoubleLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using big endian byte order.\r\n * @name util.float.readDoubleBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n// Factory function for the purpose of node-based testing in modified global environments\r\nfunction factory(exports) {\r\n\r\n    // float: typed array\r\n    if (typeof Float32Array !== \"undefined\") (function() {\r\n\r\n        var f32 = new Float32Array([ -0 ]),\r\n            f8b = new Uint8Array(f32.buffer),\r\n            le  = f8b[3] === 128;\r\n\r\n        function writeFloat_f32_cpy(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n        }\r\n\r\n        function writeFloat_f32_rev(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[3];\r\n            buf[pos + 1] = f8b[2];\r\n            buf[pos + 2] = f8b[1];\r\n            buf[pos + 3] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeFloatLE = le ? writeFloat_f32_cpy : writeFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeFloatBE = le ? writeFloat_f32_rev : writeFloat_f32_cpy;\r\n\r\n        function readFloat_f32_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        function readFloat_f32_rev(buf, pos) {\r\n            f8b[3] = buf[pos    ];\r\n            f8b[2] = buf[pos + 1];\r\n            f8b[1] = buf[pos + 2];\r\n            f8b[0] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readFloatLE = le ? readFloat_f32_cpy : readFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.readFloatBE = le ? readFloat_f32_rev : readFloat_f32_cpy;\r\n\r\n    // float: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeFloat_ieee754(writeUint, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0)\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos);\r\n            else if (isNaN(val))\r\n                writeUint(2143289344, buf, pos);\r\n            else if (val > 3.4028234663852886e+38) // +-Infinity\r\n                writeUint((sign << 31 | 2139095040) >>> 0, buf, pos);\r\n            else if (val < 1.1754943508222875e-38) // denormal\r\n                writeUint((sign << 31 | Math.round(val / 1.401298464324817e-45)) >>> 0, buf, pos);\r\n            else {\r\n                var exponent = Math.floor(Math.log(val) / Math.LN2),\r\n                    mantissa = Math.round(val * Math.pow(2, -exponent) * 8388608) & 8388607;\r\n                writeUint((sign << 31 | exponent + 127 << 23 | mantissa) >>> 0, buf, pos);\r\n            }\r\n        }\r\n\r\n        exports.writeFloatLE = writeFloat_ieee754.bind(null, writeUintLE);\r\n        exports.writeFloatBE = writeFloat_ieee754.bind(null, writeUintBE);\r\n\r\n        function readFloat_ieee754(readUint, buf, pos) {\r\n            var uint = readUint(buf, pos),\r\n                sign = (uint >> 31) * 2 + 1,\r\n                exponent = uint >>> 23 & 255,\r\n                mantissa = uint & 8388607;\r\n            return exponent === 255\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 1.401298464324817e-45 * mantissa\r\n                : sign * Math.pow(2, exponent - 150) * (mantissa + 8388608);\r\n        }\r\n\r\n        exports.readFloatLE = readFloat_ieee754.bind(null, readUintLE);\r\n        exports.readFloatBE = readFloat_ieee754.bind(null, readUintBE);\r\n\r\n    })();\r\n\r\n    // double: typed array\r\n    if (typeof Float64Array !== \"undefined\") (function() {\r\n\r\n        var f64 = new Float64Array([-0]),\r\n            f8b = new Uint8Array(f64.buffer),\r\n            le  = f8b[7] === 128;\r\n\r\n        function writeDouble_f64_cpy(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n            buf[pos + 4] = f8b[4];\r\n            buf[pos + 5] = f8b[5];\r\n            buf[pos + 6] = f8b[6];\r\n            buf[pos + 7] = f8b[7];\r\n        }\r\n\r\n        function writeDouble_f64_rev(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[7];\r\n            buf[pos + 1] = f8b[6];\r\n            buf[pos + 2] = f8b[5];\r\n            buf[pos + 3] = f8b[4];\r\n            buf[pos + 4] = f8b[3];\r\n            buf[pos + 5] = f8b[2];\r\n            buf[pos + 6] = f8b[1];\r\n            buf[pos + 7] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleLE = le ? writeDouble_f64_cpy : writeDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleBE = le ? writeDouble_f64_rev : writeDouble_f64_cpy;\r\n\r\n        function readDouble_f64_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            f8b[4] = buf[pos + 4];\r\n            f8b[5] = buf[pos + 5];\r\n            f8b[6] = buf[pos + 6];\r\n            f8b[7] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        function readDouble_f64_rev(buf, pos) {\r\n            f8b[7] = buf[pos    ];\r\n            f8b[6] = buf[pos + 1];\r\n            f8b[5] = buf[pos + 2];\r\n            f8b[4] = buf[pos + 3];\r\n            f8b[3] = buf[pos + 4];\r\n            f8b[2] = buf[pos + 5];\r\n            f8b[1] = buf[pos + 6];\r\n            f8b[0] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readDoubleLE = le ? readDouble_f64_cpy : readDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.readDoubleBE = le ? readDouble_f64_rev : readDouble_f64_cpy;\r\n\r\n    // double: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeDouble_ieee754(writeUint, off0, off1, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos + off1);\r\n            } else if (isNaN(val)) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(2146959360, buf, pos + off1);\r\n            } else if (val > 1.7976931348623157e+308) { // +-Infinity\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint((sign << 31 | 2146435072) >>> 0, buf, pos + off1);\r\n            } else {\r\n                var mantissa;\r\n                if (val < 2.2250738585072014e-308) { // denormal\r\n                    mantissa = val / 5e-324;\r\n                    writeUint(mantissa >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | mantissa / 4294967296) >>> 0, buf, pos + off1);\r\n                } else {\r\n                    var exponent = Math.floor(Math.log(val) / Math.LN2);\r\n                    if (exponent === 1024)\r\n                        exponent = 1023;\r\n                    mantissa = val * Math.pow(2, -exponent);\r\n                    writeUint(mantissa * 4503599627370496 >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | exponent + 1023 << 20 | mantissa * 1048576 & 1048575) >>> 0, buf, pos + off1);\r\n                }\r\n            }\r\n        }\r\n\r\n        exports.writeDoubleLE = writeDouble_ieee754.bind(null, writeUintLE, 0, 4);\r\n        exports.writeDoubleBE = writeDouble_ieee754.bind(null, writeUintBE, 4, 0);\r\n\r\n        function readDouble_ieee754(readUint, off0, off1, buf, pos) {\r\n            var lo = readUint(buf, pos + off0),\r\n                hi = readUint(buf, pos + off1);\r\n            var sign = (hi >> 31) * 2 + 1,\r\n                exponent = hi >>> 20 & 2047,\r\n                mantissa = 4294967296 * (hi & 1048575) + lo;\r\n            return exponent === 2047\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 5e-324 * mantissa\r\n                : sign * Math.pow(2, exponent - 1075) * (mantissa + 4503599627370496);\r\n        }\r\n\r\n        exports.readDoubleLE = readDouble_ieee754.bind(null, readUintLE, 0, 4);\r\n        exports.readDoubleBE = readDouble_ieee754.bind(null, readUintBE, 4, 0);\r\n\r\n    })();\r\n\r\n    return exports;\r\n}\r\n\r\n// uint helpers\r\n\r\nfunction writeUintLE(val, buf, pos) {\r\n    buf[pos    ] =  val        & 255;\r\n    buf[pos + 1] =  val >>> 8  & 255;\r\n    buf[pos + 2] =  val >>> 16 & 255;\r\n    buf[pos + 3] =  val >>> 24;\r\n}\r\n\r\nfunction writeUintBE(val, buf, pos) {\r\n    buf[pos    ] =  val >>> 24;\r\n    buf[pos + 1] =  val >>> 16 & 255;\r\n    buf[pos + 2] =  val >>> 8  & 255;\r\n    buf[pos + 3] =  val        & 255;\r\n}\r\n\r\nfunction readUintLE(buf, pos) {\r\n    return (buf[pos    ]\r\n          | buf[pos + 1] << 8\r\n          | buf[pos + 2] << 16\r\n          | buf[pos + 3] << 24) >>> 0;\r\n}\r\n\r\nfunction readUintBE(buf, pos) {\r\n    return (buf[pos    ] << 24\r\n          | buf[pos + 1] << 16\r\n          | buf[pos + 2] << 8\r\n          | buf[pos + 3]) >>> 0;\r\n}\r\n", "\"use strict\";\r\nmodule.exports = inquire;\r\n\r\n/**\r\n * Requires a module only if available.\r\n * @memberof util\r\n * @param {string} moduleName Module to require\r\n * @returns {?Object} Required module if available and not empty, otherwise `null`\r\n */\r\nfunction inquire(moduleName) {\r\n    try {\r\n        var mod = eval(\"quire\".replace(/^/,\"re\"))(moduleName); // eslint-disable-line no-eval\r\n        if (mod && (mod.length || Object.keys(mod).length))\r\n            return mod;\r\n    } catch (e) {} // eslint-disable-line no-empty\r\n    return null;\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal path module to resolve Unix, Windows and URL paths alike.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar path = exports;\r\n\r\nvar isAbsolute =\r\n/**\r\n * Tests if the specified path is absolute.\r\n * @param {string} path Path to test\r\n * @returns {boolean} `true` if path is absolute\r\n */\r\npath.isAbsolute = function isAbsolute(path) {\r\n    return /^(?:\\/|\\w+:)/.test(path);\r\n};\r\n\r\nvar normalize =\r\n/**\r\n * Normalizes the specified path.\r\n * @param {string} path Path to normalize\r\n * @returns {string} Normalized path\r\n */\r\npath.normalize = function normalize(path) {\r\n    path = path.replace(/\\\\/g, \"/\")\r\n               .replace(/\\/{2,}/g, \"/\");\r\n    var parts    = path.split(\"/\"),\r\n        absolute = isAbsolute(path),\r\n        prefix   = \"\";\r\n    if (absolute)\r\n        prefix = parts.shift() + \"/\";\r\n    for (var i = 0; i < parts.length;) {\r\n        if (parts[i] === \"..\") {\r\n            if (i > 0 && parts[i - 1] !== \"..\")\r\n                parts.splice(--i, 2);\r\n            else if (absolute)\r\n                parts.splice(i, 1);\r\n            else\r\n                ++i;\r\n        } else if (parts[i] === \".\")\r\n            parts.splice(i, 1);\r\n        else\r\n            ++i;\r\n    }\r\n    return prefix + parts.join(\"/\");\r\n};\r\n\r\n/**\r\n * Resolves the specified include path against the specified origin path.\r\n * @param {string} originPath Path to the origin file\r\n * @param {string} includePath Include path relative to origin path\r\n * @param {boolean} [alreadyNormalized=false] `true` if both paths are already known to be normalized\r\n * @returns {string} Path to the include file\r\n */\r\npath.resolve = function resolve(originPath, includePath, alreadyNormalized) {\r\n    if (!alreadyNormalized)\r\n        includePath = normalize(includePath);\r\n    if (isAbsolute(includePath))\r\n        return includePath;\r\n    if (!alreadyNormalized)\r\n        originPath = normalize(originPath);\r\n    return (originPath = originPath.replace(/(?:\\/|^)[^/]+$/, \"\")).length ? normalize(originPath + \"/\" + includePath) : includePath;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = pool;\r\n\r\n/**\r\n * An allocator as used by {@link util.pool}.\r\n * @typedef PoolAllocator\r\n * @type {function}\r\n * @param {number} size Buffer size\r\n * @returns {Uint8Array} Buffer\r\n */\r\n\r\n/**\r\n * A slicer as used by {@link util.pool}.\r\n * @typedef PoolSlicer\r\n * @type {function}\r\n * @param {number} start Start offset\r\n * @param {number} end End offset\r\n * @returns {Uint8Array} Buffer slice\r\n * @this {Uint8Array}\r\n */\r\n\r\n/**\r\n * A general purpose buffer pool.\r\n * @memberof util\r\n * @function\r\n * @param {PoolAllocator} alloc Allocator\r\n * @param {PoolSlicer} slice Slicer\r\n * @param {number} [size=8192] Slab size\r\n * @returns {PoolAllocator} Pooled allocator\r\n */\r\nfunction pool(alloc, slice, size) {\r\n    var SIZE   = size || 8192;\r\n    var MAX    = SIZE >>> 1;\r\n    var slab   = null;\r\n    var offset = SIZE;\r\n    return function pool_alloc(size) {\r\n        if (size < 1 || size > MAX)\r\n            return alloc(size);\r\n        if (offset + size > SIZE) {\r\n            slab = alloc(SIZE);\r\n            offset = 0;\r\n        }\r\n        var buf = slice.call(slab, offset, offset += size);\r\n        if (offset & 7) // align to 32 bit\r\n            offset = (offset | 7) + 1;\r\n        return buf;\r\n    };\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal UTF8 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar utf8 = exports;\r\n\r\n/**\r\n * Calculates the UTF8 byte length of a string.\r\n * @param {string} string String\r\n * @returns {number} Byte length\r\n */\r\nutf8.length = function utf8_length(string) {\r\n    var len = 0,\r\n        c = 0;\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c = string.charCodeAt(i);\r\n        if (c < 128)\r\n            len += 1;\r\n        else if (c < 2048)\r\n            len += 2;\r\n        else if ((c & 0xFC00) === 0xD800 && (string.charCodeAt(i + 1) & 0xFC00) === 0xDC00) {\r\n            ++i;\r\n            len += 4;\r\n        } else\r\n            len += 3;\r\n    }\r\n    return len;\r\n};\r\n\r\n/**\r\n * Reads UTF8 bytes as a string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} String read\r\n */\r\nutf8.read = function utf8_read(buffer, start, end) {\r\n    var len = end - start;\r\n    if (len < 1)\r\n        return \"\";\r\n    var parts = null,\r\n        chunk = [],\r\n        i = 0, // char offset\r\n        t;     // temporary\r\n    while (start < end) {\r\n        t = buffer[start++];\r\n        if (t < 128)\r\n            chunk[i++] = t;\r\n        else if (t > 191 && t < 224)\r\n            chunk[i++] = (t & 31) << 6 | buffer[start++] & 63;\r\n        else if (t > 239 && t < 365) {\r\n            t = ((t & 7) << 18 | (buffer[start++] & 63) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63) - 0x10000;\r\n            chunk[i++] = 0xD800 + (t >> 10);\r\n            chunk[i++] = 0xDC00 + (t & 1023);\r\n        } else\r\n            chunk[i++] = (t & 15) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63;\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\n/**\r\n * Writes a string as UTF8 bytes.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Bytes written\r\n */\r\nutf8.write = function utf8_write(string, buffer, offset) {\r\n    var start = offset,\r\n        c1, // character 1\r\n        c2; // character 2\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c1 = string.charCodeAt(i);\r\n        if (c1 < 128) {\r\n            buffer[offset++] = c1;\r\n        } else if (c1 < 2048) {\r\n            buffer[offset++] = c1 >> 6       | 192;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else if ((c1 & 0xFC00) === 0xD800 && ((c2 = string.charCodeAt(i + 1)) & 0xFC00) === 0xDC00) {\r\n            c1 = 0x10000 + ((c1 & 0x03FF) << 10) + (c2 & 0x03FF);\r\n            ++i;\r\n            buffer[offset++] = c1 >> 18      | 240;\r\n            buffer[offset++] = c1 >> 12 & 63 | 128;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else {\r\n            buffer[offset++] = c1 >> 12      | 224;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        }\r\n    }\r\n    return offset - start;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = common;\r\n\r\nvar commonRe = /\\/|\\./;\r\n\r\n/**\r\n * Provides common type definitions.\r\n * Can also be used to provide additional google types or your own custom types.\r\n * @param {string} name Short name as in `google/protobuf/[name].proto` or full file name\r\n * @param {Object.<string,*>} json JSON definition within `google.protobuf` if a short name, otherwise the file's root definition\r\n * @returns {undefined}\r\n * @property {INamespace} google/protobuf/any.proto Any\r\n * @property {INamespace} google/protobuf/duration.proto Duration\r\n * @property {INamespace} google/protobuf/empty.proto Empty\r\n * @property {INamespace} google/protobuf/field_mask.proto FieldMask\r\n * @property {INamespace} google/protobuf/struct.proto Struct, Value, NullValue and ListValue\r\n * @property {INamespace} google/protobuf/timestamp.proto Timestamp\r\n * @property {INamespace} google/protobuf/wrappers.proto Wrappers\r\n * @example\r\n * // manually provides descriptor.proto (assumes google/protobuf/ namespace and .proto extension)\r\n * protobuf.common(\"descriptor\", descriptorJson);\r\n *\r\n * // manually provides a custom definition (uses my.foo namespace)\r\n * protobuf.common(\"my/foo/bar.proto\", myFooBarJson);\r\n */\r\nfunction common(name, json) {\r\n    if (!commonRe.test(name)) {\r\n        name = \"google/protobuf/\" + name + \".proto\";\r\n        json = { nested: { google: { nested: { protobuf: { nested: json } } } } };\r\n    }\r\n    common[name] = json;\r\n}\r\n\r\n// Not provided because of limited use (feel free to discuss or to provide yourself):\r\n//\r\n// google/protobuf/descriptor.proto\r\n// google/protobuf/source_context.proto\r\n// google/protobuf/type.proto\r\n//\r\n// Stripped and pre-parsed versions of these non-bundled files are instead available as part of\r\n// the repository or package within the google/protobuf directory.\r\n\r\ncommon(\"any\", {\r\n\r\n    /**\r\n     * Properties of a google.protobuf.Any message.\r\n     * @interface IAny\r\n     * @type {Object}\r\n     * @property {string} [typeUrl]\r\n     * @property {Uint8Array} [bytes]\r\n     * @memberof common\r\n     */\r\n    Any: {\r\n        fields: {\r\n            type_url: {\r\n                type: \"string\",\r\n                id: 1\r\n            },\r\n            value: {\r\n                type: \"bytes\",\r\n                id: 2\r\n            }\r\n        }\r\n    }\r\n});\r\n\r\nvar timeType;\r\n\r\ncommon(\"duration\", {\r\n\r\n    /**\r\n     * Properties of a google.protobuf.Duration message.\r\n     * @interface IDuration\r\n     * @type {Object}\r\n     * @property {number|Long} [seconds]\r\n     * @property {number} [nanos]\r\n     * @memberof common\r\n     */\r\n    Duration: timeType = {\r\n        fields: {\r\n            seconds: {\r\n                type: \"int64\",\r\n                id: 1\r\n            },\r\n            nanos: {\r\n                type: \"int32\",\r\n                id: 2\r\n            }\r\n        }\r\n    }\r\n});\r\n\r\ncommon(\"timestamp\", {\r\n\r\n    /**\r\n     * Properties of a google.protobuf.Timestamp message.\r\n     * @interface ITimestamp\r\n     * @type {Object}\r\n     * @property {number|Long} [seconds]\r\n     * @property {number} [nanos]\r\n     * @memberof common\r\n     */\r\n    Timestamp: timeType\r\n});\r\n\r\ncommon(\"empty\", {\r\n\r\n    /**\r\n     * Properties of a google.protobuf.Empty message.\r\n     * @interface IEmpty\r\n     * @memberof common\r\n     */\r\n    Empty: {\r\n        fields: {}\r\n    }\r\n});\r\n\r\ncommon(\"struct\", {\r\n\r\n    /**\r\n     * Properties of a google.protobuf.Struct message.\r\n     * @interface IStruct\r\n     * @type {Object}\r\n     * @property {Object.<string,IValue>} [fields]\r\n     * @memberof common\r\n     */\r\n    Struct: {\r\n        fields: {\r\n            fields: {\r\n                keyType: \"string\",\r\n                type: \"Value\",\r\n                id: 1\r\n            }\r\n        }\r\n    },\r\n\r\n    /**\r\n     * Properties of a google.protobuf.Value message.\r\n     * @interface IValue\r\n     * @type {Object}\r\n     * @property {string} [kind]\r\n     * @property {0} [nullValue]\r\n     * @property {number} [numberValue]\r\n     * @property {string} [stringValue]\r\n     * @property {boolean} [boolValue]\r\n     * @property {IStruct} [structValue]\r\n     * @property {IListValue} [listValue]\r\n     * @memberof common\r\n     */\r\n    Value: {\r\n        oneofs: {\r\n            kind: {\r\n                oneof: [\r\n                    \"nullValue\",\r\n                    \"numberValue\",\r\n                    \"stringValue\",\r\n                    \"boolValue\",\r\n                    \"structValue\",\r\n                    \"listValue\"\r\n                ]\r\n            }\r\n        },\r\n        fields: {\r\n            nullValue: {\r\n                type: \"NullValue\",\r\n                id: 1\r\n            },\r\n            numberValue: {\r\n                type: \"double\",\r\n                id: 2\r\n            },\r\n            stringValue: {\r\n                type: \"string\",\r\n                id: 3\r\n            },\r\n            boolValue: {\r\n                type: \"bool\",\r\n                id: 4\r\n            },\r\n            structValue: {\r\n                type: \"Struct\",\r\n                id: 5\r\n            },\r\n            listValue: {\r\n                type: \"ListValue\",\r\n                id: 6\r\n            }\r\n        }\r\n    },\r\n\r\n    NullValue: {\r\n        values: {\r\n            NULL_VALUE: 0\r\n        }\r\n    },\r\n\r\n    /**\r\n     * Properties of a google.protobuf.ListValue message.\r\n     * @interface IListValue\r\n     * @type {Object}\r\n     * @property {Array.<IValue>} [values]\r\n     * @memberof common\r\n     */\r\n    ListValue: {\r\n        fields: {\r\n            values: {\r\n                rule: \"repeated\",\r\n                type: \"Value\",\r\n                id: 1\r\n            }\r\n        }\r\n    }\r\n});\r\n\r\ncommon(\"wrappers\", {\r\n\r\n    /**\r\n     * Properties of a google.protobuf.DoubleValue message.\r\n     * @interface IDoubleValue\r\n     * @type {Object}\r\n     * @property {number} [value]\r\n     * @memberof common\r\n     */\r\n    DoubleValue: {\r\n        fields: {\r\n            value: {\r\n                type: \"double\",\r\n                id: 1\r\n            }\r\n        }\r\n    },\r\n\r\n    /**\r\n     * Properties of a google.protobuf.FloatValue message.\r\n     * @interface IFloatValue\r\n     * @type {Object}\r\n     * @property {number} [value]\r\n     * @memberof common\r\n     */\r\n    FloatValue: {\r\n        fields: {\r\n            value: {\r\n                type: \"float\",\r\n                id: 1\r\n            }\r\n        }\r\n    },\r\n\r\n    /**\r\n     * Properties of a google.protobuf.Int64Value message.\r\n     * @interface IInt64Value\r\n     * @type {Object}\r\n     * @property {number|Long} [value]\r\n     * @memberof common\r\n     */\r\n    Int64Value: {\r\n        fields: {\r\n            value: {\r\n                type: \"int64\",\r\n                id: 1\r\n            }\r\n        }\r\n    },\r\n\r\n    /**\r\n     * Properties of a google.protobuf.UInt64Value message.\r\n     * @interface IUInt64Value\r\n     * @type {Object}\r\n     * @property {number|Long} [value]\r\n     * @memberof common\r\n     */\r\n    UInt64Value: {\r\n        fields: {\r\n            value: {\r\n                type: \"uint64\",\r\n                id: 1\r\n            }\r\n        }\r\n    },\r\n\r\n    /**\r\n     * Properties of a google.protobuf.Int32Value message.\r\n     * @interface IInt32Value\r\n     * @type {Object}\r\n     * @property {number} [value]\r\n     * @memberof common\r\n     */\r\n    Int32Value: {\r\n        fields: {\r\n            value: {\r\n                type: \"int32\",\r\n                id: 1\r\n            }\r\n        }\r\n    },\r\n\r\n    /**\r\n     * Properties of a google.protobuf.UInt32Value message.\r\n     * @interface IUInt32Value\r\n     * @type {Object}\r\n     * @property {number} [value]\r\n     * @memberof common\r\n     */\r\n    UInt32Value: {\r\n        fields: {\r\n            value: {\r\n                type: \"uint32\",\r\n                id: 1\r\n            }\r\n        }\r\n    },\r\n\r\n    /**\r\n     * Properties of a google.protobuf.BoolValue message.\r\n     * @interface IBoolValue\r\n     * @type {Object}\r\n     * @property {boolean} [value]\r\n     * @memberof common\r\n     */\r\n    BoolValue: {\r\n        fields: {\r\n            value: {\r\n                type: \"bool\",\r\n                id: 1\r\n            }\r\n        }\r\n    },\r\n\r\n    /**\r\n     * Properties of a google.protobuf.StringValue message.\r\n     * @interface IStringValue\r\n     * @type {Object}\r\n     * @property {string} [value]\r\n     * @memberof common\r\n     */\r\n    StringValue: {\r\n        fields: {\r\n            value: {\r\n                type: \"string\",\r\n                id: 1\r\n            }\r\n        }\r\n    },\r\n\r\n    /**\r\n     * Properties of a google.protobuf.BytesValue message.\r\n     * @interface IBytesValue\r\n     * @type {Object}\r\n     * @property {Uint8Array} [value]\r\n     * @memberof common\r\n     */\r\n    BytesValue: {\r\n        fields: {\r\n            value: {\r\n                type: \"bytes\",\r\n                id: 1\r\n            }\r\n        }\r\n    }\r\n});\r\n\r\ncommon(\"field_mask\", {\r\n\r\n    /**\r\n     * Properties of a google.protobuf.FieldMask message.\r\n     * @interface IDoubleValue\r\n     * @type {Object}\r\n     * @property {number} [value]\r\n     * @memberof common\r\n     */\r\n    FieldMask: {\r\n        fields: {\r\n            paths: {\r\n                rule: \"repeated\",\r\n                type: \"string\",\r\n                id: 1\r\n            }\r\n        }\r\n    }\r\n});\r\n\r\n/**\r\n * Gets the root definition of the specified common proto file.\r\n *\r\n * Bundled definitions are:\r\n * - google/protobuf/any.proto\r\n * - google/protobuf/duration.proto\r\n * - google/protobuf/empty.proto\r\n * - google/protobuf/field_mask.proto\r\n * - google/protobuf/struct.proto\r\n * - google/protobuf/timestamp.proto\r\n * - google/protobuf/wrappers.proto\r\n *\r\n * @param {string} file Proto file name\r\n * @returns {INamespace|null} Root definition or `null` if not defined\r\n */\r\ncommon.get = function get(file) {\r\n    return common[file] || null;\r\n};\r\n", "\"use strict\";\r\n/**\r\n * Runtime message from/to plain object converters.\r\n * @namespace\r\n */\r\nvar converter = exports;\r\n\r\nvar Enum = require(15),\r\n    util = require(37);\r\n\r\n/**\r\n * Generates a partial value fromObject conveter.\r\n * @param {Codegen} gen Codegen instance\r\n * @param {Field} field Reflected field\r\n * @param {number} fieldIndex Field index\r\n * @param {string} prop Property reference\r\n * @returns {Codegen} Codegen instance\r\n * @ignore\r\n */\r\nfunction genValuePartial_fromObject(gen, field, fieldIndex, prop) {\r\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\r\n    if (field.resolvedType) {\r\n        if (field.resolvedType instanceof Enum) { gen\r\n            (\"switch(d%s){\", prop);\r\n            for (var values = field.resolvedType.values, keys = Object.keys(values), i = 0; i < keys.length; ++i) {\r\n                if (field.repeated && values[keys[i]] === field.typeDefault) gen\r\n                (\"default:\");\r\n                gen\r\n                (\"case%j:\", keys[i])\r\n                (\"case %i:\", values[keys[i]])\r\n                    (\"m%s=%j\", prop, values[keys[i]])\r\n                    (\"break\");\r\n            } gen\r\n            (\"}\");\r\n        } else gen\r\n            (\"if(typeof d%s!==\\\"object\\\")\", prop)\r\n                (\"throw TypeError(%j)\", field.fullName + \": object expected\")\r\n            (\"m%s=types[%i].fromObject(d%s)\", prop, fieldIndex, prop);\r\n    } else {\r\n        var isUnsigned = false;\r\n        switch (field.type) {\r\n            case \"double\":\r\n            case \"float\": gen\r\n                (\"m%s=Number(d%s)\", prop, prop); // also catches \"NaN\", \"Infinity\"\r\n                break;\r\n            case \"uint32\":\r\n            case \"fixed32\": gen\r\n                (\"m%s=d%s>>>0\", prop, prop);\r\n                break;\r\n            case \"int32\":\r\n            case \"sint32\":\r\n            case \"sfixed32\": gen\r\n                (\"m%s=d%s|0\", prop, prop);\r\n                break;\r\n            case \"uint64\":\r\n                isUnsigned = true;\r\n                // eslint-disable-line no-fallthrough\r\n            case \"int64\":\r\n            case \"sint64\":\r\n            case \"fixed64\":\r\n            case \"sfixed64\": gen\r\n                (\"if(util.Long)\")\r\n                    (\"(m%s=util.Long.fromValue(d%s)).unsigned=%j\", prop, prop, isUnsigned)\r\n                (\"else if(typeof d%s===\\\"string\\\")\", prop)\r\n                    (\"m%s=parseInt(d%s,10)\", prop, prop)\r\n                (\"else if(typeof d%s===\\\"number\\\")\", prop)\r\n                    (\"m%s=d%s\", prop, prop)\r\n                (\"else if(typeof d%s===\\\"object\\\")\", prop)\r\n                    (\"m%s=new util.LongBits(d%s.low>>>0,d%s.high>>>0).toNumber(%s)\", prop, prop, prop, isUnsigned ? \"true\" : \"\");\r\n                break;\r\n            case \"bytes\": gen\r\n                (\"if(typeof d%s===\\\"string\\\")\", prop)\r\n                    (\"util.base64.decode(d%s,m%s=util.newBuffer(util.base64.length(d%s)),0)\", prop, prop, prop)\r\n                (\"else if(d%s.length)\", prop)\r\n                    (\"m%s=d%s\", prop, prop);\r\n                break;\r\n            case \"string\": gen\r\n                (\"m%s=String(d%s)\", prop, prop);\r\n                break;\r\n            case \"bool\": gen\r\n                (\"m%s=Boolean(d%s)\", prop, prop);\r\n                break;\r\n            /* default: gen\r\n                (\"m%s=d%s\", prop, prop);\r\n                break; */\r\n        }\r\n    }\r\n    return gen;\r\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\r\n}\r\n\r\n/**\r\n * Generates a plain object to runtime message converter specific to the specified message type.\r\n * @param {Type} mtype Message type\r\n * @returns {Codegen} Codegen instance\r\n */\r\nconverter.fromObject = function fromObject(mtype) {\r\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\r\n    var fields = mtype.fieldsArray;\r\n    var gen = util.codegen([\"d\"], mtype.name + \"$fromObject\")\r\n    (\"if(d instanceof this.ctor)\")\r\n        (\"return d\");\r\n    if (!fields.length) return gen\r\n    (\"return new this.ctor\");\r\n    gen\r\n    (\"var m=new this.ctor\");\r\n    for (var i = 0; i < fields.length; ++i) {\r\n        var field  = fields[i].resolve(),\r\n            prop   = util.safeProp(field.name);\r\n\r\n        // Map fields\r\n        if (field.map) { gen\r\n    (\"if(d%s){\", prop)\r\n        (\"if(typeof d%s!==\\\"object\\\")\", prop)\r\n            (\"throw TypeError(%j)\", field.fullName + \": object expected\")\r\n        (\"m%s={}\", prop)\r\n        (\"for(var ks=Object.keys(d%s),i=0;i<ks.length;++i){\", prop);\r\n            genValuePartial_fromObject(gen, field, /* not sorted */ i, prop + \"[ks[i]]\")\r\n        (\"}\")\r\n    (\"}\");\r\n\r\n        // Repeated fields\r\n        } else if (field.repeated) { gen\r\n    (\"if(d%s){\", prop)\r\n        (\"if(!Array.isArray(d%s))\", prop)\r\n            (\"throw TypeError(%j)\", field.fullName + \": array expected\")\r\n        (\"m%s=[]\", prop)\r\n        (\"for(var i=0;i<d%s.length;++i){\", prop);\r\n            genValuePartial_fromObject(gen, field, /* not sorted */ i, prop + \"[i]\")\r\n        (\"}\")\r\n    (\"}\");\r\n\r\n        // Non-repeated fields\r\n        } else {\r\n            if (!(field.resolvedType instanceof Enum)) gen // no need to test for null/undefined if an enum (uses switch)\r\n    (\"if(d%s!=null){\", prop); // !== undefined && !== null\r\n        genValuePartial_fromObject(gen, field, /* not sorted */ i, prop);\r\n            if (!(field.resolvedType instanceof Enum)) gen\r\n    (\"}\");\r\n        }\r\n    } return gen\r\n    (\"return m\");\r\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\r\n};\r\n\r\n/**\r\n * Generates a partial value toObject converter.\r\n * @param {Codegen} gen Codegen instance\r\n * @param {Field} field Reflected field\r\n * @param {number} fieldIndex Field index\r\n * @param {string} prop Property reference\r\n * @returns {Codegen} Codegen instance\r\n * @ignore\r\n */\r\nfunction genValuePartial_toObject(gen, field, fieldIndex, prop) {\r\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\r\n    if (field.resolvedType) {\r\n        if (field.resolvedType instanceof Enum) gen\r\n            (\"d%s=o.enums===String?types[%i].values[m%s]:m%s\", prop, fieldIndex, prop, prop);\r\n        else gen\r\n            (\"d%s=types[%i].toObject(m%s,o)\", prop, fieldIndex, prop);\r\n    } else {\r\n        var isUnsigned = false;\r\n        switch (field.type) {\r\n            case \"double\":\r\n            case \"float\": gen\r\n            (\"d%s=o.json&&!isFinite(m%s)?String(m%s):m%s\", prop, prop, prop, prop);\r\n                break;\r\n            case \"uint64\":\r\n                isUnsigned = true;\r\n                // eslint-disable-line no-fallthrough\r\n            case \"int64\":\r\n            case \"sint64\":\r\n            case \"fixed64\":\r\n            case \"sfixed64\": gen\r\n            (\"if(typeof m%s===\\\"number\\\")\", prop)\r\n                (\"d%s=o.longs===String?String(m%s):m%s\", prop, prop, prop)\r\n            (\"else\") // Long-like\r\n                (\"d%s=o.longs===String?util.Long.prototype.toString.call(m%s):o.longs===Number?new util.LongBits(m%s.low>>>0,m%s.high>>>0).toNumber(%s):m%s\", prop, prop, prop, prop, isUnsigned ? \"true\": \"\", prop);\r\n                break;\r\n            case \"bytes\": gen\r\n            (\"d%s=o.bytes===String?util.base64.encode(m%s,0,m%s.length):o.bytes===Array?Array.prototype.slice.call(m%s):m%s\", prop, prop, prop, prop, prop);\r\n                break;\r\n            default: gen\r\n            (\"d%s=m%s\", prop, prop);\r\n                break;\r\n        }\r\n    }\r\n    return gen;\r\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\r\n}\r\n\r\n/**\r\n * Generates a runtime message to plain object converter specific to the specified message type.\r\n * @param {Type} mtype Message type\r\n * @returns {Codegen} Codegen instance\r\n */\r\nconverter.toObject = function toObject(mtype) {\r\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\r\n    var fields = mtype.fieldsArray.slice().sort(util.compareFieldsById);\r\n    if (!fields.length)\r\n        return util.codegen()(\"return {}\");\r\n    var gen = util.codegen([\"m\", \"o\"], mtype.name + \"$toObject\")\r\n    (\"if(!o)\")\r\n        (\"o={}\")\r\n    (\"var d={}\");\r\n\r\n    var repeatedFields = [],\r\n        mapFields = [],\r\n        normalFields = [],\r\n        i = 0;\r\n    for (; i < fields.length; ++i)\r\n        if (!fields[i].partOf)\r\n            ( fields[i].resolve().repeated ? repeatedFields\r\n            : fields[i].map ? mapFields\r\n            : normalFields).push(fields[i]);\r\n\r\n    if (repeatedFields.length) { gen\r\n    (\"if(o.arrays||o.defaults){\");\r\n        for (i = 0; i < repeatedFields.length; ++i) gen\r\n        (\"d%s=[]\", util.safeProp(repeatedFields[i].name));\r\n        gen\r\n    (\"}\");\r\n    }\r\n\r\n    if (mapFields.length) { gen\r\n    (\"if(o.objects||o.defaults){\");\r\n        for (i = 0; i < mapFields.length; ++i) gen\r\n        (\"d%s={}\", util.safeProp(mapFields[i].name));\r\n        gen\r\n    (\"}\");\r\n    }\r\n\r\n    if (normalFields.length) { gen\r\n    (\"if(o.defaults){\");\r\n        for (i = 0; i < normalFields.length; ++i) {\r\n            var field = normalFields[i],\r\n                prop  = util.safeProp(field.name);\r\n            if (field.resolvedType instanceof Enum) gen\r\n        (\"d%s=o.enums===String?%j:%j\", prop, field.resolvedType.valuesById[field.typeDefault], field.typeDefault);\r\n            else if (field.long) gen\r\n        (\"if(util.Long){\")\r\n            (\"var n=new util.Long(%i,%i,%j)\", field.typeDefault.low, field.typeDefault.high, field.typeDefault.unsigned)\r\n            (\"d%s=o.longs===String?n.toString():o.longs===Number?n.toNumber():n\", prop)\r\n        (\"}else\")\r\n            (\"d%s=o.longs===String?%j:%i\", prop, field.typeDefault.toString(), field.typeDefault.toNumber());\r\n            else if (field.bytes) {\r\n                var arrayDefault = \"[\" + Array.prototype.slice.call(field.typeDefault).join(\",\") + \"]\";\r\n                gen\r\n        (\"if(o.bytes===String)d%s=%j\", prop, String.fromCharCode.apply(String, field.typeDefault))\r\n        (\"else{\")\r\n            (\"d%s=%s\", prop, arrayDefault)\r\n            (\"if(o.bytes!==Array)d%s=util.newBuffer(d%s)\", prop, prop)\r\n        (\"}\");\r\n            } else gen\r\n        (\"d%s=%j\", prop, field.typeDefault); // also messages (=null)\r\n        } gen\r\n    (\"}\");\r\n    }\r\n    var hasKs2 = false;\r\n    for (i = 0; i < fields.length; ++i) {\r\n        var field = fields[i],\r\n            index = mtype._fieldsArray.indexOf(field),\r\n            prop  = util.safeProp(field.name);\r\n        if (field.map) {\r\n            if (!hasKs2) { hasKs2 = true; gen\r\n    (\"var ks2\");\r\n            } gen\r\n    (\"if(m%s&&(ks2=Object.keys(m%s)).length){\", prop, prop)\r\n        (\"d%s={}\", prop)\r\n        (\"for(var j=0;j<ks2.length;++j){\");\r\n            genValuePartial_toObject(gen, field, /* sorted */ index, prop + \"[ks2[j]]\")\r\n        (\"}\");\r\n        } else if (field.repeated) { gen\r\n    (\"if(m%s&&m%s.length){\", prop, prop)\r\n        (\"d%s=[]\", prop)\r\n        (\"for(var j=0;j<m%s.length;++j){\", prop);\r\n            genValuePartial_toObject(gen, field, /* sorted */ index, prop + \"[j]\")\r\n        (\"}\");\r\n        } else { gen\r\n    (\"if(m%s!=null&&m.hasOwnProperty(%j)){\", prop, field.name); // !== undefined && !== null\r\n        genValuePartial_toObject(gen, field, /* sorted */ index, prop);\r\n        if (field.partOf) gen\r\n        (\"if(o.oneofs)\")\r\n            (\"d%s=%j\", util.safeProp(field.partOf.name), field.name);\r\n        }\r\n        gen\r\n    (\"}\");\r\n    }\r\n    return gen\r\n    (\"return d\");\r\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\r\n};\r\n", "\"use strict\";\r\nmodule.exports = decoder;\r\n\r\nvar Enum    = require(15),\r\n    types   = require(36),\r\n    util    = require(37);\r\n\r\nfunction missing(field) {\r\n    return \"missing required '\" + field.name + \"'\";\r\n}\r\n\r\n/**\r\n * Generates a decoder specific to the specified message type.\r\n * @param {Type} mtype Message type\r\n * @returns {Codegen} Codegen instance\r\n */\r\nfunction decoder(mtype) {\r\n    /* eslint-disable no-unexpected-multiline */\r\n    var gen = util.codegen([\"r\", \"l\"], mtype.name + \"$decode\")\r\n    (\"if(!(r instanceof Reader))\")\r\n        (\"r=Reader.create(r)\")\r\n    (\"var c=l===undefined?r.len:r.pos+l,m=new this.ctor\" + (mtype.fieldsArray.filter(function(field) { return field.map; }).length ? \",k\" : \"\"))\r\n    (\"while(r.pos<c){\")\r\n        (\"var t=r.uint32()\");\r\n    if (mtype.group) gen\r\n        (\"if((t&7)===4)\")\r\n            (\"break\");\r\n    gen\r\n        (\"switch(t>>>3){\");\r\n\r\n    var i = 0;\r\n    for (; i < /* initializes */ mtype.fieldsArray.length; ++i) {\r\n        var field = mtype._fieldsArray[i].resolve(),\r\n            type  = field.resolvedType instanceof Enum ? \"int32\" : field.type,\r\n            ref   = \"m\" + util.safeProp(field.name); gen\r\n            (\"case %i:\", field.id);\r\n\r\n        // Map fields\r\n        if (field.map) { gen\r\n                (\"r.skip().pos++\") // assumes id 1 + key wireType\r\n                (\"if(%s===util.emptyObject)\", ref)\r\n                    (\"%s={}\", ref)\r\n                (\"k=r.%s()\", field.keyType)\r\n                (\"r.pos++\"); // assumes id 2 + value wireType\r\n            if (types.long[field.keyType] !== undefined) {\r\n                if (types.basic[type] === undefined) gen\r\n                (\"%s[typeof k===\\\"object\\\"?util.longToHash(k):k]=types[%i].decode(r,r.uint32())\", ref, i); // can't be groups\r\n                else gen\r\n                (\"%s[typeof k===\\\"object\\\"?util.longToHash(k):k]=r.%s()\", ref, type);\r\n            } else {\r\n                if (types.basic[type] === undefined) gen\r\n                (\"%s[k]=types[%i].decode(r,r.uint32())\", ref, i); // can't be groups\r\n                else gen\r\n                (\"%s[k]=r.%s()\", ref, type);\r\n            }\r\n\r\n        // Repeated fields\r\n        } else if (field.repeated) { gen\r\n\r\n                (\"if(!(%s&&%s.length))\", ref, ref)\r\n                    (\"%s=[]\", ref);\r\n\r\n            // Packable (always check for forward and backward compatiblity)\r\n            if (types.packed[type] !== undefined) gen\r\n                (\"if((t&7)===2){\")\r\n                    (\"var c2=r.uint32()+r.pos\")\r\n                    (\"while(r.pos<c2)\")\r\n                        (\"%s.push(r.%s())\", ref, type)\r\n                (\"}else\");\r\n\r\n            // Non-packed\r\n            if (types.basic[type] === undefined) gen(field.resolvedType.group\r\n                    ? \"%s.push(types[%i].decode(r))\"\r\n                    : \"%s.push(types[%i].decode(r,r.uint32()))\", ref, i);\r\n            else gen\r\n                    (\"%s.push(r.%s())\", ref, type);\r\n\r\n        // Non-repeated\r\n        } else if (types.basic[type] === undefined) gen(field.resolvedType.group\r\n                ? \"%s=types[%i].decode(r)\"\r\n                : \"%s=types[%i].decode(r,r.uint32())\", ref, i);\r\n        else gen\r\n                (\"%s=r.%s()\", ref, type);\r\n        gen\r\n                (\"break\");\r\n    // Unknown fields\r\n    } gen\r\n            (\"default:\")\r\n                (\"r.skipType(t&7)\")\r\n                (\"break\")\r\n\r\n        (\"}\")\r\n    (\"}\");\r\n\r\n    // Field presence\r\n    for (i = 0; i < mtype._fieldsArray.length; ++i) {\r\n        var rfield = mtype._fieldsArray[i];\r\n        if (rfield.required) gen\r\n    (\"if(!m.hasOwnProperty(%j))\", rfield.name)\r\n        (\"throw util.ProtocolError(%j,{instance:m})\", missing(rfield));\r\n    }\r\n\r\n    return gen\r\n    (\"return m\");\r\n    /* eslint-enable no-unexpected-multiline */\r\n}\r\n", "\"use strict\";\r\nmodule.exports = encoder;\r\n\r\nvar Enum     = require(15),\r\n    types    = require(36),\r\n    util     = require(37);\r\n\r\n/**\r\n * Generates a partial message type encoder.\r\n * @param {Codegen} gen Codegen instance\r\n * @param {Field} field Reflected field\r\n * @param {number} fieldIndex Field index\r\n * @param {string} ref Variable reference\r\n * @returns {Codegen} Codegen instance\r\n * @ignore\r\n */\r\nfunction genTypePartial(gen, field, fieldIndex, ref) {\r\n    return field.resolvedType.group\r\n        ? gen(\"types[%i].encode(%s,w.uint32(%i)).uint32(%i)\", fieldIndex, ref, (field.id << 3 | 3) >>> 0, (field.id << 3 | 4) >>> 0)\r\n        : gen(\"types[%i].encode(%s,w.uint32(%i).fork()).ldelim()\", fieldIndex, ref, (field.id << 3 | 2) >>> 0);\r\n}\r\n\r\n/**\r\n * Generates an encoder specific to the specified message type.\r\n * @param {Type} mtype Message type\r\n * @returns {Codegen} Codegen instance\r\n */\r\nfunction encoder(mtype) {\r\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\r\n    var gen = util.codegen([\"m\", \"w\"], mtype.name + \"$encode\")\r\n    (\"if(!w)\")\r\n        (\"w=Writer.create()\");\r\n\r\n    var i, ref;\r\n\r\n    // \"when a message is serialized its known fields should be written sequentially by field number\"\r\n    var fields = /* initializes */ mtype.fieldsArray.slice().sort(util.compareFieldsById);\r\n\r\n    for (var i = 0; i < fields.length; ++i) {\r\n        var field    = fields[i].resolve(),\r\n            index    = mtype._fieldsArray.indexOf(field),\r\n            type     = field.resolvedType instanceof Enum ? \"int32\" : field.type,\r\n            wireType = types.basic[type];\r\n            ref      = \"m\" + util.safeProp(field.name);\r\n\r\n        // Map fields\r\n        if (field.map) {\r\n            gen\r\n    (\"if(%s!=null&&m.hasOwnProperty(%j)){\", ref, field.name) // !== undefined && !== null\r\n        (\"for(var ks=Object.keys(%s),i=0;i<ks.length;++i){\", ref)\r\n            (\"w.uint32(%i).fork().uint32(%i).%s(ks[i])\", (field.id << 3 | 2) >>> 0, 8 | types.mapKey[field.keyType], field.keyType);\r\n            if (wireType === undefined) gen\r\n            (\"types[%i].encode(%s[ks[i]],w.uint32(18).fork()).ldelim().ldelim()\", index, ref); // can't be groups\r\n            else gen\r\n            (\".uint32(%i).%s(%s[ks[i]]).ldelim()\", 16 | wireType, type, ref);\r\n            gen\r\n        (\"}\")\r\n    (\"}\");\r\n\r\n            // Repeated fields\r\n        } else if (field.repeated) { gen\r\n    (\"if(%s!=null&&%s.length){\", ref, ref); // !== undefined && !== null\r\n\r\n            // Packed repeated\r\n            if (field.packed && types.packed[type] !== undefined) { gen\r\n\r\n        (\"w.uint32(%i).fork()\", (field.id << 3 | 2) >>> 0)\r\n        (\"for(var i=0;i<%s.length;++i)\", ref)\r\n            (\"w.%s(%s[i])\", type, ref)\r\n        (\"w.ldelim()\");\r\n\r\n            // Non-packed\r\n            } else { gen\r\n\r\n        (\"for(var i=0;i<%s.length;++i)\", ref);\r\n                if (wireType === undefined)\r\n            genTypePartial(gen, field, index, ref + \"[i]\");\r\n                else gen\r\n            (\"w.uint32(%i).%s(%s[i])\", (field.id << 3 | wireType) >>> 0, type, ref);\r\n\r\n            } gen\r\n    (\"}\");\r\n\r\n        // Non-repeated\r\n        } else {\r\n            if (field.optional) gen\r\n    (\"if(%s!=null&&m.hasOwnProperty(%j))\", ref, field.name); // !== undefined && !== null\r\n\r\n            if (wireType === undefined)\r\n        genTypePartial(gen, field, index, ref);\r\n            else gen\r\n        (\"w.uint32(%i).%s(%s)\", (field.id << 3 | wireType) >>> 0, type, ref);\r\n\r\n        }\r\n    }\r\n\r\n    return gen\r\n    (\"return w\");\r\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\r\n}", "\"use strict\";\r\nmodule.exports = Enum;\r\n\r\n// extends ReflectionObject\r\nvar ReflectionObject = require(24);\r\n((Enum.prototype = Object.create(ReflectionObject.prototype)).constructor = Enum).className = \"Enum\";\r\n\r\nvar Namespace = require(23),\r\n    util = require(37);\r\n\r\n/**\r\n * Constructs a new enum instance.\r\n * @classdesc Reflected enum.\r\n * @extends ReflectionObject\r\n * @constructor\r\n * @param {string} name Unique name within its namespace\r\n * @param {Object.<string,number>} [values] Enum values as an object, by name\r\n * @param {Object.<string,*>} [options] Declared options\r\n * @param {string} [comment] The comment for this enum\r\n * @param {Object.<string,string>} [comments] The value comments for this enum\r\n */\r\nfunction Enum(name, values, options, comment, comments) {\r\n    ReflectionObject.call(this, name, options);\r\n\r\n    if (values && typeof values !== \"object\")\r\n        throw TypeError(\"values must be an object\");\r\n\r\n    /**\r\n     * Enum values by id.\r\n     * @type {Object.<number,string>}\r\n     */\r\n    this.valuesById = {};\r\n\r\n    /**\r\n     * Enum values by name.\r\n     * @type {Object.<string,number>}\r\n     */\r\n    this.values = Object.create(this.valuesById); // toJSON, marker\r\n\r\n    /**\r\n     * Enum comment text.\r\n     * @type {string|null}\r\n     */\r\n    this.comment = comment;\r\n\r\n    /**\r\n     * Value comment texts, if any.\r\n     * @type {Object.<string,string>}\r\n     */\r\n    this.comments = comments || {};\r\n\r\n    /**\r\n     * Reserved ranges, if any.\r\n     * @type {Array.<number[]|string>}\r\n     */\r\n    this.reserved = undefined; // toJSON\r\n\r\n    // Note that values inherit valuesById on their prototype which makes them a TypeScript-\r\n    // compatible enum. This is used by pbts to write actual enum definitions that work for\r\n    // static and reflection code alike instead of emitting generic object definitions.\r\n\r\n    if (values)\r\n        for (var keys = Object.keys(values), i = 0; i < keys.length; ++i)\r\n            if (typeof values[keys[i]] === \"number\") // use forward entries only\r\n                this.valuesById[ this.values[keys[i]] = values[keys[i]] ] = keys[i];\r\n}\r\n\r\n/**\r\n * Enum descriptor.\r\n * @interface IEnum\r\n * @property {Object.<string,number>} values Enum values\r\n * @property {Object.<string,*>} [options] Enum options\r\n */\r\n\r\n/**\r\n * Constructs an enum from an enum descriptor.\r\n * @param {string} name Enum name\r\n * @param {IEnum} json Enum descriptor\r\n * @returns {Enum} Created enum\r\n * @throws {TypeError} If arguments are invalid\r\n */\r\nEnum.fromJSON = function fromJSON(name, json) {\r\n    var enm = new Enum(name, json.values, json.options, json.comment, json.comments);\r\n    enm.reserved = json.reserved;\r\n    return enm;\r\n};\r\n\r\n/**\r\n * Converts this enum to an enum descriptor.\r\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\r\n * @returns {IEnum} Enum descriptor\r\n */\r\nEnum.prototype.toJSON = function toJSON(toJSONOptions) {\r\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\r\n    return util.toObject([\r\n        \"options\"  , this.options,\r\n        \"values\"   , this.values,\r\n        \"reserved\" , this.reserved && this.reserved.length ? this.reserved : undefined,\r\n        \"comment\"  , keepComments ? this.comment : undefined,\r\n        \"comments\" , keepComments ? this.comments : undefined\r\n    ]);\r\n};\r\n\r\n/**\r\n * Adds a value to this enum.\r\n * @param {string} name Value name\r\n * @param {number} id Value id\r\n * @param {string} [comment] Comment, if any\r\n * @returns {Enum} `this`\r\n * @throws {TypeError} If arguments are invalid\r\n * @throws {Error} If there is already a value with this name or id\r\n */\r\nEnum.prototype.add = function add(name, id, comment) {\r\n    // utilized by the parser but not by .fromJSON\r\n\r\n    if (!util.isString(name))\r\n        throw TypeError(\"name must be a string\");\r\n\r\n    if (!util.isInteger(id))\r\n        throw TypeError(\"id must be an integer\");\r\n\r\n    if (this.values[name] !== undefined)\r\n        throw Error(\"duplicate name '\" + name + \"' in \" + this);\r\n\r\n    if (this.isReservedId(id))\r\n        throw Error(\"id \" + id + \" is reserved in \" + this);\r\n\r\n    if (this.isReservedName(name))\r\n        throw Error(\"name '\" + name + \"' is reserved in \" + this);\r\n\r\n    if (this.valuesById[id] !== undefined) {\r\n        if (!(this.options && this.options.allow_alias))\r\n            throw Error(\"duplicate id \" + id + \" in \" + this);\r\n        this.values[name] = id;\r\n    } else\r\n        this.valuesById[this.values[name] = id] = name;\r\n\r\n    this.comments[name] = comment || null;\r\n    return this;\r\n};\r\n\r\n/**\r\n * Removes a value from this enum\r\n * @param {string} name Value name\r\n * @returns {Enum} `this`\r\n * @throws {TypeError} If arguments are invalid\r\n * @throws {Error} If `name` is not a name of this enum\r\n */\r\nEnum.prototype.remove = function remove(name) {\r\n\r\n    if (!util.isString(name))\r\n        throw TypeError(\"name must be a string\");\r\n\r\n    var val = this.values[name];\r\n    if (val == null)\r\n        throw Error(\"name '\" + name + \"' does not exist in \" + this);\r\n\r\n    delete this.valuesById[val];\r\n    delete this.values[name];\r\n    delete this.comments[name];\r\n\r\n    return this;\r\n};\r\n\r\n/**\r\n * Tests if the specified id is reserved.\r\n * @param {number} id Id to test\r\n * @returns {boolean} `true` if reserved, otherwise `false`\r\n */\r\nEnum.prototype.isReservedId = function isReservedId(id) {\r\n    return Namespace.isReservedId(this.reserved, id);\r\n};\r\n\r\n/**\r\n * Tests if the specified name is reserved.\r\n * @param {string} name Name to test\r\n * @returns {boolean} `true` if reserved, otherwise `false`\r\n */\r\nEnum.prototype.isReservedName = function isReservedName(name) {\r\n    return Namespace.isReservedName(this.reserved, name);\r\n};\r\n", "\"use strict\";\r\nmodule.exports = Field;\r\n\r\n// extends ReflectionObject\r\nvar ReflectionObject = require(24);\r\n((Field.prototype = Object.create(ReflectionObject.prototype)).constructor = Field).className = \"Field\";\r\n\r\nvar Enum  = require(15),\r\n    types = require(36),\r\n    util  = require(37);\r\n\r\nvar Type; // cyclic\r\n\r\nvar ruleRe = /^required|optional|repeated$/;\r\n\r\n/**\r\n * Constructs a new message field instance. Note that {@link MapField|map fields} have their own class.\r\n * @name Field\r\n * @classdesc Reflected message field.\r\n * @extends FieldBase\r\n * @constructor\r\n * @param {string} name Unique name within its namespace\r\n * @param {number} id Unique id within its namespace\r\n * @param {string} type Value type\r\n * @param {string|Object.<string,*>} [rule=\"optional\"] Field rule\r\n * @param {string|Object.<string,*>} [extend] Extended type if different from parent\r\n * @param {Object.<string,*>} [options] Declared options\r\n */\r\n\r\n/**\r\n * Constructs a field from a field descriptor.\r\n * @param {string} name Field name\r\n * @param {IField} json Field descriptor\r\n * @returns {Field} Created field\r\n * @throws {TypeError} If arguments are invalid\r\n */\r\nField.fromJSON = function fromJSON(name, json) {\r\n    return new Field(name, json.id, json.type, json.rule, json.extend, json.options, json.comment);\r\n};\r\n\r\n/**\r\n * Not an actual constructor. Use {@link Field} instead.\r\n * @classdesc Base class of all reflected message fields. This is not an actual class but here for the sake of having consistent type definitions.\r\n * @exports FieldBase\r\n * @extends ReflectionObject\r\n * @constructor\r\n * @param {string} name Unique name within its namespace\r\n * @param {number} id Unique id within its namespace\r\n * @param {string} type Value type\r\n * @param {string|Object.<string,*>} [rule=\"optional\"] Field rule\r\n * @param {string|Object.<string,*>} [extend] Extended type if different from parent\r\n * @param {Object.<string,*>} [options] Declared options\r\n * @param {string} [comment] Comment associated with this field\r\n */\r\nfunction Field(name, id, type, rule, extend, options, comment) {\r\n\r\n    if (util.isObject(rule)) {\r\n        comment = extend;\r\n        options = rule;\r\n        rule = extend = undefined;\r\n    } else if (util.isObject(extend)) {\r\n        comment = options;\r\n        options = extend;\r\n        extend = undefined;\r\n    }\r\n\r\n    ReflectionObject.call(this, name, options);\r\n\r\n    if (!util.isInteger(id) || id < 0)\r\n        throw TypeError(\"id must be a non-negative integer\");\r\n\r\n    if (!util.isString(type))\r\n        throw TypeError(\"type must be a string\");\r\n\r\n    if (rule !== undefined && !ruleRe.test(rule = rule.toString().toLowerCase()))\r\n        throw TypeError(\"rule must be a string rule\");\r\n\r\n    if (extend !== undefined && !util.isString(extend))\r\n        throw TypeError(\"extend must be a string\");\r\n\r\n    /**\r\n     * Field rule, if any.\r\n     * @type {string|undefined}\r\n     */\r\n    this.rule = rule && rule !== \"optional\" ? rule : undefined; // toJSON\r\n\r\n    /**\r\n     * Field type.\r\n     * @type {string}\r\n     */\r\n    this.type = type; // toJSON\r\n\r\n    /**\r\n     * Unique field id.\r\n     * @type {number}\r\n     */\r\n    this.id = id; // toJSON, marker\r\n\r\n    /**\r\n     * Extended type if different from parent.\r\n     * @type {string|undefined}\r\n     */\r\n    this.extend = extend || undefined; // toJSON\r\n\r\n    /**\r\n     * Whether this field is required.\r\n     * @type {boolean}\r\n     */\r\n    this.required = rule === \"required\";\r\n\r\n    /**\r\n     * Whether this field is optional.\r\n     * @type {boolean}\r\n     */\r\n    this.optional = !this.required;\r\n\r\n    /**\r\n     * Whether this field is repeated.\r\n     * @type {boolean}\r\n     */\r\n    this.repeated = rule === \"repeated\";\r\n\r\n    /**\r\n     * Whether this field is a map or not.\r\n     * @type {boolean}\r\n     */\r\n    this.map = false;\r\n\r\n    /**\r\n     * Message this field belongs to.\r\n     * @type {Type|null}\r\n     */\r\n    this.message = null;\r\n\r\n    /**\r\n     * OneOf this field belongs to, if any,\r\n     * @type {OneOf|null}\r\n     */\r\n    this.partOf = null;\r\n\r\n    /**\r\n     * The field type's default value.\r\n     * @type {*}\r\n     */\r\n    this.typeDefault = null;\r\n\r\n    /**\r\n     * The field's default value on prototypes.\r\n     * @type {*}\r\n     */\r\n    this.defaultValue = null;\r\n\r\n    /**\r\n     * Whether this field's value should be treated as a long.\r\n     * @type {boolean}\r\n     */\r\n    this.long = util.Long ? types.long[type] !== undefined : /* istanbul ignore next */ false;\r\n\r\n    /**\r\n     * Whether this field's value is a buffer.\r\n     * @type {boolean}\r\n     */\r\n    this.bytes = type === \"bytes\";\r\n\r\n    /**\r\n     * Resolved type if not a basic type.\r\n     * @type {Type|Enum|null}\r\n     */\r\n    this.resolvedType = null;\r\n\r\n    /**\r\n     * Sister-field within the extended type if a declaring extension field.\r\n     * @type {Field|null}\r\n     */\r\n    this.extensionField = null;\r\n\r\n    /**\r\n     * Sister-field within the declaring namespace if an extended field.\r\n     * @type {Field|null}\r\n     */\r\n    this.declaringField = null;\r\n\r\n    /**\r\n     * Internally remembers whether this field is packed.\r\n     * @type {boolean|null}\r\n     * @private\r\n     */\r\n    this._packed = null;\r\n\r\n    /**\r\n     * Comment for this field.\r\n     * @type {string|null}\r\n     */\r\n    this.comment = comment;\r\n}\r\n\r\n/**\r\n * Determines whether this field is packed. Only relevant when repeated and working with proto2.\r\n * @name Field#packed\r\n * @type {boolean}\r\n * @readonly\r\n */\r\nObject.defineProperty(Field.prototype, \"packed\", {\r\n    get: function() {\r\n        // defaults to packed=true if not explicity set to false\r\n        if (this._packed === null)\r\n            this._packed = this.getOption(\"packed\") !== false;\r\n        return this._packed;\r\n    }\r\n});\r\n\r\n/**\r\n * @override\r\n */\r\nField.prototype.setOption = function setOption(name, value, ifNotSet) {\r\n    if (name === \"packed\") // clear cached before setting\r\n        this._packed = null;\r\n    return ReflectionObject.prototype.setOption.call(this, name, value, ifNotSet);\r\n};\r\n\r\n/**\r\n * Field descriptor.\r\n * @interface IField\r\n * @property {string} [rule=\"optional\"] Field rule\r\n * @property {string} type Field type\r\n * @property {number} id Field id\r\n * @property {Object.<string,*>} [options] Field options\r\n */\r\n\r\n/**\r\n * Extension field descriptor.\r\n * @interface IExtensionField\r\n * @extends IField\r\n * @property {string} extend Extended type\r\n */\r\n\r\n/**\r\n * Converts this field to a field descriptor.\r\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\r\n * @returns {IField} Field descriptor\r\n */\r\nField.prototype.toJSON = function toJSON(toJSONOptions) {\r\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\r\n    return util.toObject([\r\n        \"rule\"    , this.rule !== \"optional\" && this.rule || undefined,\r\n        \"type\"    , this.type,\r\n        \"id\"      , this.id,\r\n        \"extend\"  , this.extend,\r\n        \"options\" , this.options,\r\n        \"comment\" , keepComments ? this.comment : undefined\r\n    ]);\r\n};\r\n\r\n/**\r\n * Resolves this field's type references.\r\n * @returns {Field} `this`\r\n * @throws {Error} If any reference cannot be resolved\r\n */\r\nField.prototype.resolve = function resolve() {\r\n\r\n    if (this.resolved)\r\n        return this;\r\n\r\n    if ((this.typeDefault = types.defaults[this.type]) === undefined) { // if not a basic type, resolve it\r\n        this.resolvedType = (this.declaringField ? this.declaringField.parent : this.parent).lookupTypeOrEnum(this.type);\r\n        if (this.resolvedType instanceof Type)\r\n            this.typeDefault = null;\r\n        else // instanceof Enum\r\n            this.typeDefault = this.resolvedType.values[Object.keys(this.resolvedType.values)[0]]; // first defined\r\n    }\r\n\r\n    // use explicitly set default value if present\r\n    if (this.options && this.options[\"default\"] != null) {\r\n        this.typeDefault = this.options[\"default\"];\r\n        if (this.resolvedType instanceof Enum && typeof this.typeDefault === \"string\")\r\n            this.typeDefault = this.resolvedType.values[this.typeDefault];\r\n    }\r\n\r\n    // remove unnecessary options\r\n    if (this.options) {\r\n        if (this.options.packed === true || this.options.packed !== undefined && this.resolvedType && !(this.resolvedType instanceof Enum))\r\n            delete this.options.packed;\r\n        if (!Object.keys(this.options).length)\r\n            this.options = undefined;\r\n    }\r\n\r\n    // convert to internal data type if necesssary\r\n    if (this.long) {\r\n        this.typeDefault = util.Long.fromNumber(this.typeDefault, this.type.charAt(0) === \"u\");\r\n\r\n        /* istanbul ignore else */\r\n        if (Object.freeze)\r\n            Object.freeze(this.typeDefault); // long instances are meant to be immutable anyway (i.e. use small int cache that even requires it)\r\n\r\n    } else if (this.bytes && typeof this.typeDefault === \"string\") {\r\n        var buf;\r\n        if (util.base64.test(this.typeDefault))\r\n            util.base64.decode(this.typeDefault, buf = util.newBuffer(util.base64.length(this.typeDefault)), 0);\r\n        else\r\n            util.utf8.write(this.typeDefault, buf = util.newBuffer(util.utf8.length(this.typeDefault)), 0);\r\n        this.typeDefault = buf;\r\n    }\r\n\r\n    // take special care of maps and repeated fields\r\n    if (this.map)\r\n        this.defaultValue = util.emptyObject;\r\n    else if (this.repeated)\r\n        this.defaultValue = util.emptyArray;\r\n    else\r\n        this.defaultValue = this.typeDefault;\r\n\r\n    // ensure proper value on prototype\r\n    if (this.parent instanceof Type)\r\n        this.parent.ctor.prototype[this.name] = this.defaultValue;\r\n\r\n    return ReflectionObject.prototype.resolve.call(this);\r\n};\r\n\r\n/**\r\n * Decorator function as returned by {@link Field.d} and {@link MapField.d} (TypeScript).\r\n * @typedef FieldDecorator\r\n * @type {function}\r\n * @param {Object} prototype Target prototype\r\n * @param {string} fieldName Field name\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Field decorator (TypeScript).\r\n * @name Field.d\r\n * @function\r\n * @param {number} fieldId Field id\r\n * @param {\"double\"|\"float\"|\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"string\"|\"bool\"|\"bytes\"|Object} fieldType Field type\r\n * @param {\"optional\"|\"required\"|\"repeated\"} [fieldRule=\"optional\"] Field rule\r\n * @param {T} [defaultValue] Default value\r\n * @returns {FieldDecorator} Decorator function\r\n * @template T extends number | number[] | Long | Long[] | string | string[] | boolean | boolean[] | Uint8Array | Uint8Array[] | Buffer | Buffer[]\r\n */\r\nField.d = function decorateField(fieldId, fieldType, fieldRule, defaultValue) {\r\n\r\n    // submessage: decorate the submessage and use its name as the type\r\n    if (typeof fieldType === \"function\")\r\n        fieldType = util.decorateType(fieldType).name;\r\n\r\n    // enum reference: create a reflected copy of the enum and keep reuseing it\r\n    else if (fieldType && typeof fieldType === \"object\")\r\n        fieldType = util.decorateEnum(fieldType).name;\r\n\r\n    return function fieldDecorator(prototype, fieldName) {\r\n        util.decorateType(prototype.constructor)\r\n            .add(new Field(fieldName, fieldId, fieldType, fieldRule, { \"default\": defaultValue }));\r\n    };\r\n};\r\n\r\n/**\r\n * Field decorator (TypeScript).\r\n * @name Field.d\r\n * @function\r\n * @param {number} fieldId Field id\r\n * @param {Constructor<T>|string} fieldType Field type\r\n * @param {\"optional\"|\"required\"|\"repeated\"} [fieldRule=\"optional\"] Field rule\r\n * @returns {FieldDecorator} Decorator function\r\n * @template T extends Message<T>\r\n * @variation 2\r\n */\r\n// like Field.d but without a default value\r\n\r\n// Sets up cyclic dependencies (called in index-light)\r\nField._configure = function configure(Type_) {\r\n    Type = Type_;\r\n};\r\n", "\"use strict\";\r\nvar protobuf = module.exports = require(18);\r\n\r\nprotobuf.build = \"light\";\r\n\r\n/**\r\n * A node-style callback as used by {@link load} and {@link Root#load}.\r\n * @typedef LoadCallback\r\n * @type {function}\r\n * @param {Error|null} error Error, if any, otherwise `null`\r\n * @param {Root} [root] Root, if there hasn't been an error\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and calls the callback.\r\n * @param {string|string[]} filename One or multiple files to load\r\n * @param {Root} root Root namespace, defaults to create a new one if omitted.\r\n * @param {LoadCallback} callback Callback function\r\n * @returns {undefined}\r\n * @see {@link Root#load}\r\n */\r\nfunction load(filename, root, callback) {\r\n    if (typeof root === \"function\") {\r\n        callback = root;\r\n        root = new protobuf.Root();\r\n    } else if (!root)\r\n        root = new protobuf.Root();\r\n    return root.load(filename, callback);\r\n}\r\n\r\n/**\r\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and calls the callback.\r\n * @name load\r\n * @function\r\n * @param {string|string[]} filename One or multiple files to load\r\n * @param {LoadCallback} callback Callback function\r\n * @returns {undefined}\r\n * @see {@link Root#load}\r\n * @variation 2\r\n */\r\n// function load(filename:string, callback:LoadCallback):undefined\r\n\r\n/**\r\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and returns a promise.\r\n * @name load\r\n * @function\r\n * @param {string|string[]} filename One or multiple files to load\r\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted.\r\n * @returns {Promise<Root>} Promise\r\n * @see {@link Root#load}\r\n * @variation 3\r\n */\r\n// function load(filename:string, [root:Root]):Promise<Root>\r\n\r\nprotobuf.load = load;\r\n\r\n/**\r\n * Synchronously loads one or multiple .proto or preprocessed .json files into a common root namespace (node only).\r\n * @param {string|string[]} filename One or multiple files to load\r\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted.\r\n * @returns {Root} Root namespace\r\n * @throws {Error} If synchronous fetching is not supported (i.e. in browsers) or if a file's syntax is invalid\r\n * @see {@link Root#loadSync}\r\n */\r\nfunction loadSync(filename, root) {\r\n    if (!root)\r\n        root = new protobuf.Root();\r\n    return root.loadSync(filename);\r\n}\r\n\r\nprotobuf.loadSync = loadSync;\r\n\r\n// Serialization\r\nprotobuf.encoder          = require(14);\r\nprotobuf.decoder          = require(13);\r\nprotobuf.verifier         = require(40);\r\nprotobuf.converter        = require(12);\r\n\r\n// Reflection\r\nprotobuf.ReflectionObject = require(24);\r\nprotobuf.Namespace        = require(23);\r\nprotobuf.Root             = require(29);\r\nprotobuf.Enum             = require(15);\r\nprotobuf.Type             = require(35);\r\nprotobuf.Field            = require(16);\r\nprotobuf.OneOf            = require(25);\r\nprotobuf.MapField         = require(20);\r\nprotobuf.Service          = require(33);\r\nprotobuf.Method           = require(22);\r\n\r\n// Runtime\r\nprotobuf.Message          = require(21);\r\nprotobuf.wrappers         = require(41);\r\n\r\n// Utility\r\nprotobuf.types            = require(36);\r\nprotobuf.util             = require(37);\r\n\r\n// Set up possibly cyclic reflection dependencies\r\nprotobuf.ReflectionObject._configure(protobuf.Root);\r\nprotobuf.Namespace._configure(protobuf.Type, protobuf.Service, protobuf.Enum);\r\nprotobuf.Root._configure(protobuf.Type);\r\nprotobuf.Field._configure(protobuf.Type);\r\n", "\"use strict\";\r\nvar protobuf = exports;\r\n\r\n/**\r\n * Build type, one of `\"full\"`, `\"light\"` or `\"minimal\"`.\r\n * @name build\r\n * @type {string}\r\n * @const\r\n */\r\nprotobuf.build = \"minimal\";\r\n\r\n// Serialization\r\nprotobuf.Writer       = require(42);\r\nprotobuf.BufferWriter = require(43);\r\nprotobuf.Reader       = require(27);\r\nprotobuf.BufferReader = require(28);\r\n\r\n// Utility\r\nprotobuf.util         = require(39);\r\nprotobuf.rpc          = require(31);\r\nprotobuf.roots        = require(30);\r\nprotobuf.configure    = configure;\r\n\r\n/* istanbul ignore next */\r\n/**\r\n * Reconfigures the library according to the environment.\r\n * @returns {undefined}\r\n */\r\nfunction configure() {\r\n    protobuf.Reader._configure(protobuf.BufferReader);\r\n    protobuf.util._configure();\r\n}\r\n\r\n// Set up buffer utility according to the environment\r\nprotobuf.Writer._configure(protobuf.BufferWriter);\r\nconfigure();\r\n", "\"use strict\";\r\nvar protobuf = module.exports = require(17);\r\n\r\nprotobuf.build = \"full\";\r\n\r\n// Parser\r\nprotobuf.tokenize         = require(34);\r\nprotobuf.parse            = require(26);\r\nprotobuf.common           = require(11);\r\n\r\n// Configure parser\r\nprotobuf.Root._configure(protobuf.Type, protobuf.parse, protobuf.common);\r\n", "\"use strict\";\r\nmodule.exports = MapField;\r\n\r\n// extends Field\r\nvar Field = require(16);\r\n((MapField.prototype = Object.create(Field.prototype)).constructor = MapField).className = \"MapField\";\r\n\r\nvar types   = require(36),\r\n    util    = require(37);\r\n\r\n/**\r\n * Constructs a new map field instance.\r\n * @classdesc Reflected map field.\r\n * @extends FieldBase\r\n * @constructor\r\n * @param {string} name Unique name within its namespace\r\n * @param {number} id Unique id within its namespace\r\n * @param {string} keyType Key type\r\n * @param {string} type Value type\r\n * @param {Object.<string,*>} [options] Declared options\r\n * @param {string} [comment] Comment associated with this field\r\n */\r\nfunction MapField(name, id, keyType, type, options, comment) {\r\n    Field.call(this, name, id, type, undefined, undefined, options, comment);\r\n\r\n    /* istanbul ignore if */\r\n    if (!util.isString(keyType))\r\n        throw TypeError(\"keyType must be a string\");\r\n\r\n    /**\r\n     * Key type.\r\n     * @type {string}\r\n     */\r\n    this.keyType = keyType; // toJSON, marker\r\n\r\n    /**\r\n     * Resolved key type if not a basic type.\r\n     * @type {ReflectionObject|null}\r\n     */\r\n    this.resolvedKeyType = null;\r\n\r\n    // Overrides Field#map\r\n    this.map = true;\r\n}\r\n\r\n/**\r\n * Map field descriptor.\r\n * @interface IMapField\r\n * @extends {IField}\r\n * @property {string} keyType Key type\r\n */\r\n\r\n/**\r\n * Extension map field descriptor.\r\n * @interface IExtensionMapField\r\n * @extends IMapField\r\n * @property {string} extend Extended type\r\n */\r\n\r\n/**\r\n * Constructs a map field from a map field descriptor.\r\n * @param {string} name Field name\r\n * @param {IMapField} json Map field descriptor\r\n * @returns {MapField} Created map field\r\n * @throws {TypeError} If arguments are invalid\r\n */\r\nMapField.fromJSON = function fromJSON(name, json) {\r\n    return new MapField(name, json.id, json.keyType, json.type, json.options, json.comment);\r\n};\r\n\r\n/**\r\n * Converts this map field to a map field descriptor.\r\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\r\n * @returns {IMapField} Map field descriptor\r\n */\r\nMapField.prototype.toJSON = function toJSON(toJSONOptions) {\r\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\r\n    return util.toObject([\r\n        \"keyType\" , this.keyType,\r\n        \"type\"    , this.type,\r\n        \"id\"      , this.id,\r\n        \"extend\"  , this.extend,\r\n        \"options\" , this.options,\r\n        \"comment\" , keepComments ? this.comment : undefined\r\n    ]);\r\n};\r\n\r\n/**\r\n * @override\r\n */\r\nMapField.prototype.resolve = function resolve() {\r\n    if (this.resolved)\r\n        return this;\r\n\r\n    // Besides a value type, map fields have a key type that may be \"any scalar type except for floating point types and bytes\"\r\n    if (types.mapKey[this.keyType] === undefined)\r\n        throw Error(\"invalid key type: \" + this.keyType);\r\n\r\n    return Field.prototype.resolve.call(this);\r\n};\r\n\r\n/**\r\n * Map field decorator (TypeScript).\r\n * @name MapField.d\r\n * @function\r\n * @param {number} fieldId Field id\r\n * @param {\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"bool\"|\"string\"} fieldKeyType Field key type\r\n * @param {\"double\"|\"float\"|\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"bool\"|\"string\"|\"bytes\"|Object|Constructor<{}>} fieldValueType Field value type\r\n * @returns {FieldDecorator} Decorator function\r\n * @template T extends { [key: string]: number | Long | string | boolean | Uint8Array | Buffer | number[] | Message<{}> }\r\n */\r\nMapField.d = function decorateMapField(fieldId, fieldKeyType, fieldValueType) {\r\n\r\n    // submessage value: decorate the submessage and use its name as the type\r\n    if (typeof fieldValueType === \"function\")\r\n        fieldValueType = util.decorateType(fieldValueType).name;\r\n\r\n    // enum reference value: create a reflected copy of the enum and keep reuseing it\r\n    else if (fieldValueType && typeof fieldValueType === \"object\")\r\n        fieldValueType = util.decorateEnum(fieldValueType).name;\r\n\r\n    return function mapFieldDecorator(prototype, fieldName) {\r\n        util.decorateType(prototype.constructor)\r\n            .add(new MapField(fieldName, fieldId, fieldKeyType, fieldValueType));\r\n    };\r\n};\r\n", "\"use strict\";\r\nmodule.exports = Message;\r\n\r\nvar util = require(39);\r\n\r\n/**\r\n * Constructs a new message instance.\r\n * @classdesc Abstract runtime message.\r\n * @constructor\r\n * @param {Properties<T>} [properties] Properties to set\r\n * @template T extends object = object\r\n */\r\nfunction Message(properties) {\r\n    // not used internally\r\n    if (properties)\r\n        for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\r\n            this[keys[i]] = properties[keys[i]];\r\n}\r\n\r\n/**\r\n * Reference to the reflected type.\r\n * @name Message.$type\r\n * @type {Type}\r\n * @readonly\r\n */\r\n\r\n/**\r\n * Reference to the reflected type.\r\n * @name Message#$type\r\n * @type {Type}\r\n * @readonly\r\n */\r\n\r\n/*eslint-disable valid-jsdoc*/\r\n\r\n/**\r\n * Creates a new message of this type using the specified properties.\r\n * @param {Object.<string,*>} [properties] Properties to set\r\n * @returns {Message<T>} Message instance\r\n * @template T extends Message<T>\r\n * @this Constructor<T>\r\n */\r\nMessage.create = function create(properties) {\r\n    return this.$type.create(properties);\r\n};\r\n\r\n/**\r\n * Encodes a message of this type.\r\n * @param {T|Object.<string,*>} message Message to encode\r\n * @param {Writer} [writer] Writer to use\r\n * @returns {Writer} Writer\r\n * @template T extends Message<T>\r\n * @this Constructor<T>\r\n */\r\nMessage.encode = function encode(message, writer) {\r\n    return this.$type.encode(message, writer);\r\n};\r\n\r\n/**\r\n * Encodes a message of this type preceeded by its length as a varint.\r\n * @param {T|Object.<string,*>} message Message to encode\r\n * @param {Writer} [writer] Writer to use\r\n * @returns {Writer} Writer\r\n * @template T extends Message<T>\r\n * @this Constructor<T>\r\n */\r\nMessage.encodeDelimited = function encodeDelimited(message, writer) {\r\n    return this.$type.encodeDelimited(message, writer);\r\n};\r\n\r\n/**\r\n * Decodes a message of this type.\r\n * @name Message.decode\r\n * @function\r\n * @param {Reader|Uint8Array} reader Reader or buffer to decode\r\n * @returns {T} Decoded message\r\n * @template T extends Message<T>\r\n * @this Constructor<T>\r\n */\r\nMessage.decode = function decode(reader) {\r\n    return this.$type.decode(reader);\r\n};\r\n\r\n/**\r\n * Decodes a message of this type preceeded by its length as a varint.\r\n * @name Message.decodeDelimited\r\n * @function\r\n * @param {Reader|Uint8Array} reader Reader or buffer to decode\r\n * @returns {T} Decoded message\r\n * @template T extends Message<T>\r\n * @this Constructor<T>\r\n */\r\nMessage.decodeDelimited = function decodeDelimited(reader) {\r\n    return this.$type.decodeDelimited(reader);\r\n};\r\n\r\n/**\r\n * Verifies a message of this type.\r\n * @name Message.verify\r\n * @function\r\n * @param {Object.<string,*>} message Plain object to verify\r\n * @returns {string|null} `null` if valid, otherwise the reason why it is not\r\n */\r\nMessage.verify = function verify(message) {\r\n    return this.$type.verify(message);\r\n};\r\n\r\n/**\r\n * Creates a new message of this type from a plain object. Also converts values to their respective internal types.\r\n * @param {Object.<string,*>} object Plain object\r\n * @returns {T} Message instance\r\n * @template T extends Message<T>\r\n * @this Constructor<T>\r\n */\r\nMessage.fromObject = function fromObject(object) {\r\n    return this.$type.fromObject(object);\r\n};\r\n\r\n/**\r\n * Creates a plain object from a message of this type. Also converts values to other types if specified.\r\n * @param {T} message Message instance\r\n * @param {IConversionOptions} [options] Conversion options\r\n * @returns {Object.<string,*>} Plain object\r\n * @template T extends Message<T>\r\n * @this Constructor<T>\r\n */\r\nMessage.toObject = function toObject(message, options) {\r\n    return this.$type.toObject(message, options);\r\n};\r\n\r\n/**\r\n * Converts this message to JSON.\r\n * @returns {Object.<string,*>} JSON object\r\n */\r\nMessage.prototype.toJSON = function toJSON() {\r\n    return this.$type.toObject(this, util.toJSONOptions);\r\n};\r\n\r\n/*eslint-enable valid-jsdoc*/", "\"use strict\";\r\nmodule.exports = Method;\r\n\r\n// extends ReflectionObject\r\nvar ReflectionObject = require(24);\r\n((Method.prototype = Object.create(ReflectionObject.prototype)).constructor = Method).className = \"Method\";\r\n\r\nvar util = require(37);\r\n\r\n/**\r\n * Constructs a new service method instance.\r\n * @classdesc Reflected service method.\r\n * @extends ReflectionObject\r\n * @constructor\r\n * @param {string} name Method name\r\n * @param {string|undefined} type Method type, usually `\"rpc\"`\r\n * @param {string} requestType Request message type\r\n * @param {string} responseType Response message type\r\n * @param {boolean|Object.<string,*>} [requestStream] Whether the request is streamed\r\n * @param {boolean|Object.<string,*>} [responseStream] Whether the response is streamed\r\n * @param {Object.<string,*>} [options] Declared options\r\n * @param {string} [comment] The comment for this method\r\n */\r\nfunction Method(name, type, requestType, responseType, requestStream, responseStream, options, comment) {\r\n\r\n    /* istanbul ignore next */\r\n    if (util.isObject(requestStream)) {\r\n        options = requestStream;\r\n        requestStream = responseStream = undefined;\r\n    } else if (util.isObject(responseStream)) {\r\n        options = responseStream;\r\n        responseStream = undefined;\r\n    }\r\n\r\n    /* istanbul ignore if */\r\n    if (!(type === undefined || util.isString(type)))\r\n        throw TypeError(\"type must be a string\");\r\n\r\n    /* istanbul ignore if */\r\n    if (!util.isString(requestType))\r\n        throw TypeError(\"requestType must be a string\");\r\n\r\n    /* istanbul ignore if */\r\n    if (!util.isString(responseType))\r\n        throw TypeError(\"responseType must be a string\");\r\n\r\n    ReflectionObject.call(this, name, options);\r\n\r\n    /**\r\n     * Method type.\r\n     * @type {string}\r\n     */\r\n    this.type = type || \"rpc\"; // toJSON\r\n\r\n    /**\r\n     * Request type.\r\n     * @type {string}\r\n     */\r\n    this.requestType = requestType; // toJSON, marker\r\n\r\n    /**\r\n     * Whether requests are streamed or not.\r\n     * @type {boolean|undefined}\r\n     */\r\n    this.requestStream = requestStream ? true : undefined; // toJSON\r\n\r\n    /**\r\n     * Response type.\r\n     * @type {string}\r\n     */\r\n    this.responseType = responseType; // toJSON\r\n\r\n    /**\r\n     * Whether responses are streamed or not.\r\n     * @type {boolean|undefined}\r\n     */\r\n    this.responseStream = responseStream ? true : undefined; // toJSON\r\n\r\n    /**\r\n     * Resolved request type.\r\n     * @type {Type|null}\r\n     */\r\n    this.resolvedRequestType = null;\r\n\r\n    /**\r\n     * Resolved response type.\r\n     * @type {Type|null}\r\n     */\r\n    this.resolvedResponseType = null;\r\n\r\n    /**\r\n     * Comment for this method\r\n     * @type {string|null}\r\n     */\r\n    this.comment = comment;\r\n}\r\n\r\n/**\r\n * Method descriptor.\r\n * @interface IMethod\r\n * @property {string} [type=\"rpc\"] Method type\r\n * @property {string} requestType Request type\r\n * @property {string} responseType Response type\r\n * @property {boolean} [requestStream=false] Whether requests are streamed\r\n * @property {boolean} [responseStream=false] Whether responses are streamed\r\n * @property {Object.<string,*>} [options] Method options\r\n */\r\n\r\n/**\r\n * Constructs a method from a method descriptor.\r\n * @param {string} name Method name\r\n * @param {IMethod} json Method descriptor\r\n * @returns {Method} Created method\r\n * @throws {TypeError} If arguments are invalid\r\n */\r\nMethod.fromJSON = function fromJSON(name, json) {\r\n    return new Method(name, json.type, json.requestType, json.responseType, json.requestStream, json.responseStream, json.options, json.comment);\r\n};\r\n\r\n/**\r\n * Converts this method to a method descriptor.\r\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\r\n * @returns {IMethod} Method descriptor\r\n */\r\nMethod.prototype.toJSON = function toJSON(toJSONOptions) {\r\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\r\n    return util.toObject([\r\n        \"type\"           , this.type !== \"rpc\" && /* istanbul ignore next */ this.type || undefined,\r\n        \"requestType\"    , this.requestType,\r\n        \"requestStream\"  , this.requestStream,\r\n        \"responseType\"   , this.responseType,\r\n        \"responseStream\" , this.responseStream,\r\n        \"options\"        , this.options,\r\n        \"comment\"        , keepComments ? this.comment : undefined\r\n    ]);\r\n};\r\n\r\n/**\r\n * @override\r\n */\r\nMethod.prototype.resolve = function resolve() {\r\n\r\n    /* istanbul ignore if */\r\n    if (this.resolved)\r\n        return this;\r\n\r\n    this.resolvedRequestType = this.parent.lookupType(this.requestType);\r\n    this.resolvedResponseType = this.parent.lookupType(this.responseType);\r\n\r\n    return ReflectionObject.prototype.resolve.call(this);\r\n};\r\n", "\"use strict\";\r\nmodule.exports = Namespace;\r\n\r\n// extends ReflectionObject\r\nvar ReflectionObject = require(24);\r\n((Namespace.prototype = Object.create(ReflectionObject.prototype)).constructor = Namespace).className = \"Namespace\";\r\n\r\nvar Field    = require(16),\r\n    util     = require(37);\r\n\r\nvar Type,    // cyclic\r\n    Service,\r\n    Enum;\r\n\r\n/**\r\n * Constructs a new namespace instance.\r\n * @name Namespace\r\n * @classdesc Reflected namespace.\r\n * @extends NamespaceBase\r\n * @constructor\r\n * @param {string} name Namespace name\r\n * @param {Object.<string,*>} [options] Declared options\r\n */\r\n\r\n/**\r\n * Constructs a namespace from JSON.\r\n * @memberof Namespace\r\n * @function\r\n * @param {string} name Namespace name\r\n * @param {Object.<string,*>} json JSON object\r\n * @returns {Namespace} Created namespace\r\n * @throws {TypeError} If arguments are invalid\r\n */\r\nNamespace.fromJSON = function fromJSON(name, json) {\r\n    return new Namespace(name, json.options).addJSON(json.nested);\r\n};\r\n\r\n/**\r\n * Converts an array of reflection objects to JSON.\r\n * @memberof Namespace\r\n * @param {ReflectionObject[]} array Object array\r\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\r\n * @returns {Object.<string,*>|undefined} JSON object or `undefined` when array is empty\r\n */\r\nfunction arrayToJSON(array, toJSONOptions) {\r\n    if (!(array && array.length))\r\n        return undefined;\r\n    var obj = {};\r\n    for (var i = 0; i < array.length; ++i)\r\n        obj[array[i].name] = array[i].toJSON(toJSONOptions);\r\n    return obj;\r\n}\r\n\r\nNamespace.arrayToJSON = arrayToJSON;\r\n\r\n/**\r\n * Tests if the specified id is reserved.\r\n * @param {Array.<number[]|string>|undefined} reserved Array of reserved ranges and names\r\n * @param {number} id Id to test\r\n * @returns {boolean} `true` if reserved, otherwise `false`\r\n */\r\nNamespace.isReservedId = function isReservedId(reserved, id) {\r\n    if (reserved)\r\n        for (var i = 0; i < reserved.length; ++i)\r\n            if (typeof reserved[i] !== \"string\" && reserved[i][0] <= id && reserved[i][1] >= id)\r\n                return true;\r\n    return false;\r\n};\r\n\r\n/**\r\n * Tests if the specified name is reserved.\r\n * @param {Array.<number[]|string>|undefined} reserved Array of reserved ranges and names\r\n * @param {string} name Name to test\r\n * @returns {boolean} `true` if reserved, otherwise `false`\r\n */\r\nNamespace.isReservedName = function isReservedName(reserved, name) {\r\n    if (reserved)\r\n        for (var i = 0; i < reserved.length; ++i)\r\n            if (reserved[i] === name)\r\n                return true;\r\n    return false;\r\n};\r\n\r\n/**\r\n * Not an actual constructor. Use {@link Namespace} instead.\r\n * @classdesc Base class of all reflection objects containing nested objects. This is not an actual class but here for the sake of having consistent type definitions.\r\n * @exports NamespaceBase\r\n * @extends ReflectionObject\r\n * @abstract\r\n * @constructor\r\n * @param {string} name Namespace name\r\n * @param {Object.<string,*>} [options] Declared options\r\n * @see {@link Namespace}\r\n */\r\nfunction Namespace(name, options) {\r\n    ReflectionObject.call(this, name, options);\r\n\r\n    /**\r\n     * Nested objects by name.\r\n     * @type {Object.<string,ReflectionObject>|undefined}\r\n     */\r\n    this.nested = undefined; // toJSON\r\n\r\n    /**\r\n     * Cached nested objects as an array.\r\n     * @type {ReflectionObject[]|null}\r\n     * @private\r\n     */\r\n    this._nestedArray = null;\r\n}\r\n\r\nfunction clearCache(namespace) {\r\n    namespace._nestedArray = null;\r\n    return namespace;\r\n}\r\n\r\n/**\r\n * Nested objects of this namespace as an array for iteration.\r\n * @name NamespaceBase#nestedArray\r\n * @type {ReflectionObject[]}\r\n * @readonly\r\n */\r\nObject.defineProperty(Namespace.prototype, \"nestedArray\", {\r\n    get: function() {\r\n        return this._nestedArray || (this._nestedArray = util.toArray(this.nested));\r\n    }\r\n});\r\n\r\n/**\r\n * Namespace descriptor.\r\n * @interface INamespace\r\n * @property {Object.<string,*>} [options] Namespace options\r\n * @property {Object.<string,AnyNestedObject>} [nested] Nested object descriptors\r\n */\r\n\r\n/**\r\n * Any extension field descriptor.\r\n * @typedef AnyExtensionField\r\n * @type {IExtensionField|IExtensionMapField}\r\n */\r\n\r\n/**\r\n * Any nested object descriptor.\r\n * @typedef AnyNestedObject\r\n * @type {IEnum|IType|IService|AnyExtensionField|INamespace}\r\n */\r\n// ^ BEWARE: VSCode hangs forever when using more than 5 types (that's why AnyExtensionField exists in the first place)\r\n\r\n/**\r\n * Converts this namespace to a namespace descriptor.\r\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\r\n * @returns {INamespace} Namespace descriptor\r\n */\r\nNamespace.prototype.toJSON = function toJSON(toJSONOptions) {\r\n    return util.toObject([\r\n        \"options\" , this.options,\r\n        \"nested\"  , arrayToJSON(this.nestedArray, toJSONOptions)\r\n    ]);\r\n};\r\n\r\n/**\r\n * Adds nested objects to this namespace from nested object descriptors.\r\n * @param {Object.<string,AnyNestedObject>} nestedJson Any nested object descriptors\r\n * @returns {Namespace} `this`\r\n */\r\nNamespace.prototype.addJSON = function addJSON(nestedJson) {\r\n    var ns = this;\r\n    /* istanbul ignore else */\r\n    if (nestedJson) {\r\n        for (var names = Object.keys(nestedJson), i = 0, nested; i < names.length; ++i) {\r\n            nested = nestedJson[names[i]];\r\n            ns.add( // most to least likely\r\n                ( nested.fields !== undefined\r\n                ? Type.fromJSON\r\n                : nested.values !== undefined\r\n                ? Enum.fromJSON\r\n                : nested.methods !== undefined\r\n                ? Service.fromJSON\r\n                : nested.id !== undefined\r\n                ? Field.fromJSON\r\n                : Namespace.fromJSON )(names[i], nested)\r\n            );\r\n        }\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Gets the nested object of the specified name.\r\n * @param {string} name Nested object name\r\n * @returns {ReflectionObject|null} The reflection object or `null` if it doesn't exist\r\n */\r\nNamespace.prototype.get = function get(name) {\r\n    return this.nested && this.nested[name]\r\n        || null;\r\n};\r\n\r\n/**\r\n * Gets the values of the nested {@link Enum|enum} of the specified name.\r\n * This methods differs from {@link Namespace#get|get} in that it returns an enum's values directly and throws instead of returning `null`.\r\n * @param {string} name Nested enum name\r\n * @returns {Object.<string,number>} Enum values\r\n * @throws {Error} If there is no such enum\r\n */\r\nNamespace.prototype.getEnum = function getEnum(name) {\r\n    if (this.nested && this.nested[name] instanceof Enum)\r\n        return this.nested[name].values;\r\n    throw Error(\"no such enum: \" + name);\r\n};\r\n\r\n/**\r\n * Adds a nested object to this namespace.\r\n * @param {ReflectionObject} object Nested object to add\r\n * @returns {Namespace} `this`\r\n * @throws {TypeError} If arguments are invalid\r\n * @throws {Error} If there is already a nested object with this name\r\n */\r\nNamespace.prototype.add = function add(object) {\r\n\r\n    if (!(object instanceof Field && object.extend !== undefined || object instanceof Type || object instanceof Enum || object instanceof Service || object instanceof Namespace))\r\n        throw TypeError(\"object must be a valid nested object\");\r\n\r\n    if (!this.nested)\r\n        this.nested = {};\r\n    else {\r\n        var prev = this.get(object.name);\r\n        if (prev) {\r\n            if (prev instanceof Namespace && object instanceof Namespace && !(prev instanceof Type || prev instanceof Service)) {\r\n                // replace plain namespace but keep existing nested elements and options\r\n                var nested = prev.nestedArray;\r\n                for (var i = 0; i < nested.length; ++i)\r\n                    object.add(nested[i]);\r\n                this.remove(prev);\r\n                if (!this.nested)\r\n                    this.nested = {};\r\n                object.setOptions(prev.options, true);\r\n\r\n            } else\r\n                throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\r\n        }\r\n    }\r\n    this.nested[object.name] = object;\r\n    object.onAdd(this);\r\n    return clearCache(this);\r\n};\r\n\r\n/**\r\n * Removes a nested object from this namespace.\r\n * @param {ReflectionObject} object Nested object to remove\r\n * @returns {Namespace} `this`\r\n * @throws {TypeError} If arguments are invalid\r\n * @throws {Error} If `object` is not a member of this namespace\r\n */\r\nNamespace.prototype.remove = function remove(object) {\r\n\r\n    if (!(object instanceof ReflectionObject))\r\n        throw TypeError(\"object must be a ReflectionObject\");\r\n    if (object.parent !== this)\r\n        throw Error(object + \" is not a member of \" + this);\r\n\r\n    delete this.nested[object.name];\r\n    if (!Object.keys(this.nested).length)\r\n        this.nested = undefined;\r\n\r\n    object.onRemove(this);\r\n    return clearCache(this);\r\n};\r\n\r\n/**\r\n * Defines additial namespaces within this one if not yet existing.\r\n * @param {string|string[]} path Path to create\r\n * @param {*} [json] Nested types to create from JSON\r\n * @returns {Namespace} Pointer to the last namespace created or `this` if path is empty\r\n */\r\nNamespace.prototype.define = function define(path, json) {\r\n\r\n    if (util.isString(path))\r\n        path = path.split(\".\");\r\n    else if (!Array.isArray(path))\r\n        throw TypeError(\"illegal path\");\r\n    if (path && path.length && path[0] === \"\")\r\n        throw Error(\"path must be relative\");\r\n\r\n    var ptr = this;\r\n    while (path.length > 0) {\r\n        var part = path.shift();\r\n        if (ptr.nested && ptr.nested[part]) {\r\n            ptr = ptr.nested[part];\r\n            if (!(ptr instanceof Namespace))\r\n                throw Error(\"path conflicts with non-namespace objects\");\r\n        } else\r\n            ptr.add(ptr = new Namespace(part));\r\n    }\r\n    if (json)\r\n        ptr.addJSON(json);\r\n    return ptr;\r\n};\r\n\r\n/**\r\n * Resolves this namespace's and all its nested objects' type references. Useful to validate a reflection tree, but comes at a cost.\r\n * @returns {Namespace} `this`\r\n */\r\nNamespace.prototype.resolveAll = function resolveAll() {\r\n    var nested = this.nestedArray, i = 0;\r\n    while (i < nested.length)\r\n        if (nested[i] instanceof Namespace)\r\n            nested[i++].resolveAll();\r\n        else\r\n            nested[i++].resolve();\r\n    return this.resolve();\r\n};\r\n\r\n/**\r\n * Recursively looks up the reflection object matching the specified path in the scope of this namespace.\r\n * @param {string|string[]} path Path to look up\r\n * @param {*|Array.<*>} filterTypes Filter types, any combination of the constructors of `protobuf.Type`, `protobuf.Enum`, `protobuf.Service` etc.\r\n * @param {boolean} [parentAlreadyChecked=false] If known, whether the parent has already been checked\r\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\r\n */\r\nNamespace.prototype.lookup = function lookup(path, filterTypes, parentAlreadyChecked) {\r\n\r\n    /* istanbul ignore next */\r\n    if (typeof filterTypes === \"boolean\") {\r\n        parentAlreadyChecked = filterTypes;\r\n        filterTypes = undefined;\r\n    } else if (filterTypes && !Array.isArray(filterTypes))\r\n        filterTypes = [ filterTypes ];\r\n\r\n    if (util.isString(path) && path.length) {\r\n        if (path === \".\")\r\n            return this.root;\r\n        path = path.split(\".\");\r\n    } else if (!path.length)\r\n        return this;\r\n\r\n    // Start at root if path is absolute\r\n    if (path[0] === \"\")\r\n        return this.root.lookup(path.slice(1), filterTypes);\r\n\r\n    // Test if the first part matches any nested object, and if so, traverse if path contains more\r\n    var found = this.get(path[0]);\r\n    if (found) {\r\n        if (path.length === 1) {\r\n            if (!filterTypes || filterTypes.indexOf(found.constructor) > -1)\r\n                return found;\r\n        } else if (found instanceof Namespace && (found = found.lookup(path.slice(1), filterTypes, true)))\r\n            return found;\r\n\r\n    // Otherwise try each nested namespace\r\n    } else\r\n        for (var i = 0; i < this.nestedArray.length; ++i)\r\n            if (this._nestedArray[i] instanceof Namespace && (found = this._nestedArray[i].lookup(path, filterTypes, true)))\r\n                return found;\r\n\r\n    // If there hasn't been a match, try again at the parent\r\n    if (this.parent === null || parentAlreadyChecked)\r\n        return null;\r\n    return this.parent.lookup(path, filterTypes);\r\n};\r\n\r\n/**\r\n * Looks up the reflection object at the specified path, relative to this namespace.\r\n * @name NamespaceBase#lookup\r\n * @function\r\n * @param {string|string[]} path Path to look up\r\n * @param {boolean} [parentAlreadyChecked=false] Whether the parent has already been checked\r\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\r\n * @variation 2\r\n */\r\n// lookup(path: string, [parentAlreadyChecked: boolean])\r\n\r\n/**\r\n * Looks up the {@link Type|type} at the specified path, relative to this namespace.\r\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\r\n * @param {string|string[]} path Path to look up\r\n * @returns {Type} Looked up type\r\n * @throws {Error} If `path` does not point to a type\r\n */\r\nNamespace.prototype.lookupType = function lookupType(path) {\r\n    var found = this.lookup(path, [ Type ]);\r\n    if (!found)\r\n        throw Error(\"no such type: \" + path);\r\n    return found;\r\n};\r\n\r\n/**\r\n * Looks up the values of the {@link Enum|enum} at the specified path, relative to this namespace.\r\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\r\n * @param {string|string[]} path Path to look up\r\n * @returns {Enum} Looked up enum\r\n * @throws {Error} If `path` does not point to an enum\r\n */\r\nNamespace.prototype.lookupEnum = function lookupEnum(path) {\r\n    var found = this.lookup(path, [ Enum ]);\r\n    if (!found)\r\n        throw Error(\"no such Enum '\" + path + \"' in \" + this);\r\n    return found;\r\n};\r\n\r\n/**\r\n * Looks up the {@link Type|type} or {@link Enum|enum} at the specified path, relative to this namespace.\r\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\r\n * @param {string|string[]} path Path to look up\r\n * @returns {Type} Looked up type or enum\r\n * @throws {Error} If `path` does not point to a type or enum\r\n */\r\nNamespace.prototype.lookupTypeOrEnum = function lookupTypeOrEnum(path) {\r\n    var found = this.lookup(path, [ Type, Enum ]);\r\n    if (!found)\r\n        throw Error(\"no such Type or Enum '\" + path + \"' in \" + this);\r\n    return found;\r\n};\r\n\r\n/**\r\n * Looks up the {@link Service|service} at the specified path, relative to this namespace.\r\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\r\n * @param {string|string[]} path Path to look up\r\n * @returns {Service} Looked up service\r\n * @throws {Error} If `path` does not point to a service\r\n */\r\nNamespace.prototype.lookupService = function lookupService(path) {\r\n    var found = this.lookup(path, [ Service ]);\r\n    if (!found)\r\n        throw Error(\"no such Service '\" + path + \"' in \" + this);\r\n    return found;\r\n};\r\n\r\n// Sets up cyclic dependencies (called in index-light)\r\nNamespace._configure = function(Type_, Service_, Enum_) {\r\n    Type    = Type_;\r\n    Service = Service_;\r\n    Enum    = Enum_;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = ReflectionObject;\r\n\r\nReflectionObject.className = \"ReflectionObject\";\r\n\r\nvar util = require(37);\r\n\r\nvar Root; // cyclic\r\n\r\n/**\r\n * Constructs a new reflection object instance.\r\n * @classdesc Base class of all reflection objects.\r\n * @constructor\r\n * @param {string} name Object name\r\n * @param {Object.<string,*>} [options] Declared options\r\n * @abstract\r\n */\r\nfunction ReflectionObject(name, options) {\r\n\r\n    if (!util.isString(name))\r\n        throw TypeError(\"name must be a string\");\r\n\r\n    if (options && !util.isObject(options))\r\n        throw TypeError(\"options must be an object\");\r\n\r\n    /**\r\n     * Options.\r\n     * @type {Object.<string,*>|undefined}\r\n     */\r\n    this.options = options; // toJSON\r\n\r\n    /**\r\n     * Unique name within its namespace.\r\n     * @type {string}\r\n     */\r\n    this.name = name;\r\n\r\n    /**\r\n     * Parent namespace.\r\n     * @type {Namespace|null}\r\n     */\r\n    this.parent = null;\r\n\r\n    /**\r\n     * Whether already resolved or not.\r\n     * @type {boolean}\r\n     */\r\n    this.resolved = false;\r\n\r\n    /**\r\n     * Comment text, if any.\r\n     * @type {string|null}\r\n     */\r\n    this.comment = null;\r\n\r\n    /**\r\n     * Defining file name.\r\n     * @type {string|null}\r\n     */\r\n    this.filename = null;\r\n}\r\n\r\nObject.defineProperties(ReflectionObject.prototype, {\r\n\r\n    /**\r\n     * Reference to the root namespace.\r\n     * @name ReflectionObject#root\r\n     * @type {Root}\r\n     * @readonly\r\n     */\r\n    root: {\r\n        get: function() {\r\n            var ptr = this;\r\n            while (ptr.parent !== null)\r\n                ptr = ptr.parent;\r\n            return ptr;\r\n        }\r\n    },\r\n\r\n    /**\r\n     * Full name including leading dot.\r\n     * @name ReflectionObject#fullName\r\n     * @type {string}\r\n     * @readonly\r\n     */\r\n    fullName: {\r\n        get: function() {\r\n            var path = [ this.name ],\r\n                ptr = this.parent;\r\n            while (ptr) {\r\n                path.unshift(ptr.name);\r\n                ptr = ptr.parent;\r\n            }\r\n            return path.join(\".\");\r\n        }\r\n    }\r\n});\r\n\r\n/**\r\n * Converts this reflection object to its descriptor representation.\r\n * @returns {Object.<string,*>} Descriptor\r\n * @abstract\r\n */\r\nReflectionObject.prototype.toJSON = /* istanbul ignore next */ function toJSON() {\r\n    throw Error(); // not implemented, shouldn't happen\r\n};\r\n\r\n/**\r\n * Called when this object is added to a parent.\r\n * @param {ReflectionObject} parent Parent added to\r\n * @returns {undefined}\r\n */\r\nReflectionObject.prototype.onAdd = function onAdd(parent) {\r\n    if (this.parent && this.parent !== parent)\r\n        this.parent.remove(this);\r\n    this.parent = parent;\r\n    this.resolved = false;\r\n    var root = parent.root;\r\n    if (root instanceof Root)\r\n        root._handleAdd(this);\r\n};\r\n\r\n/**\r\n * Called when this object is removed from a parent.\r\n * @param {ReflectionObject} parent Parent removed from\r\n * @returns {undefined}\r\n */\r\nReflectionObject.prototype.onRemove = function onRemove(parent) {\r\n    var root = parent.root;\r\n    if (root instanceof Root)\r\n        root._handleRemove(this);\r\n    this.parent = null;\r\n    this.resolved = false;\r\n};\r\n\r\n/**\r\n * Resolves this objects type references.\r\n * @returns {ReflectionObject} `this`\r\n */\r\nReflectionObject.prototype.resolve = function resolve() {\r\n    if (this.resolved)\r\n        return this;\r\n    if (this.root instanceof Root)\r\n        this.resolved = true; // only if part of a root\r\n    return this;\r\n};\r\n\r\n/**\r\n * Gets an option value.\r\n * @param {string} name Option name\r\n * @returns {*} Option value or `undefined` if not set\r\n */\r\nReflectionObject.prototype.getOption = function getOption(name) {\r\n    if (this.options)\r\n        return this.options[name];\r\n    return undefined;\r\n};\r\n\r\n/**\r\n * Sets an option.\r\n * @param {string} name Option name\r\n * @param {*} value Option value\r\n * @param {boolean} [ifNotSet] Sets the option only if it isn't currently set\r\n * @returns {ReflectionObject} `this`\r\n */\r\nReflectionObject.prototype.setOption = function setOption(name, value, ifNotSet) {\r\n    if (!ifNotSet || !this.options || this.options[name] === undefined)\r\n        (this.options || (this.options = {}))[name] = value;\r\n    return this;\r\n};\r\n\r\n/**\r\n * Sets multiple options.\r\n * @param {Object.<string,*>} options Options to set\r\n * @param {boolean} [ifNotSet] Sets an option only if it isn't currently set\r\n * @returns {ReflectionObject} `this`\r\n */\r\nReflectionObject.prototype.setOptions = function setOptions(options, ifNotSet) {\r\n    if (options)\r\n        for (var keys = Object.keys(options), i = 0; i < keys.length; ++i)\r\n            this.setOption(keys[i], options[keys[i]], ifNotSet);\r\n    return this;\r\n};\r\n\r\n/**\r\n * Converts this instance to its string representation.\r\n * @returns {string} Class name[, space, full name]\r\n */\r\nReflectionObject.prototype.toString = function toString() {\r\n    var className = this.constructor.className,\r\n        fullName  = this.fullName;\r\n    if (fullName.length)\r\n        return className + \" \" + fullName;\r\n    return className;\r\n};\r\n\r\n// Sets up cyclic dependencies (called in index-light)\r\nReflectionObject._configure = function(Root_) {\r\n    Root = Root_;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = OneOf;\r\n\r\n// extends ReflectionObject\r\nvar ReflectionObject = require(24);\r\n((OneOf.prototype = Object.create(ReflectionObject.prototype)).constructor = OneOf).className = \"OneOf\";\r\n\r\nvar Field = require(16),\r\n    util  = require(37);\r\n\r\n/**\r\n * Constructs a new oneof instance.\r\n * @classdesc Reflected oneof.\r\n * @extends ReflectionObject\r\n * @constructor\r\n * @param {string} name Oneof name\r\n * @param {string[]|Object.<string,*>} [fieldNames] Field names\r\n * @param {Object.<string,*>} [options] Declared options\r\n * @param {string} [comment] Comment associated with this field\r\n */\r\nfunction OneOf(name, fieldNames, options, comment) {\r\n    if (!Array.isArray(fieldNames)) {\r\n        options = fieldNames;\r\n        fieldNames = undefined;\r\n    }\r\n    ReflectionObject.call(this, name, options);\r\n\r\n    /* istanbul ignore if */\r\n    if (!(fieldNames === undefined || Array.isArray(fieldNames)))\r\n        throw TypeError(\"fieldNames must be an Array\");\r\n\r\n    /**\r\n     * Field names that belong to this oneof.\r\n     * @type {string[]}\r\n     */\r\n    this.oneof = fieldNames || []; // toJSON, marker\r\n\r\n    /**\r\n     * Fields that belong to this oneof as an array for iteration.\r\n     * @type {Field[]}\r\n     * @readonly\r\n     */\r\n    this.fieldsArray = []; // declared readonly for conformance, possibly not yet added to parent\r\n\r\n    /**\r\n     * Comment for this field.\r\n     * @type {string|null}\r\n     */\r\n    this.comment = comment;\r\n}\r\n\r\n/**\r\n * Oneof descriptor.\r\n * @interface IOneOf\r\n * @property {Array.<string>} oneof Oneof field names\r\n * @property {Object.<string,*>} [options] Oneof options\r\n */\r\n\r\n/**\r\n * Constructs a oneof from a oneof descriptor.\r\n * @param {string} name Oneof name\r\n * @param {IOneOf} json Oneof descriptor\r\n * @returns {OneOf} Created oneof\r\n * @throws {TypeError} If arguments are invalid\r\n */\r\nOneOf.fromJSON = function fromJSON(name, json) {\r\n    return new OneOf(name, json.oneof, json.options, json.comment);\r\n};\r\n\r\n/**\r\n * Converts this oneof to a oneof descriptor.\r\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\r\n * @returns {IOneOf} Oneof descriptor\r\n */\r\nOneOf.prototype.toJSON = function toJSON(toJSONOptions) {\r\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\r\n    return util.toObject([\r\n        \"options\" , this.options,\r\n        \"oneof\"   , this.oneof,\r\n        \"comment\" , keepComments ? this.comment : undefined\r\n    ]);\r\n};\r\n\r\n/**\r\n * Adds the fields of the specified oneof to the parent if not already done so.\r\n * @param {OneOf} oneof The oneof\r\n * @returns {undefined}\r\n * @inner\r\n * @ignore\r\n */\r\nfunction addFieldsToParent(oneof) {\r\n    if (oneof.parent)\r\n        for (var i = 0; i < oneof.fieldsArray.length; ++i)\r\n            if (!oneof.fieldsArray[i].parent)\r\n                oneof.parent.add(oneof.fieldsArray[i]);\r\n}\r\n\r\n/**\r\n * Adds a field to this oneof and removes it from its current parent, if any.\r\n * @param {Field} field Field to add\r\n * @returns {OneOf} `this`\r\n */\r\nOneOf.prototype.add = function add(field) {\r\n\r\n    /* istanbul ignore if */\r\n    if (!(field instanceof Field))\r\n        throw TypeError(\"field must be a Field\");\r\n\r\n    if (field.parent && field.parent !== this.parent)\r\n        field.parent.remove(field);\r\n    this.oneof.push(field.name);\r\n    this.fieldsArray.push(field);\r\n    field.partOf = this; // field.parent remains null\r\n    addFieldsToParent(this);\r\n    return this;\r\n};\r\n\r\n/**\r\n * Removes a field from this oneof and puts it back to the oneof's parent.\r\n * @param {Field} field Field to remove\r\n * @returns {OneOf} `this`\r\n */\r\nOneOf.prototype.remove = function remove(field) {\r\n\r\n    /* istanbul ignore if */\r\n    if (!(field instanceof Field))\r\n        throw TypeError(\"field must be a Field\");\r\n\r\n    var index = this.fieldsArray.indexOf(field);\r\n\r\n    /* istanbul ignore if */\r\n    if (index < 0)\r\n        throw Error(field + \" is not a member of \" + this);\r\n\r\n    this.fieldsArray.splice(index, 1);\r\n    index = this.oneof.indexOf(field.name);\r\n\r\n    /* istanbul ignore else */\r\n    if (index > -1) // theoretical\r\n        this.oneof.splice(index, 1);\r\n\r\n    field.partOf = null;\r\n    return this;\r\n};\r\n\r\n/**\r\n * @override\r\n */\r\nOneOf.prototype.onAdd = function onAdd(parent) {\r\n    ReflectionObject.prototype.onAdd.call(this, parent);\r\n    var self = this;\r\n    // Collect present fields\r\n    for (var i = 0; i < this.oneof.length; ++i) {\r\n        var field = parent.get(this.oneof[i]);\r\n        if (field && !field.partOf) {\r\n            field.partOf = self;\r\n            self.fieldsArray.push(field);\r\n        }\r\n    }\r\n    // Add not yet present fields\r\n    addFieldsToParent(this);\r\n};\r\n\r\n/**\r\n * @override\r\n */\r\nOneOf.prototype.onRemove = function onRemove(parent) {\r\n    for (var i = 0, field; i < this.fieldsArray.length; ++i)\r\n        if ((field = this.fieldsArray[i]).parent)\r\n            field.parent.remove(field);\r\n    ReflectionObject.prototype.onRemove.call(this, parent);\r\n};\r\n\r\n/**\r\n * Decorator function as returned by {@link OneOf.d} (TypeScript).\r\n * @typedef OneOfDecorator\r\n * @type {function}\r\n * @param {Object} prototype Target prototype\r\n * @param {string} oneofName OneOf name\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * OneOf decorator (TypeScript).\r\n * @function\r\n * @param {...string} fieldNames Field names\r\n * @returns {OneOfDecorator} Decorator function\r\n * @template T extends string\r\n */\r\nOneOf.d = function decorateOneOf() {\r\n    var fieldNames = new Array(arguments.length),\r\n        index = 0;\r\n    while (index < arguments.length)\r\n        fieldNames[index] = arguments[index++];\r\n    return function oneOfDecorator(prototype, oneofName) {\r\n        util.decorateType(prototype.constructor)\r\n            .add(new OneOf(oneofName, fieldNames));\r\n        Object.defineProperty(prototype, oneofName, {\r\n            get: util.oneOfGetter(fieldNames),\r\n            set: util.oneOfSetter(fieldNames)\r\n        });\r\n    };\r\n};\r\n", "\"use strict\";\r\nmodule.exports = parse;\r\n\r\nparse.filename = null;\r\nparse.defaults = { keepCase: false };\r\n\r\nvar tokenize  = require(34),\r\n    Root      = require(29),\r\n    Type      = require(35),\r\n    Field     = require(16),\r\n    MapField  = require(20),\r\n    OneOf     = require(25),\r\n    Enum      = require(15),\r\n    Service   = require(33),\r\n    Method    = require(22),\r\n    types     = require(36),\r\n    util      = require(37);\r\n\r\nvar base10Re    = /^[1-9][0-9]*$/,\r\n    base10NegRe = /^-?[1-9][0-9]*$/,\r\n    base16Re    = /^0[x][0-9a-fA-F]+$/,\r\n    base16NegRe = /^-?0[x][0-9a-fA-F]+$/,\r\n    base8Re     = /^0[0-7]+$/,\r\n    base8NegRe  = /^-?0[0-7]+$/,\r\n    numberRe    = /^(?![eE])[0-9]*(?:\\.[0-9]*)?(?:[eE][+-]?[0-9]+)?$/,\r\n    nameRe      = /^[a-zA-Z_][a-zA-Z_0-9]*$/,\r\n    typeRefRe   = /^(?:\\.?[a-zA-Z_][a-zA-Z_0-9]*)(?:\\.[a-zA-Z_][a-zA-Z_0-9]*)*$/,\r\n    fqTypeRefRe = /^(?:\\.[a-zA-Z_][a-zA-Z_0-9]*)+$/;\r\n\r\n/**\r\n * Result object returned from {@link parse}.\r\n * @interface IParserResult\r\n * @property {string|undefined} package Package name, if declared\r\n * @property {string[]|undefined} imports Imports, if any\r\n * @property {string[]|undefined} weakImports Weak imports, if any\r\n * @property {string|undefined} syntax Syntax, if specified (either `\"proto2\"` or `\"proto3\"`)\r\n * @property {Root} root Populated root instance\r\n */\r\n\r\n/**\r\n * Options modifying the behavior of {@link parse}.\r\n * @interface IParseOptions\r\n * @property {boolean} [keepCase=false] Keeps field casing instead of converting to camel case\r\n * @property {boolean} [alternateCommentMode=false] Recognize double-slash comments in addition to doc-block comments.\r\n */\r\n\r\n/**\r\n * Options modifying the behavior of JSON serialization.\r\n * @interface IToJSONOptions\r\n * @property {boolean} [keepComments=false] Serializes comments.\r\n */\r\n\r\n/**\r\n * Parses the given .proto source and returns an object with the parsed contents.\r\n * @param {string} source Source contents\r\n * @param {Root} root Root to populate\r\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\r\n * @returns {IParserResult} Parser result\r\n * @property {string} filename=null Currently processing file name for error reporting, if known\r\n * @property {IParseOptions} defaults Default {@link IParseOptions}\r\n */\r\nfunction parse(source, root, options) {\r\n    /* eslint-disable callback-return */\r\n    if (!(root instanceof Root)) {\r\n        options = root;\r\n        root = new Root();\r\n    }\r\n    if (!options)\r\n        options = parse.defaults;\r\n\r\n    var tn = tokenize(source, options.alternateCommentMode || false),\r\n        next = tn.next,\r\n        push = tn.push,\r\n        peek = tn.peek,\r\n        skip = tn.skip,\r\n        cmnt = tn.cmnt;\r\n\r\n    var head = true,\r\n        pkg,\r\n        imports,\r\n        weakImports,\r\n        syntax,\r\n        isProto3 = false;\r\n\r\n    var ptr = root;\r\n\r\n    var applyCase = options.keepCase ? function(name) { return name; } : util.camelCase;\r\n\r\n    /* istanbul ignore next */\r\n    function illegal(token, name, insideTryCatch) {\r\n        var filename = parse.filename;\r\n        if (!insideTryCatch)\r\n            parse.filename = null;\r\n        return Error(\"illegal \" + (name || \"token\") + \" '\" + token + \"' (\" + (filename ? filename + \", \" : \"\") + \"line \" + tn.line + \")\");\r\n    }\r\n\r\n    function readString() {\r\n        var values = [],\r\n            token;\r\n        do {\r\n            /* istanbul ignore if */\r\n            if ((token = next()) !== \"\\\"\" && token !== \"'\")\r\n                throw illegal(token);\r\n\r\n            values.push(next());\r\n            skip(token);\r\n            token = peek();\r\n        } while (token === \"\\\"\" || token === \"'\");\r\n        return values.join(\"\");\r\n    }\r\n\r\n    function readValue(acceptTypeRef) {\r\n        var token = next();\r\n        switch (token) {\r\n            case \"'\":\r\n            case \"\\\"\":\r\n                push(token);\r\n                return readString();\r\n            case \"true\": case \"TRUE\":\r\n                return true;\r\n            case \"false\": case \"FALSE\":\r\n                return false;\r\n        }\r\n        try {\r\n            return parseNumber(token, /* insideTryCatch */ true);\r\n        } catch (e) {\r\n\r\n            /* istanbul ignore else */\r\n            if (acceptTypeRef && typeRefRe.test(token))\r\n                return token;\r\n\r\n            /* istanbul ignore next */\r\n            throw illegal(token, \"value\");\r\n        }\r\n    }\r\n\r\n    function readRanges(target, acceptStrings) {\r\n        var token, start;\r\n        do {\r\n            if (acceptStrings && ((token = peek()) === \"\\\"\" || token === \"'\"))\r\n                target.push(readString());\r\n            else\r\n                target.push([ start = parseId(next()), skip(\"to\", true) ? parseId(next()) : start ]);\r\n        } while (skip(\",\", true));\r\n        skip(\";\");\r\n    }\r\n\r\n    function parseNumber(token, insideTryCatch) {\r\n        var sign = 1;\r\n        if (token.charAt(0) === \"-\") {\r\n            sign = -1;\r\n            token = token.substring(1);\r\n        }\r\n        switch (token) {\r\n            case \"inf\": case \"INF\": case \"Inf\":\r\n                return sign * Infinity;\r\n            case \"nan\": case \"NAN\": case \"Nan\": case \"NaN\":\r\n                return NaN;\r\n            case \"0\":\r\n                return 0;\r\n        }\r\n        if (base10Re.test(token))\r\n            return sign * parseInt(token, 10);\r\n        if (base16Re.test(token))\r\n            return sign * parseInt(token, 16);\r\n        if (base8Re.test(token))\r\n            return sign * parseInt(token, 8);\r\n\r\n        /* istanbul ignore else */\r\n        if (numberRe.test(token))\r\n            return sign * parseFloat(token);\r\n\r\n        /* istanbul ignore next */\r\n        throw illegal(token, \"number\", insideTryCatch);\r\n    }\r\n\r\n    function parseId(token, acceptNegative) {\r\n        switch (token) {\r\n            case \"max\": case \"MAX\": case \"Max\":\r\n                return 536870911;\r\n            case \"0\":\r\n                return 0;\r\n        }\r\n\r\n        /* istanbul ignore if */\r\n        if (!acceptNegative && token.charAt(0) === \"-\")\r\n            throw illegal(token, \"id\");\r\n\r\n        if (base10NegRe.test(token))\r\n            return parseInt(token, 10);\r\n        if (base16NegRe.test(token))\r\n            return parseInt(token, 16);\r\n\r\n        /* istanbul ignore else */\r\n        if (base8NegRe.test(token))\r\n            return parseInt(token, 8);\r\n\r\n        /* istanbul ignore next */\r\n        throw illegal(token, \"id\");\r\n    }\r\n\r\n    function parsePackage() {\r\n\r\n        /* istanbul ignore if */\r\n        if (pkg !== undefined)\r\n            throw illegal(\"package\");\r\n\r\n        pkg = next();\r\n\r\n        /* istanbul ignore if */\r\n        if (!typeRefRe.test(pkg))\r\n            throw illegal(pkg, \"name\");\r\n\r\n        ptr = ptr.define(pkg);\r\n        skip(\";\");\r\n    }\r\n\r\n    function parseImport() {\r\n        var token = peek();\r\n        var whichImports;\r\n        switch (token) {\r\n            case \"weak\":\r\n                whichImports = weakImports || (weakImports = []);\r\n                next();\r\n                break;\r\n            case \"public\":\r\n                next();\r\n                // eslint-disable-line no-fallthrough\r\n            default:\r\n                whichImports = imports || (imports = []);\r\n                break;\r\n        }\r\n        token = readString();\r\n        skip(\";\");\r\n        whichImports.push(token);\r\n    }\r\n\r\n    function parseSyntax() {\r\n        skip(\"=\");\r\n        syntax = readString();\r\n        isProto3 = syntax === \"proto3\";\r\n\r\n        /* istanbul ignore if */\r\n        if (!isProto3 && syntax !== \"proto2\")\r\n            throw illegal(syntax, \"syntax\");\r\n\r\n        skip(\";\");\r\n    }\r\n\r\n    function parseCommon(parent, token) {\r\n        switch (token) {\r\n\r\n            case \"option\":\r\n                parseOption(parent, token);\r\n                skip(\";\");\r\n                return true;\r\n\r\n            case \"message\":\r\n                parseType(parent, token);\r\n                return true;\r\n\r\n            case \"enum\":\r\n                parseEnum(parent, token);\r\n                return true;\r\n\r\n            case \"service\":\r\n                parseService(parent, token);\r\n                return true;\r\n\r\n            case \"extend\":\r\n                parseExtension(parent, token);\r\n                return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    function ifBlock(obj, fnIf, fnElse) {\r\n        var trailingLine = tn.line;\r\n        if (obj) {\r\n            obj.comment = cmnt(); // try block-type comment\r\n            obj.filename = parse.filename;\r\n        }\r\n        if (skip(\"{\", true)) {\r\n            var token;\r\n            while ((token = next()) !== \"}\")\r\n                fnIf(token);\r\n            skip(\";\", true);\r\n        } else {\r\n            if (fnElse)\r\n                fnElse();\r\n            skip(\";\");\r\n            if (obj && typeof obj.comment !== \"string\")\r\n                obj.comment = cmnt(trailingLine); // try line-type comment if no block\r\n        }\r\n    }\r\n\r\n    function parseType(parent, token) {\r\n\r\n        /* istanbul ignore if */\r\n        if (!nameRe.test(token = next()))\r\n            throw illegal(token, \"type name\");\r\n\r\n        var type = new Type(token);\r\n        ifBlock(type, function parseType_block(token) {\r\n            if (parseCommon(type, token))\r\n                return;\r\n\r\n            switch (token) {\r\n\r\n                case \"map\":\r\n                    parseMapField(type, token);\r\n                    break;\r\n\r\n                case \"required\":\r\n                case \"optional\":\r\n                case \"repeated\":\r\n                    parseField(type, token);\r\n                    break;\r\n\r\n                case \"oneof\":\r\n                    parseOneOf(type, token);\r\n                    break;\r\n\r\n                case \"extensions\":\r\n                    readRanges(type.extensions || (type.extensions = []));\r\n                    break;\r\n\r\n                case \"reserved\":\r\n                    readRanges(type.reserved || (type.reserved = []), true);\r\n                    break;\r\n\r\n                default:\r\n                    /* istanbul ignore if */\r\n                    if (!isProto3 || !typeRefRe.test(token))\r\n                        throw illegal(token);\r\n\r\n                    push(token);\r\n                    parseField(type, \"optional\");\r\n                    break;\r\n            }\r\n        });\r\n        parent.add(type);\r\n    }\r\n\r\n    function parseField(parent, rule, extend) {\r\n        var type = next();\r\n        if (type === \"group\") {\r\n            parseGroup(parent, rule);\r\n            return;\r\n        }\r\n\r\n        /* istanbul ignore if */\r\n        if (!typeRefRe.test(type))\r\n            throw illegal(type, \"type\");\r\n\r\n        var name = next();\r\n\r\n        /* istanbul ignore if */\r\n        if (!nameRe.test(name))\r\n            throw illegal(name, \"name\");\r\n\r\n        name = applyCase(name);\r\n        skip(\"=\");\r\n\r\n        var field = new Field(name, parseId(next()), type, rule, extend);\r\n        ifBlock(field, function parseField_block(token) {\r\n\r\n            /* istanbul ignore else */\r\n            if (token === \"option\") {\r\n                parseOption(field, token);\r\n                skip(\";\");\r\n            } else\r\n                throw illegal(token);\r\n\r\n        }, function parseField_line() {\r\n            parseInlineOptions(field);\r\n        });\r\n        parent.add(field);\r\n\r\n        // JSON defaults to packed=true if not set so we have to set packed=false explicity when\r\n        // parsing proto2 descriptors without the option, where applicable. This must be done for\r\n        // all known packable types and anything that could be an enum (= is not a basic type).\r\n        if (!isProto3 && field.repeated && (types.packed[type] !== undefined || types.basic[type] === undefined))\r\n            field.setOption(\"packed\", false, /* ifNotSet */ true);\r\n    }\r\n\r\n    function parseGroup(parent, rule) {\r\n        var name = next();\r\n\r\n        /* istanbul ignore if */\r\n        if (!nameRe.test(name))\r\n            throw illegal(name, \"name\");\r\n\r\n        var fieldName = util.lcFirst(name);\r\n        if (name === fieldName)\r\n            name = util.ucFirst(name);\r\n        skip(\"=\");\r\n        var id = parseId(next());\r\n        var type = new Type(name);\r\n        type.group = true;\r\n        var field = new Field(fieldName, id, name, rule);\r\n        field.filename = parse.filename;\r\n        ifBlock(type, function parseGroup_block(token) {\r\n            switch (token) {\r\n\r\n                case \"option\":\r\n                    parseOption(type, token);\r\n                    skip(\";\");\r\n                    break;\r\n\r\n                case \"required\":\r\n                case \"optional\":\r\n                case \"repeated\":\r\n                    parseField(type, token);\r\n                    break;\r\n\r\n                /* istanbul ignore next */\r\n                default:\r\n                    throw illegal(token); // there are no groups with proto3 semantics\r\n            }\r\n        });\r\n        parent.add(type)\r\n              .add(field);\r\n    }\r\n\r\n    function parseMapField(parent) {\r\n        skip(\"<\");\r\n        var keyType = next();\r\n\r\n        /* istanbul ignore if */\r\n        if (types.mapKey[keyType] === undefined)\r\n            throw illegal(keyType, \"type\");\r\n\r\n        skip(\",\");\r\n        var valueType = next();\r\n\r\n        /* istanbul ignore if */\r\n        if (!typeRefRe.test(valueType))\r\n            throw illegal(valueType, \"type\");\r\n\r\n        skip(\">\");\r\n        var name = next();\r\n\r\n        /* istanbul ignore if */\r\n        if (!nameRe.test(name))\r\n            throw illegal(name, \"name\");\r\n\r\n        skip(\"=\");\r\n        var field = new MapField(applyCase(name), parseId(next()), keyType, valueType);\r\n        ifBlock(field, function parseMapField_block(token) {\r\n\r\n            /* istanbul ignore else */\r\n            if (token === \"option\") {\r\n                parseOption(field, token);\r\n                skip(\";\");\r\n            } else\r\n                throw illegal(token);\r\n\r\n        }, function parseMapField_line() {\r\n            parseInlineOptions(field);\r\n        });\r\n        parent.add(field);\r\n    }\r\n\r\n    function parseOneOf(parent, token) {\r\n\r\n        /* istanbul ignore if */\r\n        if (!nameRe.test(token = next()))\r\n            throw illegal(token, \"name\");\r\n\r\n        var oneof = new OneOf(applyCase(token));\r\n        ifBlock(oneof, function parseOneOf_block(token) {\r\n            if (token === \"option\") {\r\n                parseOption(oneof, token);\r\n                skip(\";\");\r\n            } else {\r\n                push(token);\r\n                parseField(oneof, \"optional\");\r\n            }\r\n        });\r\n        parent.add(oneof);\r\n    }\r\n\r\n    function parseEnum(parent, token) {\r\n\r\n        /* istanbul ignore if */\r\n        if (!nameRe.test(token = next()))\r\n            throw illegal(token, \"name\");\r\n\r\n        var enm = new Enum(token);\r\n        ifBlock(enm, function parseEnum_block(token) {\r\n          switch(token) {\r\n            case \"option\":\r\n              parseOption(enm, token);\r\n              skip(\";\");\r\n              break;\r\n\r\n            case \"reserved\":\r\n              readRanges(enm.reserved || (enm.reserved = []), true);\r\n              break;\r\n\r\n            default:\r\n              parseEnumValue(enm, token);\r\n          }\r\n        });\r\n        parent.add(enm);\r\n    }\r\n\r\n    function parseEnumValue(parent, token) {\r\n\r\n        /* istanbul ignore if */\r\n        if (!nameRe.test(token))\r\n            throw illegal(token, \"name\");\r\n\r\n        skip(\"=\");\r\n        var value = parseId(next(), true),\r\n            dummy = {};\r\n        ifBlock(dummy, function parseEnumValue_block(token) {\r\n\r\n            /* istanbul ignore else */\r\n            if (token === \"option\") {\r\n                parseOption(dummy, token); // skip\r\n                skip(\";\");\r\n            } else\r\n                throw illegal(token);\r\n\r\n        }, function parseEnumValue_line() {\r\n            parseInlineOptions(dummy); // skip\r\n        });\r\n        parent.add(token, value, dummy.comment);\r\n    }\r\n\r\n    function parseOption(parent, token) {\r\n        var isCustom = skip(\"(\", true);\r\n\r\n        /* istanbul ignore if */\r\n        if (!typeRefRe.test(token = next()))\r\n            throw illegal(token, \"name\");\r\n\r\n        var name = token;\r\n        if (isCustom) {\r\n            skip(\")\");\r\n            name = \"(\" + name + \")\";\r\n            token = peek();\r\n            if (fqTypeRefRe.test(token)) {\r\n                name += token;\r\n                next();\r\n            }\r\n        }\r\n        skip(\"=\");\r\n        parseOptionValue(parent, name);\r\n    }\r\n\r\n    function parseOptionValue(parent, name) {\r\n        if (skip(\"{\", true)) { // { a: \"foo\" b { c: \"bar\" } }\r\n            do {\r\n                /* istanbul ignore if */\r\n                if (!nameRe.test(token = next()))\r\n                    throw illegal(token, \"name\");\r\n\r\n                if (peek() === \"{\")\r\n                    parseOptionValue(parent, name + \".\" + token);\r\n                else {\r\n                    skip(\":\");\r\n                    if (peek() === \"{\")\r\n                        parseOptionValue(parent, name + \".\" + token);\r\n                    else\r\n                        setOption(parent, name + \".\" + token, readValue(true));\r\n                }\r\n                skip(\",\", true);\r\n            } while (!skip(\"}\", true));\r\n        } else\r\n            setOption(parent, name, readValue(true));\r\n        // Does not enforce a delimiter to be universal\r\n    }\r\n\r\n    function setOption(parent, name, value) {\r\n        if (parent.setOption)\r\n            parent.setOption(name, value);\r\n    }\r\n\r\n    function parseInlineOptions(parent) {\r\n        if (skip(\"[\", true)) {\r\n            do {\r\n                parseOption(parent, \"option\");\r\n            } while (skip(\",\", true));\r\n            skip(\"]\");\r\n        }\r\n        return parent;\r\n    }\r\n\r\n    function parseService(parent, token) {\r\n\r\n        /* istanbul ignore if */\r\n        if (!nameRe.test(token = next()))\r\n            throw illegal(token, \"service name\");\r\n\r\n        var service = new Service(token);\r\n        ifBlock(service, function parseService_block(token) {\r\n            if (parseCommon(service, token))\r\n                return;\r\n\r\n            /* istanbul ignore else */\r\n            if (token === \"rpc\")\r\n                parseMethod(service, token);\r\n            else\r\n                throw illegal(token);\r\n        });\r\n        parent.add(service);\r\n    }\r\n\r\n    function parseMethod(parent, token) {\r\n        var type = token;\r\n\r\n        /* istanbul ignore if */\r\n        if (!nameRe.test(token = next()))\r\n            throw illegal(token, \"name\");\r\n\r\n        var name = token,\r\n            requestType, requestStream,\r\n            responseType, responseStream;\r\n\r\n        skip(\"(\");\r\n        if (skip(\"stream\", true))\r\n            requestStream = true;\r\n\r\n        /* istanbul ignore if */\r\n        if (!typeRefRe.test(token = next()))\r\n            throw illegal(token);\r\n\r\n        requestType = token;\r\n        skip(\")\"); skip(\"returns\"); skip(\"(\");\r\n        if (skip(\"stream\", true))\r\n            responseStream = true;\r\n\r\n        /* istanbul ignore if */\r\n        if (!typeRefRe.test(token = next()))\r\n            throw illegal(token);\r\n\r\n        responseType = token;\r\n        skip(\")\");\r\n\r\n        var method = new Method(name, type, requestType, responseType, requestStream, responseStream);\r\n        ifBlock(method, function parseMethod_block(token) {\r\n\r\n            /* istanbul ignore else */\r\n            if (token === \"option\") {\r\n                parseOption(method, token);\r\n                skip(\";\");\r\n            } else\r\n                throw illegal(token);\r\n\r\n        });\r\n        parent.add(method);\r\n    }\r\n\r\n    function parseExtension(parent, token) {\r\n\r\n        /* istanbul ignore if */\r\n        if (!typeRefRe.test(token = next()))\r\n            throw illegal(token, \"reference\");\r\n\r\n        var reference = token;\r\n        ifBlock(null, function parseExtension_block(token) {\r\n            switch (token) {\r\n\r\n                case \"required\":\r\n                case \"repeated\":\r\n                case \"optional\":\r\n                    parseField(parent, token, reference);\r\n                    break;\r\n\r\n                default:\r\n                    /* istanbul ignore if */\r\n                    if (!isProto3 || !typeRefRe.test(token))\r\n                        throw illegal(token);\r\n                    push(token);\r\n                    parseField(parent, \"optional\", reference);\r\n                    break;\r\n            }\r\n        });\r\n    }\r\n\r\n    var token;\r\n    while ((token = next()) !== null) {\r\n        switch (token) {\r\n\r\n            case \"package\":\r\n\r\n                /* istanbul ignore if */\r\n                if (!head)\r\n                    throw illegal(token);\r\n\r\n                parsePackage();\r\n                break;\r\n\r\n            case \"import\":\r\n\r\n                /* istanbul ignore if */\r\n                if (!head)\r\n                    throw illegal(token);\r\n\r\n                parseImport();\r\n                break;\r\n\r\n            case \"syntax\":\r\n\r\n                /* istanbul ignore if */\r\n                if (!head)\r\n                    throw illegal(token);\r\n\r\n                parseSyntax();\r\n                break;\r\n\r\n            case \"option\":\r\n\r\n                /* istanbul ignore if */\r\n                if (!head)\r\n                    throw illegal(token);\r\n\r\n                parseOption(ptr, token);\r\n                skip(\";\");\r\n                break;\r\n\r\n            default:\r\n\r\n                /* istanbul ignore else */\r\n                if (parseCommon(ptr, token)) {\r\n                    head = false;\r\n                    continue;\r\n                }\r\n\r\n                /* istanbul ignore next */\r\n                throw illegal(token);\r\n        }\r\n    }\r\n\r\n    parse.filename = null;\r\n    return {\r\n        \"package\"     : pkg,\r\n        \"imports\"     : imports,\r\n         weakImports  : weakImports,\r\n         syntax       : syntax,\r\n         root         : root\r\n    };\r\n}\r\n\r\n/**\r\n * Parses the given .proto source and returns an object with the parsed contents.\r\n * @name parse\r\n * @function\r\n * @param {string} source Source contents\r\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\r\n * @returns {IParserResult} Parser result\r\n * @property {string} filename=null Currently processing file name for error reporting, if known\r\n * @property {IParseOptions} defaults Default {@link IParseOptions}\r\n * @variation 2\r\n */\r\n", "\"use strict\";\r\nmodule.exports = Reader;\r\n\r\nvar util      = require(39);\r\n\r\nvar BufferReader; // cyclic\r\n\r\nvar LongBits  = util.LongBits,\r\n    utf8      = util.utf8;\r\n\r\n/* istanbul ignore next */\r\nfunction indexOutOfRange(reader, writeLength) {\r\n    return RangeError(\"index out of range: \" + reader.pos + \" + \" + (writeLength || 1) + \" > \" + reader.len);\r\n}\r\n\r\n/**\r\n * Constructs a new reader instance using the specified buffer.\r\n * @classdesc Wire format reader using `Uint8Array` if available, otherwise `Array`.\r\n * @constructor\r\n * @param {Uint8Array} buffer Buffer to read from\r\n */\r\nfunction Reader(buffer) {\r\n\r\n    /**\r\n     * Read buffer.\r\n     * @type {Uint8Array}\r\n     */\r\n    this.buf = buffer;\r\n\r\n    /**\r\n     * Read buffer position.\r\n     * @type {number}\r\n     */\r\n    this.pos = 0;\r\n\r\n    /**\r\n     * Read buffer length.\r\n     * @type {number}\r\n     */\r\n    this.len = buffer.length;\r\n}\r\n\r\nvar create_array = typeof Uint8Array !== \"undefined\"\r\n    ? function create_typed_array(buffer) {\r\n        if (buffer instanceof Uint8Array || Array.isArray(buffer))\r\n            return new Reader(buffer);\r\n        throw Error(\"illegal buffer\");\r\n    }\r\n    /* istanbul ignore next */\r\n    : function create_array(buffer) {\r\n        if (Array.isArray(buffer))\r\n            return new Reader(buffer);\r\n        throw Error(\"illegal buffer\");\r\n    };\r\n\r\n/**\r\n * Creates a new reader using the specified buffer.\r\n * @function\r\n * @param {Uint8Array|Buffer} buffer Buffer to read from\r\n * @returns {Reader|BufferReader} A {@link BufferReader} if `buffer` is a Buffer, otherwise a {@link Reader}\r\n * @throws {Error} If `buffer` is not a valid buffer\r\n */\r\nReader.create = util.Buffer\r\n    ? function create_buffer_setup(buffer) {\r\n        return (Reader.create = function create_buffer(buffer) {\r\n            return util.Buffer.isBuffer(buffer)\r\n                ? new BufferReader(buffer)\r\n                /* istanbul ignore next */\r\n                : create_array(buffer);\r\n        })(buffer);\r\n    }\r\n    /* istanbul ignore next */\r\n    : create_array;\r\n\r\nReader.prototype._slice = util.Array.prototype.subarray || /* istanbul ignore next */ util.Array.prototype.slice;\r\n\r\n/**\r\n * Reads a varint as an unsigned 32 bit value.\r\n * @function\r\n * @returns {number} Value read\r\n */\r\nReader.prototype.uint32 = (function read_uint32_setup() {\r\n    var value = 4294967295; // optimizer type-hint, tends to deopt otherwise (?!)\r\n    return function read_uint32() {\r\n        value = (         this.buf[this.pos] & 127       ) >>> 0; if (this.buf[this.pos++] < 128) return value;\r\n        value = (value | (this.buf[this.pos] & 127) <<  7) >>> 0; if (this.buf[this.pos++] < 128) return value;\r\n        value = (value | (this.buf[this.pos] & 127) << 14) >>> 0; if (this.buf[this.pos++] < 128) return value;\r\n        value = (value | (this.buf[this.pos] & 127) << 21) >>> 0; if (this.buf[this.pos++] < 128) return value;\r\n        value = (value | (this.buf[this.pos] &  15) << 28) >>> 0; if (this.buf[this.pos++] < 128) return value;\r\n\r\n        /* istanbul ignore if */\r\n        if ((this.pos += 5) > this.len) {\r\n            this.pos = this.len;\r\n            throw indexOutOfRange(this, 10);\r\n        }\r\n        return value;\r\n    };\r\n})();\r\n\r\n/**\r\n * Reads a varint as a signed 32 bit value.\r\n * @returns {number} Value read\r\n */\r\nReader.prototype.int32 = function read_int32() {\r\n    return this.uint32() | 0;\r\n};\r\n\r\n/**\r\n * Reads a zig-zag encoded varint as a signed 32 bit value.\r\n * @returns {number} Value read\r\n */\r\nReader.prototype.sint32 = function read_sint32() {\r\n    var value = this.uint32();\r\n    return value >>> 1 ^ -(value & 1) | 0;\r\n};\r\n\r\n/* eslint-disable no-invalid-this */\r\n\r\nfunction readLongVarint() {\r\n    // tends to deopt with local vars for octet etc.\r\n    var bits = new LongBits(0, 0);\r\n    var i = 0;\r\n    if (this.len - this.pos > 4) { // fast route (lo)\r\n        for (; i < 4; ++i) {\r\n            // 1st..4th\r\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\r\n            if (this.buf[this.pos++] < 128)\r\n                return bits;\r\n        }\r\n        // 5th\r\n        bits.lo = (bits.lo | (this.buf[this.pos] & 127) << 28) >>> 0;\r\n        bits.hi = (bits.hi | (this.buf[this.pos] & 127) >>  4) >>> 0;\r\n        if (this.buf[this.pos++] < 128)\r\n            return bits;\r\n        i = 0;\r\n    } else {\r\n        for (; i < 3; ++i) {\r\n            /* istanbul ignore if */\r\n            if (this.pos >= this.len)\r\n                throw indexOutOfRange(this);\r\n            // 1st..3th\r\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\r\n            if (this.buf[this.pos++] < 128)\r\n                return bits;\r\n        }\r\n        // 4th\r\n        bits.lo = (bits.lo | (this.buf[this.pos++] & 127) << i * 7) >>> 0;\r\n        return bits;\r\n    }\r\n    if (this.len - this.pos > 4) { // fast route (hi)\r\n        for (; i < 5; ++i) {\r\n            // 6th..10th\r\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\r\n            if (this.buf[this.pos++] < 128)\r\n                return bits;\r\n        }\r\n    } else {\r\n        for (; i < 5; ++i) {\r\n            /* istanbul ignore if */\r\n            if (this.pos >= this.len)\r\n                throw indexOutOfRange(this);\r\n            // 6th..10th\r\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\r\n            if (this.buf[this.pos++] < 128)\r\n                return bits;\r\n        }\r\n    }\r\n    /* istanbul ignore next */\r\n    throw Error(\"invalid varint encoding\");\r\n}\r\n\r\n/* eslint-enable no-invalid-this */\r\n\r\n/**\r\n * Reads a varint as a signed 64 bit value.\r\n * @name Reader#int64\r\n * @function\r\n * @returns {Long} Value read\r\n */\r\n\r\n/**\r\n * Reads a varint as an unsigned 64 bit value.\r\n * @name Reader#uint64\r\n * @function\r\n * @returns {Long} Value read\r\n */\r\n\r\n/**\r\n * Reads a zig-zag encoded varint as a signed 64 bit value.\r\n * @name Reader#sint64\r\n * @function\r\n * @returns {Long} Value read\r\n */\r\n\r\n/**\r\n * Reads a varint as a boolean.\r\n * @returns {boolean} Value read\r\n */\r\nReader.prototype.bool = function read_bool() {\r\n    return this.uint32() !== 0;\r\n};\r\n\r\nfunction readFixed32_end(buf, end) { // note that this uses `end`, not `pos`\r\n    return (buf[end - 4]\r\n          | buf[end - 3] << 8\r\n          | buf[end - 2] << 16\r\n          | buf[end - 1] << 24) >>> 0;\r\n}\r\n\r\n/**\r\n * Reads fixed 32 bits as an unsigned 32 bit integer.\r\n * @returns {number} Value read\r\n */\r\nReader.prototype.fixed32 = function read_fixed32() {\r\n\r\n    /* istanbul ignore if */\r\n    if (this.pos + 4 > this.len)\r\n        throw indexOutOfRange(this, 4);\r\n\r\n    return readFixed32_end(this.buf, this.pos += 4);\r\n};\r\n\r\n/**\r\n * Reads fixed 32 bits as a signed 32 bit integer.\r\n * @returns {number} Value read\r\n */\r\nReader.prototype.sfixed32 = function read_sfixed32() {\r\n\r\n    /* istanbul ignore if */\r\n    if (this.pos + 4 > this.len)\r\n        throw indexOutOfRange(this, 4);\r\n\r\n    return readFixed32_end(this.buf, this.pos += 4) | 0;\r\n};\r\n\r\n/* eslint-disable no-invalid-this */\r\n\r\nfunction readFixed64(/* this: Reader */) {\r\n\r\n    /* istanbul ignore if */\r\n    if (this.pos + 8 > this.len)\r\n        throw indexOutOfRange(this, 8);\r\n\r\n    return new LongBits(readFixed32_end(this.buf, this.pos += 4), readFixed32_end(this.buf, this.pos += 4));\r\n}\r\n\r\n/* eslint-enable no-invalid-this */\r\n\r\n/**\r\n * Reads fixed 64 bits.\r\n * @name Reader#fixed64\r\n * @function\r\n * @returns {Long} Value read\r\n */\r\n\r\n/**\r\n * Reads zig-zag encoded fixed 64 bits.\r\n * @name Reader#sfixed64\r\n * @function\r\n * @returns {Long} Value read\r\n */\r\n\r\n/**\r\n * Reads a float (32 bit) as a number.\r\n * @function\r\n * @returns {number} Value read\r\n */\r\nReader.prototype.float = function read_float() {\r\n\r\n    /* istanbul ignore if */\r\n    if (this.pos + 4 > this.len)\r\n        throw indexOutOfRange(this, 4);\r\n\r\n    var value = util.float.readFloatLE(this.buf, this.pos);\r\n    this.pos += 4;\r\n    return value;\r\n};\r\n\r\n/**\r\n * Reads a double (64 bit float) as a number.\r\n * @function\r\n * @returns {number} Value read\r\n */\r\nReader.prototype.double = function read_double() {\r\n\r\n    /* istanbul ignore if */\r\n    if (this.pos + 8 > this.len)\r\n        throw indexOutOfRange(this, 4);\r\n\r\n    var value = util.float.readDoubleLE(this.buf, this.pos);\r\n    this.pos += 8;\r\n    return value;\r\n};\r\n\r\n/**\r\n * Reads a sequence of bytes preceeded by its length as a varint.\r\n * @returns {Uint8Array} Value read\r\n */\r\nReader.prototype.bytes = function read_bytes() {\r\n    var length = this.uint32(),\r\n        start  = this.pos,\r\n        end    = this.pos + length;\r\n\r\n    /* istanbul ignore if */\r\n    if (end > this.len)\r\n        throw indexOutOfRange(this, length);\r\n\r\n    this.pos += length;\r\n    if (Array.isArray(this.buf)) // plain array\r\n        return this.buf.slice(start, end);\r\n    return start === end // fix for IE 10/Win8 and others' subarray returning array of size 1\r\n        ? new this.buf.constructor(0)\r\n        : this._slice.call(this.buf, start, end);\r\n};\r\n\r\n/**\r\n * Reads a string preceeded by its byte length as a varint.\r\n * @returns {string} Value read\r\n */\r\nReader.prototype.string = function read_string() {\r\n    var bytes = this.bytes();\r\n    return utf8.read(bytes, 0, bytes.length);\r\n};\r\n\r\n/**\r\n * Skips the specified number of bytes if specified, otherwise skips a varint.\r\n * @param {number} [length] Length if known, otherwise a varint is assumed\r\n * @returns {Reader} `this`\r\n */\r\nReader.prototype.skip = function skip(length) {\r\n    if (typeof length === \"number\") {\r\n        /* istanbul ignore if */\r\n        if (this.pos + length > this.len)\r\n            throw indexOutOfRange(this, length);\r\n        this.pos += length;\r\n    } else {\r\n        do {\r\n            /* istanbul ignore if */\r\n            if (this.pos >= this.len)\r\n                throw indexOutOfRange(this);\r\n        } while (this.buf[this.pos++] & 128);\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Skips the next element of the specified wire type.\r\n * @param {number} wireType Wire type received\r\n * @returns {Reader} `this`\r\n */\r\nReader.prototype.skipType = function(wireType) {\r\n    switch (wireType) {\r\n        case 0:\r\n            this.skip();\r\n            break;\r\n        case 1:\r\n            this.skip(8);\r\n            break;\r\n        case 2:\r\n            this.skip(this.uint32());\r\n            break;\r\n        case 3:\r\n            while ((wireType = this.uint32() & 7) !== 4) {\r\n                this.skipType(wireType);\r\n            }\r\n            break;\r\n        case 5:\r\n            this.skip(4);\r\n            break;\r\n\r\n        /* istanbul ignore next */\r\n        default:\r\n            throw Error(\"invalid wire type \" + wireType + \" at offset \" + this.pos);\r\n    }\r\n    return this;\r\n};\r\n\r\nReader._configure = function(BufferReader_) {\r\n    BufferReader = BufferReader_;\r\n\r\n    var fn = util.Long ? \"toLong\" : /* istanbul ignore next */ \"toNumber\";\r\n    util.merge(Reader.prototype, {\r\n\r\n        int64: function read_int64() {\r\n            return readLongVarint.call(this)[fn](false);\r\n        },\r\n\r\n        uint64: function read_uint64() {\r\n            return readLongVarint.call(this)[fn](true);\r\n        },\r\n\r\n        sint64: function read_sint64() {\r\n            return readLongVarint.call(this).zzDecode()[fn](false);\r\n        },\r\n\r\n        fixed64: function read_fixed64() {\r\n            return readFixed64.call(this)[fn](true);\r\n        },\r\n\r\n        sfixed64: function read_sfixed64() {\r\n            return readFixed64.call(this)[fn](false);\r\n        }\r\n\r\n    });\r\n};\r\n", "\"use strict\";\r\nmodule.exports = <PERSON><PERSON>erReader;\r\n\r\n// extends Reader\r\nvar Reader = require(27);\r\n(BufferReader.prototype = Object.create(Reader.prototype)).constructor = BufferReader;\r\n\r\nvar util = require(39);\r\n\r\n/**\r\n * Constructs a new buffer reader instance.\r\n * @classdesc Wire format reader using node buffers.\r\n * @extends Reader\r\n * @constructor\r\n * @param {Buffer} buffer Buffer to read from\r\n */\r\nfunction BufferReader(buffer) {\r\n    Reader.call(this, buffer);\r\n\r\n    /**\r\n     * Read buffer.\r\n     * @name BufferReader#buf\r\n     * @type {Buffer}\r\n     */\r\n}\r\n\r\n/* istanbul ignore else */\r\nif (util.Buffer)\r\n    BufferReader.prototype._slice = util.Buffer.prototype.slice;\r\n\r\n/**\r\n * @override\r\n */\r\nBufferReader.prototype.string = function read_string_buffer() {\r\n    var len = this.uint32(); // modifies pos\r\n    return this.buf.utf8Slice(this.pos, this.pos = Math.min(this.pos + len, this.len));\r\n};\r\n\r\n/**\r\n * Reads a sequence of bytes preceeded by its length as a varint.\r\n * @name BufferReader#bytes\r\n * @function\r\n * @returns {<PERSON><PERSON>er} Value read\r\n */\r\n", "\"use strict\";\r\nmodule.exports = Root;\r\n\r\n// extends Namespace\r\nvar Namespace = require(23);\r\n((Root.prototype = Object.create(Namespace.prototype)).constructor = Root).className = \"Root\";\r\n\r\nvar Field   = require(16),\r\n    Enum    = require(15),\r\n    OneOf   = require(25),\r\n    util    = require(37);\r\n\r\nvar Type,   // cyclic\r\n    parse,  // might be excluded\r\n    common; // \"\r\n\r\n/**\r\n * Constructs a new root namespace instance.\r\n * @classdesc Root namespace wrapping all types, enums, services, sub-namespaces etc. that belong together.\r\n * @extends NamespaceBase\r\n * @constructor\r\n * @param {Object.<string,*>} [options] Top level options\r\n */\r\nfunction Root(options) {\r\n    Namespace.call(this, \"\", options);\r\n\r\n    /**\r\n     * Deferred extension fields.\r\n     * @type {Field[]}\r\n     */\r\n    this.deferred = [];\r\n\r\n    /**\r\n     * Resolved file names of loaded files.\r\n     * @type {string[]}\r\n     */\r\n    this.files = [];\r\n}\r\n\r\n/**\r\n * Loads a namespace descriptor into a root namespace.\r\n * @param {INamespace} json Nameespace descriptor\r\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted\r\n * @returns {Root} Root namespace\r\n */\r\nRoot.fromJSON = function fromJSON(json, root) {\r\n    if (!root)\r\n        root = new Root();\r\n    if (json.options)\r\n        root.setOptions(json.options);\r\n    return root.addJSON(json.nested);\r\n};\r\n\r\n/**\r\n * Resolves the path of an imported file, relative to the importing origin.\r\n * This method exists so you can override it with your own logic in case your imports are scattered over multiple directories.\r\n * @function\r\n * @param {string} origin The file name of the importing file\r\n * @param {string} target The file name being imported\r\n * @returns {string|null} Resolved path to `target` or `null` to skip the file\r\n */\r\nRoot.prototype.resolvePath = util.path.resolve;\r\n\r\n// A symbol-like function to safely signal synchronous loading\r\n/* istanbul ignore next */\r\nfunction SYNC() {} // eslint-disable-line no-empty-function\r\n\r\n/**\r\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and calls the callback.\r\n * @param {string|string[]} filename Names of one or multiple files to load\r\n * @param {IParseOptions} options Parse options\r\n * @param {LoadCallback} callback Callback function\r\n * @returns {undefined}\r\n */\r\nRoot.prototype.load = function load(filename, options, callback) {\r\n    if (typeof options === \"function\") {\r\n        callback = options;\r\n        options = undefined;\r\n    }\r\n    var self = this;\r\n    if (!callback)\r\n        return util.asPromise(load, self, filename, options);\r\n\r\n    var sync = callback === SYNC; // undocumented\r\n\r\n    // Finishes loading by calling the callback (exactly once)\r\n    function finish(err, root) {\r\n        /* istanbul ignore if */\r\n        if (!callback)\r\n            return;\r\n        var cb = callback;\r\n        callback = null;\r\n        if (sync)\r\n            throw err;\r\n        cb(err, root);\r\n    }\r\n\r\n    // Processes a single file\r\n    function process(filename, source) {\r\n        try {\r\n            if (util.isString(source) && source.charAt(0) === \"{\")\r\n                source = JSON.parse(source);\r\n            if (!util.isString(source))\r\n                self.setOptions(source.options).addJSON(source.nested);\r\n            else {\r\n                parse.filename = filename;\r\n                var parsed = parse(source, self, options),\r\n                    resolved,\r\n                    i = 0;\r\n                if (parsed.imports)\r\n                    for (; i < parsed.imports.length; ++i)\r\n                        if (resolved = self.resolvePath(filename, parsed.imports[i]))\r\n                            fetch(resolved);\r\n                if (parsed.weakImports)\r\n                    for (i = 0; i < parsed.weakImports.length; ++i)\r\n                        if (resolved = self.resolvePath(filename, parsed.weakImports[i]))\r\n                            fetch(resolved, true);\r\n            }\r\n        } catch (err) {\r\n            finish(err);\r\n        }\r\n        if (!sync && !queued)\r\n            finish(null, self); // only once anyway\r\n    }\r\n\r\n    // Fetches a single file\r\n    function fetch(filename, weak) {\r\n\r\n        // Strip path if this file references a bundled definition\r\n        var idx = filename.lastIndexOf(\"google/protobuf/\");\r\n        if (idx > -1) {\r\n            var altname = filename.substring(idx);\r\n            if (altname in common)\r\n                filename = altname;\r\n        }\r\n\r\n        // Skip if already loaded / attempted\r\n        if (self.files.indexOf(filename) > -1)\r\n            return;\r\n        self.files.push(filename);\r\n\r\n        // Shortcut bundled definitions\r\n        if (filename in common) {\r\n            if (sync)\r\n                process(filename, common[filename]);\r\n            else {\r\n                ++queued;\r\n                setTimeout(function() {\r\n                    --queued;\r\n                    process(filename, common[filename]);\r\n                });\r\n            }\r\n            return;\r\n        }\r\n\r\n        // Otherwise fetch from disk or network\r\n        if (sync) {\r\n            var source;\r\n            try {\r\n                source = util.fs.readFileSync(filename).toString(\"utf8\");\r\n            } catch (err) {\r\n                if (!weak)\r\n                    finish(err);\r\n                return;\r\n            }\r\n            process(filename, source);\r\n        } else {\r\n            ++queued;\r\n            util.fetch(filename, function(err, source) {\r\n                --queued;\r\n                /* istanbul ignore if */\r\n                if (!callback)\r\n                    return; // terminated meanwhile\r\n                if (err) {\r\n                    /* istanbul ignore else */\r\n                    if (!weak)\r\n                        finish(err);\r\n                    else if (!queued) // can't be covered reliably\r\n                        finish(null, self);\r\n                    return;\r\n                }\r\n                process(filename, source);\r\n            });\r\n        }\r\n    }\r\n    var queued = 0;\r\n\r\n    // Assembling the root namespace doesn't require working type\r\n    // references anymore, so we can load everything in parallel\r\n    if (util.isString(filename))\r\n        filename = [ filename ];\r\n    for (var i = 0, resolved; i < filename.length; ++i)\r\n        if (resolved = self.resolvePath(\"\", filename[i]))\r\n            fetch(resolved);\r\n\r\n    if (sync)\r\n        return self;\r\n    if (!queued)\r\n        finish(null, self);\r\n    return undefined;\r\n};\r\n// function load(filename:string, options:IParseOptions, callback:LoadCallback):undefined\r\n\r\n/**\r\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and calls the callback.\r\n * @function Root#load\r\n * @param {string|string[]} filename Names of one or multiple files to load\r\n * @param {LoadCallback} callback Callback function\r\n * @returns {undefined}\r\n * @variation 2\r\n */\r\n// function load(filename:string, callback:LoadCallback):undefined\r\n\r\n/**\r\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and returns a promise.\r\n * @function Root#load\r\n * @param {string|string[]} filename Names of one or multiple files to load\r\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\r\n * @returns {Promise<Root>} Promise\r\n * @variation 3\r\n */\r\n// function load(filename:string, [options:IParseOptions]):Promise<Root>\r\n\r\n/**\r\n * Synchronously loads one or multiple .proto or preprocessed .json files into this root namespace (node only).\r\n * @function Root#loadSync\r\n * @param {string|string[]} filename Names of one or multiple files to load\r\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\r\n * @returns {Root} Root namespace\r\n * @throws {Error} If synchronous fetching is not supported (i.e. in browsers) or if a file's syntax is invalid\r\n */\r\nRoot.prototype.loadSync = function loadSync(filename, options) {\r\n    if (!util.isNode)\r\n        throw Error(\"not supported\");\r\n    return this.load(filename, options, SYNC);\r\n};\r\n\r\n/**\r\n * @override\r\n */\r\nRoot.prototype.resolveAll = function resolveAll() {\r\n    if (this.deferred.length)\r\n        throw Error(\"unresolvable extensions: \" + this.deferred.map(function(field) {\r\n            return \"'extend \" + field.extend + \"' in \" + field.parent.fullName;\r\n        }).join(\", \"));\r\n    return Namespace.prototype.resolveAll.call(this);\r\n};\r\n\r\n// only uppercased (and thus conflict-free) children are exposed, see below\r\nvar exposeRe = /^[A-Z]/;\r\n\r\n/**\r\n * Handles a deferred declaring extension field by creating a sister field to represent it within its extended type.\r\n * @param {Root} root Root instance\r\n * @param {Field} field Declaring extension field witin the declaring type\r\n * @returns {boolean} `true` if successfully added to the extended type, `false` otherwise\r\n * @inner\r\n * @ignore\r\n */\r\nfunction tryHandleExtension(root, field) {\r\n    var extendedType = field.parent.lookup(field.extend);\r\n    if (extendedType) {\r\n        var sisterField = new Field(field.fullName, field.id, field.type, field.rule, undefined, field.options);\r\n        sisterField.declaringField = field;\r\n        field.extensionField = sisterField;\r\n        extendedType.add(sisterField);\r\n        return true;\r\n    }\r\n    return false;\r\n}\r\n\r\n/**\r\n * Called when any object is added to this root or its sub-namespaces.\r\n * @param {ReflectionObject} object Object added\r\n * @returns {undefined}\r\n * @private\r\n */\r\nRoot.prototype._handleAdd = function _handleAdd(object) {\r\n    if (object instanceof Field) {\r\n\r\n        if (/* an extension field (implies not part of a oneof) */ object.extend !== undefined && /* not already handled */ !object.extensionField)\r\n            if (!tryHandleExtension(this, object))\r\n                this.deferred.push(object);\r\n\r\n    } else if (object instanceof Enum) {\r\n\r\n        if (exposeRe.test(object.name))\r\n            object.parent[object.name] = object.values; // expose enum values as property of its parent\r\n\r\n    } else if (!(object instanceof OneOf)) /* everything else is a namespace */ {\r\n\r\n        if (object instanceof Type) // Try to handle any deferred extensions\r\n            for (var i = 0; i < this.deferred.length;)\r\n                if (tryHandleExtension(this, this.deferred[i]))\r\n                    this.deferred.splice(i, 1);\r\n                else\r\n                    ++i;\r\n        for (var j = 0; j < /* initializes */ object.nestedArray.length; ++j) // recurse into the namespace\r\n            this._handleAdd(object._nestedArray[j]);\r\n        if (exposeRe.test(object.name))\r\n            object.parent[object.name] = object; // expose namespace as property of its parent\r\n    }\r\n\r\n    // The above also adds uppercased (and thus conflict-free) nested types, services and enums as\r\n    // properties of namespaces just like static code does. This allows using a .d.ts generated for\r\n    // a static module with reflection-based solutions where the condition is met.\r\n};\r\n\r\n/**\r\n * Called when any object is removed from this root or its sub-namespaces.\r\n * @param {ReflectionObject} object Object removed\r\n * @returns {undefined}\r\n * @private\r\n */\r\nRoot.prototype._handleRemove = function _handleRemove(object) {\r\n    if (object instanceof Field) {\r\n\r\n        if (/* an extension field */ object.extend !== undefined) {\r\n            if (/* already handled */ object.extensionField) { // remove its sister field\r\n                object.extensionField.parent.remove(object.extensionField);\r\n                object.extensionField = null;\r\n            } else { // cancel the extension\r\n                var index = this.deferred.indexOf(object);\r\n                /* istanbul ignore else */\r\n                if (index > -1)\r\n                    this.deferred.splice(index, 1);\r\n            }\r\n        }\r\n\r\n    } else if (object instanceof Enum) {\r\n\r\n        if (exposeRe.test(object.name))\r\n            delete object.parent[object.name]; // unexpose enum values\r\n\r\n    } else if (object instanceof Namespace) {\r\n\r\n        for (var i = 0; i < /* initializes */ object.nestedArray.length; ++i) // recurse into the namespace\r\n            this._handleRemove(object._nestedArray[i]);\r\n\r\n        if (exposeRe.test(object.name))\r\n            delete object.parent[object.name]; // unexpose namespaces\r\n\r\n    }\r\n};\r\n\r\n// Sets up cyclic dependencies (called in index-light)\r\nRoot._configure = function(Type_, parse_, common_) {\r\n    Type   = Type_;\r\n    parse  = parse_;\r\n    common = common_;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = {};\r\n\r\n/**\r\n * Named roots.\r\n * This is where pbjs stores generated structures (the option `-r, --root` specifies a name).\r\n * Can also be used manually to make roots available accross modules.\r\n * @name roots\r\n * @type {Object.<string,Root>}\r\n * @example\r\n * // pbjs -r myroot -o compiled.js ...\r\n *\r\n * // in another module:\r\n * require(\"./compiled.js\");\r\n *\r\n * // in any subsequent module:\r\n * var root = protobuf.roots[\"myroot\"];\r\n */\r\n", "\"use strict\";\r\n\r\n/**\r\n * Streaming RPC helpers.\r\n * @namespace\r\n */\r\nvar rpc = exports;\r\n\r\n/**\r\n * RPC implementation passed to {@link Service#create} performing a service request on network level, i.e. by utilizing http requests or websockets.\r\n * @typedef RPCImpl\r\n * @type {function}\r\n * @param {Method|rpc.ServiceMethod<Message<{}>,Message<{}>>} method Reflected or static method being called\r\n * @param {Uint8Array} requestData Request data\r\n * @param {RPCImplCallback} callback Callback function\r\n * @returns {undefined}\r\n * @example\r\n * function rpcImpl(method, requestData, callback) {\r\n *     if (protobuf.util.lcFirst(method.name) !== \"myMethod\") // compatible with static code\r\n *         throw Error(\"no such method\");\r\n *     asynchronouslyObtainAResponse(requestData, function(err, responseData) {\r\n *         callback(err, responseData);\r\n *     });\r\n * }\r\n */\r\n\r\n/**\r\n * Node-style callback as used by {@link RPCImpl}.\r\n * @typedef RPCImplCallback\r\n * @type {function}\r\n * @param {Error|null} error Error, if any, otherwise `null`\r\n * @param {Uint8Array|null} [response] Response data or `null` to signal end of stream, if there hasn't been an error\r\n * @returns {undefined}\r\n */\r\n\r\nrpc.Service = require(32);\r\n", "\"use strict\";\r\nmodule.exports = Service;\r\n\r\nvar util = require(39);\r\n\r\n// Extends EventEmitter\r\n(Service.prototype = Object.create(util.EventEmitter.prototype)).constructor = Service;\r\n\r\n/**\r\n * A service method callback as used by {@link rpc.ServiceMethod|ServiceMethod}.\r\n *\r\n * Differs from {@link RPCImplCallback} in that it is an actual callback of a service method which may not return `response = null`.\r\n * @typedef rpc.ServiceMethodCallback\r\n * @template TRes extends Message<TRes>\r\n * @type {function}\r\n * @param {Error|null} error Error, if any\r\n * @param {TRes} [response] Response message\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * A service method part of a {@link rpc.Service} as created by {@link Service.create}.\r\n * @typedef rpc.ServiceMethod\r\n * @template TReq extends Message<TReq>\r\n * @template TRes extends Message<TRes>\r\n * @type {function}\r\n * @param {TReq|Properties<TReq>} request Request message or plain object\r\n * @param {rpc.ServiceMethodCallback<TRes>} [callback] Node-style callback called with the error, if any, and the response message\r\n * @returns {Promise<Message<TRes>>} Promise if `callback` has been omitted, otherwise `undefined`\r\n */\r\n\r\n/**\r\n * Constructs a new RPC service instance.\r\n * @classdesc An RPC service as returned by {@link Service#create}.\r\n * @exports rpc.Service\r\n * @extends util.EventEmitter\r\n * @constructor\r\n * @param {RPCImpl} rpcImpl RPC implementation\r\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\r\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\r\n */\r\nfunction Service(rpcImpl, requestDelimited, responseDelimited) {\r\n\r\n    if (typeof rpcImpl !== \"function\")\r\n        throw TypeError(\"rpcImpl must be a function\");\r\n\r\n    util.EventEmitter.call(this);\r\n\r\n    /**\r\n     * RPC implementation. Becomes `null` once the service is ended.\r\n     * @type {RPCImpl|null}\r\n     */\r\n    this.rpcImpl = rpcImpl;\r\n\r\n    /**\r\n     * Whether requests are length-delimited.\r\n     * @type {boolean}\r\n     */\r\n    this.requestDelimited = Boolean(requestDelimited);\r\n\r\n    /**\r\n     * Whether responses are length-delimited.\r\n     * @type {boolean}\r\n     */\r\n    this.responseDelimited = Boolean(responseDelimited);\r\n}\r\n\r\n/**\r\n * Calls a service method through {@link rpc.Service#rpcImpl|rpcImpl}.\r\n * @param {Method|rpc.ServiceMethod<TReq,TRes>} method Reflected or static method\r\n * @param {Constructor<TReq>} requestCtor Request constructor\r\n * @param {Constructor<TRes>} responseCtor Response constructor\r\n * @param {TReq|Properties<TReq>} request Request message or plain object\r\n * @param {rpc.ServiceMethodCallback<TRes>} callback Service callback\r\n * @returns {undefined}\r\n * @template TReq extends Message<TReq>\r\n * @template TRes extends Message<TRes>\r\n */\r\nService.prototype.rpcCall = function rpcCall(method, requestCtor, responseCtor, request, callback) {\r\n\r\n    if (!request)\r\n        throw TypeError(\"request must be specified\");\r\n\r\n    var self = this;\r\n    if (!callback)\r\n        return util.asPromise(rpcCall, self, method, requestCtor, responseCtor, request);\r\n\r\n    if (!self.rpcImpl) {\r\n        setTimeout(function() { callback(Error(\"already ended\")); }, 0);\r\n        return undefined;\r\n    }\r\n\r\n    try {\r\n        return self.rpcImpl(\r\n            method,\r\n            requestCtor[self.requestDelimited ? \"encodeDelimited\" : \"encode\"](request).finish(),\r\n            function rpcCallback(err, response) {\r\n\r\n                if (err) {\r\n                    self.emit(\"error\", err, method);\r\n                    return callback(err);\r\n                }\r\n\r\n                if (response === null) {\r\n                    self.end(/* endedByRPC */ true);\r\n                    return undefined;\r\n                }\r\n\r\n                if (!(response instanceof responseCtor)) {\r\n                    try {\r\n                        response = responseCtor[self.responseDelimited ? \"decodeDelimited\" : \"decode\"](response);\r\n                    } catch (err) {\r\n                        self.emit(\"error\", err, method);\r\n                        return callback(err);\r\n                    }\r\n                }\r\n\r\n                self.emit(\"data\", response, method);\r\n                return callback(null, response);\r\n            }\r\n        );\r\n    } catch (err) {\r\n        self.emit(\"error\", err, method);\r\n        setTimeout(function() { callback(err); }, 0);\r\n        return undefined;\r\n    }\r\n};\r\n\r\n/**\r\n * Ends this service and emits the `end` event.\r\n * @param {boolean} [endedByRPC=false] Whether the service has been ended by the RPC implementation.\r\n * @returns {rpc.Service} `this`\r\n */\r\nService.prototype.end = function end(endedByRPC) {\r\n    if (this.rpcImpl) {\r\n        if (!endedByRPC) // signal end to rpcImpl\r\n            this.rpcImpl(null, null, null);\r\n        this.rpcImpl = null;\r\n        this.emit(\"end\").off();\r\n    }\r\n    return this;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = Service;\r\n\r\n// extends Namespace\r\nvar Namespace = require(23);\r\n((Service.prototype = Object.create(Namespace.prototype)).constructor = Service).className = \"Service\";\r\n\r\nvar Method = require(22),\r\n    util   = require(37),\r\n    rpc    = require(31);\r\n\r\n/**\r\n * Constructs a new service instance.\r\n * @classdesc Reflected service.\r\n * @extends NamespaceBase\r\n * @constructor\r\n * @param {string} name Service name\r\n * @param {Object.<string,*>} [options] Service options\r\n * @throws {TypeError} If arguments are invalid\r\n */\r\nfunction Service(name, options) {\r\n    Namespace.call(this, name, options);\r\n\r\n    /**\r\n     * Service methods.\r\n     * @type {Object.<string,Method>}\r\n     */\r\n    this.methods = {}; // toJSON, marker\r\n\r\n    /**\r\n     * Cached methods as an array.\r\n     * @type {Method[]|null}\r\n     * @private\r\n     */\r\n    this._methodsArray = null;\r\n}\r\n\r\n/**\r\n * Service descriptor.\r\n * @interface IService\r\n * @extends INamespace\r\n * @property {Object.<string,IMethod>} methods Method descriptors\r\n */\r\n\r\n/**\r\n * Constructs a service from a service descriptor.\r\n * @param {string} name Service name\r\n * @param {IService} json Service descriptor\r\n * @returns {Service} Created service\r\n * @throws {TypeError} If arguments are invalid\r\n */\r\nService.fromJSON = function fromJSON(name, json) {\r\n    var service = new Service(name, json.options);\r\n    /* istanbul ignore else */\r\n    if (json.methods)\r\n        for (var names = Object.keys(json.methods), i = 0; i < names.length; ++i)\r\n            service.add(Method.fromJSON(names[i], json.methods[names[i]]));\r\n    if (json.nested)\r\n        service.addJSON(json.nested);\r\n    service.comment = json.comment;\r\n    return service;\r\n};\r\n\r\n/**\r\n * Converts this service to a service descriptor.\r\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\r\n * @returns {IService} Service descriptor\r\n */\r\nService.prototype.toJSON = function toJSON(toJSONOptions) {\r\n    var inherited = Namespace.prototype.toJSON.call(this, toJSONOptions);\r\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\r\n    return util.toObject([\r\n        \"options\" , inherited && inherited.options || undefined,\r\n        \"methods\" , Namespace.arrayToJSON(this.methodsArray, toJSONOptions) || /* istanbul ignore next */ {},\r\n        \"nested\"  , inherited && inherited.nested || undefined,\r\n        \"comment\" , keepComments ? this.comment : undefined\r\n    ]);\r\n};\r\n\r\n/**\r\n * Methods of this service as an array for iteration.\r\n * @name Service#methodsArray\r\n * @type {Method[]}\r\n * @readonly\r\n */\r\nObject.defineProperty(Service.prototype, \"methodsArray\", {\r\n    get: function() {\r\n        return this._methodsArray || (this._methodsArray = util.toArray(this.methods));\r\n    }\r\n});\r\n\r\nfunction clearCache(service) {\r\n    service._methodsArray = null;\r\n    return service;\r\n}\r\n\r\n/**\r\n * @override\r\n */\r\nService.prototype.get = function get(name) {\r\n    return this.methods[name]\r\n        || Namespace.prototype.get.call(this, name);\r\n};\r\n\r\n/**\r\n * @override\r\n */\r\nService.prototype.resolveAll = function resolveAll() {\r\n    var methods = this.methodsArray;\r\n    for (var i = 0; i < methods.length; ++i)\r\n        methods[i].resolve();\r\n    return Namespace.prototype.resolve.call(this);\r\n};\r\n\r\n/**\r\n * @override\r\n */\r\nService.prototype.add = function add(object) {\r\n\r\n    /* istanbul ignore if */\r\n    if (this.get(object.name))\r\n        throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\r\n\r\n    if (object instanceof Method) {\r\n        this.methods[object.name] = object;\r\n        object.parent = this;\r\n        return clearCache(this);\r\n    }\r\n    return Namespace.prototype.add.call(this, object);\r\n};\r\n\r\n/**\r\n * @override\r\n */\r\nService.prototype.remove = function remove(object) {\r\n    if (object instanceof Method) {\r\n\r\n        /* istanbul ignore if */\r\n        if (this.methods[object.name] !== object)\r\n            throw Error(object + \" is not a member of \" + this);\r\n\r\n        delete this.methods[object.name];\r\n        object.parent = null;\r\n        return clearCache(this);\r\n    }\r\n    return Namespace.prototype.remove.call(this, object);\r\n};\r\n\r\n/**\r\n * Creates a runtime service using the specified rpc implementation.\r\n * @param {RPCImpl} rpcImpl RPC implementation\r\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\r\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\r\n * @returns {rpc.Service} RPC service. Useful where requests and/or responses are streamed.\r\n */\r\nService.prototype.create = function create(rpcImpl, requestDelimited, responseDelimited) {\r\n    var rpcService = new rpc.Service(rpcImpl, requestDelimited, responseDelimited);\r\n    for (var i = 0, method; i < /* initializes */ this.methodsArray.length; ++i) {\r\n        var methodName = util.lcFirst((method = this._methodsArray[i]).resolve().name).replace(/[^$\\w_]/g, \"\");\r\n        rpcService[methodName] = util.codegen([\"r\",\"c\"], util.isReserved(methodName) ? methodName + \"_\" : methodName)(\"return this.rpcCall(m,q,s,r,c)\")({\r\n            m: method,\r\n            q: method.resolvedRequestType.ctor,\r\n            s: method.resolvedResponseType.ctor\r\n        });\r\n    }\r\n    return rpcService;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = tokenize;\r\n\r\nvar delimRe        = /[\\s{}=;:[\\],'\"()<>]/g,\r\n    stringDoubleRe = /(?:\"([^\"\\\\]*(?:\\\\.[^\"\\\\]*)*)\")/g,\r\n    stringSingleRe = /(?:'([^'\\\\]*(?:\\\\.[^'\\\\]*)*)')/g;\r\n\r\nvar setCommentRe = /^ *[*/]+ */,\r\n    setCommentAltRe = /^\\s*\\*?\\/*/,\r\n    setCommentSplitRe = /\\n/g,\r\n    whitespaceRe = /\\s/,\r\n    unescapeRe = /\\\\(.?)/g;\r\n\r\nvar unescapeMap = {\r\n    \"0\": \"\\0\",\r\n    \"r\": \"\\r\",\r\n    \"n\": \"\\n\",\r\n    \"t\": \"\\t\"\r\n};\r\n\r\n/**\r\n * Unescapes a string.\r\n * @param {string} str String to unescape\r\n * @returns {string} Unescaped string\r\n * @property {Object.<string,string>} map Special characters map\r\n * @memberof tokenize\r\n */\r\nfunction unescape(str) {\r\n    return str.replace(unescapeRe, function($0, $1) {\r\n        switch ($1) {\r\n            case \"\\\\\":\r\n            case \"\":\r\n                return $1;\r\n            default:\r\n                return unescapeMap[$1] || \"\";\r\n        }\r\n    });\r\n}\r\n\r\ntokenize.unescape = unescape;\r\n\r\n/**\r\n * Gets the next token and advances.\r\n * @typedef TokenizerHandleNext\r\n * @type {function}\r\n * @returns {string|null} Next token or `null` on eof\r\n */\r\n\r\n/**\r\n * Peeks for the next token.\r\n * @typedef TokenizerHandlePeek\r\n * @type {function}\r\n * @returns {string|null} Next token or `null` on eof\r\n */\r\n\r\n/**\r\n * Pushes a token back to the stack.\r\n * @typedef TokenizerHandlePush\r\n * @type {function}\r\n * @param {string} token Token\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Skips the next token.\r\n * @typedef TokenizerHandleSkip\r\n * @type {function}\r\n * @param {string} expected Expected token\r\n * @param {boolean} [optional=false] If optional\r\n * @returns {boolean} Whether the token matched\r\n * @throws {Error} If the token didn't match and is not optional\r\n */\r\n\r\n/**\r\n * Gets the comment on the previous line or, alternatively, the line comment on the specified line.\r\n * @typedef TokenizerHandleCmnt\r\n * @type {function}\r\n * @param {number} [line] Line number\r\n * @returns {string|null} Comment text or `null` if none\r\n */\r\n\r\n/**\r\n * Handle object returned from {@link tokenize}.\r\n * @interface ITokenizerHandle\r\n * @property {TokenizerHandleNext} next Gets the next token and advances (`null` on eof)\r\n * @property {TokenizerHandlePeek} peek Peeks for the next token (`null` on eof)\r\n * @property {TokenizerHandlePush} push Pushes a token back to the stack\r\n * @property {TokenizerHandleSkip} skip Skips a token, returns its presence and advances or, if non-optional and not present, throws\r\n * @property {TokenizerHandleCmnt} cmnt Gets the comment on the previous line or the line comment on the specified line, if any\r\n * @property {number} line Current line number\r\n */\r\n\r\n/**\r\n * Tokenizes the given .proto source and returns an object with useful utility functions.\r\n * @param {string} source Source contents\r\n * @param {boolean} alternateCommentMode Whether we should activate alternate comment parsing mode.\r\n * @returns {ITokenizerHandle} Tokenizer handle\r\n */\r\nfunction tokenize(source, alternateCommentMode) {\r\n    /* eslint-disable callback-return */\r\n    source = source.toString();\r\n\r\n    var offset = 0,\r\n        length = source.length,\r\n        line = 1,\r\n        commentType = null,\r\n        commentText = null,\r\n        commentLine = 0,\r\n        commentLineEmpty = false;\r\n\r\n    var stack = [];\r\n\r\n    var stringDelim = null;\r\n\r\n    /* istanbul ignore next */\r\n    /**\r\n     * Creates an error for illegal syntax.\r\n     * @param {string} subject Subject\r\n     * @returns {Error} Error created\r\n     * @inner\r\n     */\r\n    function illegal(subject) {\r\n        return Error(\"illegal \" + subject + \" (line \" + line + \")\");\r\n    }\r\n\r\n    /**\r\n     * Reads a string till its end.\r\n     * @returns {string} String read\r\n     * @inner\r\n     */\r\n    function readString() {\r\n        var re = stringDelim === \"'\" ? stringSingleRe : stringDoubleRe;\r\n        re.lastIndex = offset - 1;\r\n        var match = re.exec(source);\r\n        if (!match)\r\n            throw illegal(\"string\");\r\n        offset = re.lastIndex;\r\n        push(stringDelim);\r\n        stringDelim = null;\r\n        return unescape(match[1]);\r\n    }\r\n\r\n    /**\r\n     * Gets the character at `pos` within the source.\r\n     * @param {number} pos Position\r\n     * @returns {string} Character\r\n     * @inner\r\n     */\r\n    function charAt(pos) {\r\n        return source.charAt(pos);\r\n    }\r\n\r\n    /**\r\n     * Sets the current comment text.\r\n     * @param {number} start Start offset\r\n     * @param {number} end End offset\r\n     * @returns {undefined}\r\n     * @inner\r\n     */\r\n    function setComment(start, end) {\r\n        commentType = source.charAt(start++);\r\n        commentLine = line;\r\n        commentLineEmpty = false;\r\n        var lookback;\r\n        if (alternateCommentMode) {\r\n            lookback = 2;  // alternate comment parsing: \"//\" or \"/*\"\r\n        } else {\r\n            lookback = 3;  // \"///\" or \"/**\"\r\n        }\r\n        var commentOffset = start - lookback,\r\n            c;\r\n        do {\r\n            if (--commentOffset < 0 ||\r\n                    (c = source.charAt(commentOffset)) === \"\\n\") {\r\n                commentLineEmpty = true;\r\n                break;\r\n            }\r\n        } while (c === \" \" || c === \"\\t\");\r\n        var lines = source\r\n            .substring(start, end)\r\n            .split(setCommentSplitRe);\r\n        for (var i = 0; i < lines.length; ++i)\r\n            lines[i] = lines[i]\r\n                .replace(alternateCommentMode ? setCommentAltRe : setCommentRe, \"\")\r\n                .trim();\r\n        commentText = lines\r\n            .join(\"\\n\")\r\n            .trim();\r\n    }\r\n\r\n    function isDoubleSlashCommentLine(startOffset) {\r\n        var endOffset = findEndOfLine(startOffset);\r\n\r\n        // see if remaining line matches comment pattern\r\n        var lineText = source.substring(startOffset, endOffset);\r\n        // look for 1 or 2 slashes since startOffset would already point past\r\n        // the first slash that started the comment.\r\n        var isComment = /^\\s*\\/{1,2}/.test(lineText);\r\n        return isComment;\r\n    }\r\n\r\n    function findEndOfLine(cursor) {\r\n        // find end of cursor's line\r\n        var endOffset = cursor;\r\n        while (endOffset < length && charAt(endOffset) !== \"\\n\") {\r\n            endOffset++;\r\n        }\r\n        return endOffset;\r\n    }\r\n\r\n    /**\r\n     * Obtains the next token.\r\n     * @returns {string|null} Next token or `null` on eof\r\n     * @inner\r\n     */\r\n    function next() {\r\n        if (stack.length > 0)\r\n            return stack.shift();\r\n        if (stringDelim)\r\n            return readString();\r\n        var repeat,\r\n            prev,\r\n            curr,\r\n            start,\r\n            isDoc;\r\n        do {\r\n            if (offset === length)\r\n                return null;\r\n            repeat = false;\r\n            while (whitespaceRe.test(curr = charAt(offset))) {\r\n                if (curr === \"\\n\")\r\n                    ++line;\r\n                if (++offset === length)\r\n                    return null;\r\n            }\r\n\r\n            if (charAt(offset) === \"/\") {\r\n                if (++offset === length) {\r\n                    throw illegal(\"comment\");\r\n                }\r\n                if (charAt(offset) === \"/\") { // Line\r\n                    if (!alternateCommentMode) {\r\n                        // check for triple-slash comment\r\n                        isDoc = charAt(start = offset + 1) === \"/\";\r\n\r\n                        while (charAt(++offset) !== \"\\n\") {\r\n                            if (offset === length) {\r\n                                return null;\r\n                            }\r\n                        }\r\n                        ++offset;\r\n                        if (isDoc) {\r\n                            setComment(start, offset - 1);\r\n                        }\r\n                        ++line;\r\n                        repeat = true;\r\n                    } else {\r\n                        // check for double-slash comments, consolidating consecutive lines\r\n                        start = offset;\r\n                        isDoc = false;\r\n                        if (isDoubleSlashCommentLine(offset)) {\r\n                            isDoc = true;\r\n                            do {\r\n                                offset = findEndOfLine(offset);\r\n                                if (offset === length) {\r\n                                    break;\r\n                                }\r\n                                offset++;\r\n                            } while (isDoubleSlashCommentLine(offset));\r\n                        } else {\r\n                            offset = Math.min(length, findEndOfLine(offset) + 1);\r\n                        }\r\n                        if (isDoc) {\r\n                            setComment(start, offset);\r\n                        }\r\n                        line++;\r\n                        repeat = true;\r\n                    }\r\n                } else if ((curr = charAt(offset)) === \"*\") { /* Block */\r\n                    // check for /** (regular comment mode) or /* (alternate comment mode)\r\n                    start = offset + 1;\r\n                    isDoc = alternateCommentMode || charAt(start) === \"*\";\r\n                    do {\r\n                        if (curr === \"\\n\") {\r\n                            ++line;\r\n                        }\r\n                        if (++offset === length) {\r\n                            throw illegal(\"comment\");\r\n                        }\r\n                        prev = curr;\r\n                        curr = charAt(offset);\r\n                    } while (prev !== \"*\" || curr !== \"/\");\r\n                    ++offset;\r\n                    if (isDoc) {\r\n                        setComment(start, offset - 2);\r\n                    }\r\n                    repeat = true;\r\n                } else {\r\n                    return \"/\";\r\n                }\r\n            }\r\n        } while (repeat);\r\n\r\n        // offset !== length if we got here\r\n\r\n        var end = offset;\r\n        delimRe.lastIndex = 0;\r\n        var delim = delimRe.test(charAt(end++));\r\n        if (!delim)\r\n            while (end < length && !delimRe.test(charAt(end)))\r\n                ++end;\r\n        var token = source.substring(offset, offset = end);\r\n        if (token === \"\\\"\" || token === \"'\")\r\n            stringDelim = token;\r\n        return token;\r\n    }\r\n\r\n    /**\r\n     * Pushes a token back to the stack.\r\n     * @param {string} token Token\r\n     * @returns {undefined}\r\n     * @inner\r\n     */\r\n    function push(token) {\r\n        stack.push(token);\r\n    }\r\n\r\n    /**\r\n     * Peeks for the next token.\r\n     * @returns {string|null} Token or `null` on eof\r\n     * @inner\r\n     */\r\n    function peek() {\r\n        if (!stack.length) {\r\n            var token = next();\r\n            if (token === null)\r\n                return null;\r\n            push(token);\r\n        }\r\n        return stack[0];\r\n    }\r\n\r\n    /**\r\n     * Skips a token.\r\n     * @param {string} expected Expected token\r\n     * @param {boolean} [optional=false] Whether the token is optional\r\n     * @returns {boolean} `true` when skipped, `false` if not\r\n     * @throws {Error} When a required token is not present\r\n     * @inner\r\n     */\r\n    function skip(expected, optional) {\r\n        var actual = peek(),\r\n            equals = actual === expected;\r\n        if (equals) {\r\n            next();\r\n            return true;\r\n        }\r\n        if (!optional)\r\n            throw illegal(\"token '\" + actual + \"', '\" + expected + \"' expected\");\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Gets a comment.\r\n     * @param {number} [trailingLine] Line number if looking for a trailing comment\r\n     * @returns {string|null} Comment text\r\n     * @inner\r\n     */\r\n    function cmnt(trailingLine) {\r\n        var ret = null;\r\n        if (trailingLine === undefined) {\r\n            if (commentLine === line - 1 && (alternateCommentMode || commentType === \"*\" || commentLineEmpty)) {\r\n                ret = commentText;\r\n            }\r\n        } else {\r\n            /* istanbul ignore else */\r\n            if (commentLine < trailingLine) {\r\n                peek();\r\n            }\r\n            if (commentLine === trailingLine && !commentLineEmpty && (alternateCommentMode || commentType === \"/\")) {\r\n                ret = commentText;\r\n            }\r\n        }\r\n        return ret;\r\n    }\r\n\r\n    return Object.defineProperty({\r\n        next: next,\r\n        peek: peek,\r\n        push: push,\r\n        skip: skip,\r\n        cmnt: cmnt\r\n    }, \"line\", {\r\n        get: function() { return line; }\r\n    });\r\n    /* eslint-enable callback-return */\r\n}\r\n", "\"use strict\";\r\nmodule.exports = Type;\r\n\r\n// extends Namespace\r\nvar Namespace = require(23);\r\n((Type.prototype = Object.create(Namespace.prototype)).constructor = Type).className = \"Type\";\r\n\r\nvar Enum      = require(15),\r\n    OneOf     = require(25),\r\n    Field     = require(16),\r\n    MapField  = require(20),\r\n    Service   = require(33),\r\n    Message   = require(21),\r\n    Reader    = require(27),\r\n    Writer    = require(42),\r\n    util      = require(37),\r\n    encoder   = require(14),\r\n    decoder   = require(13),\r\n    verifier  = require(40),\r\n    converter = require(12),\r\n    wrappers  = require(41);\r\n\r\n/**\r\n * Constructs a new reflected message type instance.\r\n * @classdesc Reflected message type.\r\n * @extends NamespaceBase\r\n * @constructor\r\n * @param {string} name Message name\r\n * @param {Object.<string,*>} [options] Declared options\r\n */\r\nfunction Type(name, options) {\r\n    Namespace.call(this, name, options);\r\n\r\n    /**\r\n     * Message fields.\r\n     * @type {Object.<string,Field>}\r\n     */\r\n    this.fields = {};  // toJSON, marker\r\n\r\n    /**\r\n     * Oneofs declared within this namespace, if any.\r\n     * @type {Object.<string,OneOf>}\r\n     */\r\n    this.oneofs = undefined; // toJSON\r\n\r\n    /**\r\n     * Extension ranges, if any.\r\n     * @type {number[][]}\r\n     */\r\n    this.extensions = undefined; // toJSON\r\n\r\n    /**\r\n     * Reserved ranges, if any.\r\n     * @type {Array.<number[]|string>}\r\n     */\r\n    this.reserved = undefined; // toJSON\r\n\r\n    /*?\r\n     * Whether this type is a legacy group.\r\n     * @type {boolean|undefined}\r\n     */\r\n    this.group = undefined; // toJSON\r\n\r\n    /**\r\n     * Cached fields by id.\r\n     * @type {Object.<number,Field>|null}\r\n     * @private\r\n     */\r\n    this._fieldsById = null;\r\n\r\n    /**\r\n     * Cached fields as an array.\r\n     * @type {Field[]|null}\r\n     * @private\r\n     */\r\n    this._fieldsArray = null;\r\n\r\n    /**\r\n     * Cached oneofs as an array.\r\n     * @type {OneOf[]|null}\r\n     * @private\r\n     */\r\n    this._oneofsArray = null;\r\n\r\n    /**\r\n     * Cached constructor.\r\n     * @type {Constructor<{}>}\r\n     * @private\r\n     */\r\n    this._ctor = null;\r\n}\r\n\r\nObject.defineProperties(Type.prototype, {\r\n\r\n    /**\r\n     * Message fields by id.\r\n     * @name Type#fieldsById\r\n     * @type {Object.<number,Field>}\r\n     * @readonly\r\n     */\r\n    fieldsById: {\r\n        get: function() {\r\n\r\n            /* istanbul ignore if */\r\n            if (this._fieldsById)\r\n                return this._fieldsById;\r\n\r\n            this._fieldsById = {};\r\n            for (var names = Object.keys(this.fields), i = 0; i < names.length; ++i) {\r\n                var field = this.fields[names[i]],\r\n                    id = field.id;\r\n\r\n                /* istanbul ignore if */\r\n                if (this._fieldsById[id])\r\n                    throw Error(\"duplicate id \" + id + \" in \" + this);\r\n\r\n                this._fieldsById[id] = field;\r\n            }\r\n            return this._fieldsById;\r\n        }\r\n    },\r\n\r\n    /**\r\n     * Fields of this message as an array for iteration.\r\n     * @name Type#fieldsArray\r\n     * @type {Field[]}\r\n     * @readonly\r\n     */\r\n    fieldsArray: {\r\n        get: function() {\r\n            return this._fieldsArray || (this._fieldsArray = util.toArray(this.fields));\r\n        }\r\n    },\r\n\r\n    /**\r\n     * Oneofs of this message as an array for iteration.\r\n     * @name Type#oneofsArray\r\n     * @type {OneOf[]}\r\n     * @readonly\r\n     */\r\n    oneofsArray: {\r\n        get: function() {\r\n            return this._oneofsArray || (this._oneofsArray = util.toArray(this.oneofs));\r\n        }\r\n    },\r\n\r\n    /**\r\n     * The registered constructor, if any registered, otherwise a generic constructor.\r\n     * Assigning a function replaces the internal constructor. If the function does not extend {@link Message} yet, its prototype will be setup accordingly and static methods will be populated. If it already extends {@link Message}, it will just replace the internal constructor.\r\n     * @name Type#ctor\r\n     * @type {Constructor<{}>}\r\n     */\r\n    ctor: {\r\n        get: function() {\r\n            return this._ctor || (this.ctor = Type.generateConstructor(this)());\r\n        },\r\n        set: function(ctor) {\r\n\r\n            // Ensure proper prototype\r\n            var prototype = ctor.prototype;\r\n            if (!(prototype instanceof Message)) {\r\n                (ctor.prototype = new Message()).constructor = ctor;\r\n                util.merge(ctor.prototype, prototype);\r\n            }\r\n\r\n            // Classes and messages reference their reflected type\r\n            ctor.$type = ctor.prototype.$type = this;\r\n\r\n            // Mix in static methods\r\n            util.merge(ctor, Message, true);\r\n\r\n            this._ctor = ctor;\r\n\r\n            // Messages have non-enumerable default values on their prototype\r\n            var i = 0;\r\n            for (; i < /* initializes */ this.fieldsArray.length; ++i)\r\n                this._fieldsArray[i].resolve(); // ensures a proper value\r\n\r\n            // Messages have non-enumerable getters and setters for each virtual oneof field\r\n            var ctorProperties = {};\r\n            for (i = 0; i < /* initializes */ this.oneofsArray.length; ++i)\r\n                ctorProperties[this._oneofsArray[i].resolve().name] = {\r\n                    get: util.oneOfGetter(this._oneofsArray[i].oneof),\r\n                    set: util.oneOfSetter(this._oneofsArray[i].oneof)\r\n                };\r\n            if (i)\r\n                Object.defineProperties(ctor.prototype, ctorProperties);\r\n        }\r\n    }\r\n});\r\n\r\n/**\r\n * Generates a constructor function for the specified type.\r\n * @param {Type} mtype Message type\r\n * @returns {Codegen} Codegen instance\r\n */\r\nType.generateConstructor = function generateConstructor(mtype) {\r\n    /* eslint-disable no-unexpected-multiline */\r\n    var gen = util.codegen([\"p\"], mtype.name);\r\n    // explicitly initialize mutable object/array fields so that these aren't just inherited from the prototype\r\n    for (var i = 0, field; i < mtype.fieldsArray.length; ++i)\r\n        if ((field = mtype._fieldsArray[i]).map) gen\r\n            (\"this%s={}\", util.safeProp(field.name));\r\n        else if (field.repeated) gen\r\n            (\"this%s=[]\", util.safeProp(field.name));\r\n    return gen\r\n    (\"if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)\") // omit undefined or null\r\n        (\"this[ks[i]]=p[ks[i]]\");\r\n    /* eslint-enable no-unexpected-multiline */\r\n};\r\n\r\nfunction clearCache(type) {\r\n    type._fieldsById = type._fieldsArray = type._oneofsArray = null;\r\n    delete type.encode;\r\n    delete type.decode;\r\n    delete type.verify;\r\n    return type;\r\n}\r\n\r\n/**\r\n * Message type descriptor.\r\n * @interface IType\r\n * @extends INamespace\r\n * @property {Object.<string,IOneOf>} [oneofs] Oneof descriptors\r\n * @property {Object.<string,IField>} fields Field descriptors\r\n * @property {number[][]} [extensions] Extension ranges\r\n * @property {number[][]} [reserved] Reserved ranges\r\n * @property {boolean} [group=false] Whether a legacy group or not\r\n */\r\n\r\n/**\r\n * Creates a message type from a message type descriptor.\r\n * @param {string} name Message name\r\n * @param {IType} json Message type descriptor\r\n * @returns {Type} Created message type\r\n */\r\nType.fromJSON = function fromJSON(name, json) {\r\n    var type = new Type(name, json.options);\r\n    type.extensions = json.extensions;\r\n    type.reserved = json.reserved;\r\n    var names = Object.keys(json.fields),\r\n        i = 0;\r\n    for (; i < names.length; ++i)\r\n        type.add(\r\n            ( typeof json.fields[names[i]].keyType !== \"undefined\"\r\n            ? MapField.fromJSON\r\n            : Field.fromJSON )(names[i], json.fields[names[i]])\r\n        );\r\n    if (json.oneofs)\r\n        for (names = Object.keys(json.oneofs), i = 0; i < names.length; ++i)\r\n            type.add(OneOf.fromJSON(names[i], json.oneofs[names[i]]));\r\n    if (json.nested)\r\n        for (names = Object.keys(json.nested), i = 0; i < names.length; ++i) {\r\n            var nested = json.nested[names[i]];\r\n            type.add( // most to least likely\r\n                ( nested.id !== undefined\r\n                ? Field.fromJSON\r\n                : nested.fields !== undefined\r\n                ? Type.fromJSON\r\n                : nested.values !== undefined\r\n                ? Enum.fromJSON\r\n                : nested.methods !== undefined\r\n                ? Service.fromJSON\r\n                : Namespace.fromJSON )(names[i], nested)\r\n            );\r\n        }\r\n    if (json.extensions && json.extensions.length)\r\n        type.extensions = json.extensions;\r\n    if (json.reserved && json.reserved.length)\r\n        type.reserved = json.reserved;\r\n    if (json.group)\r\n        type.group = true;\r\n    if (json.comment)\r\n        type.comment = json.comment;\r\n    return type;\r\n};\r\n\r\n/**\r\n * Converts this message type to a message type descriptor.\r\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\r\n * @returns {IType} Message type descriptor\r\n */\r\nType.prototype.toJSON = function toJSON(toJSONOptions) {\r\n    var inherited = Namespace.prototype.toJSON.call(this, toJSONOptions);\r\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\r\n    return util.toObject([\r\n        \"options\"    , inherited && inherited.options || undefined,\r\n        \"oneofs\"     , Namespace.arrayToJSON(this.oneofsArray, toJSONOptions),\r\n        \"fields\"     , Namespace.arrayToJSON(this.fieldsArray.filter(function(obj) { return !obj.declaringField; }), toJSONOptions) || {},\r\n        \"extensions\" , this.extensions && this.extensions.length ? this.extensions : undefined,\r\n        \"reserved\"   , this.reserved && this.reserved.length ? this.reserved : undefined,\r\n        \"group\"      , this.group || undefined,\r\n        \"nested\"     , inherited && inherited.nested || undefined,\r\n        \"comment\"    , keepComments ? this.comment : undefined\r\n    ]);\r\n};\r\n\r\n/**\r\n * @override\r\n */\r\nType.prototype.resolveAll = function resolveAll() {\r\n    var fields = this.fieldsArray, i = 0;\r\n    while (i < fields.length)\r\n        fields[i++].resolve();\r\n    var oneofs = this.oneofsArray; i = 0;\r\n    while (i < oneofs.length)\r\n        oneofs[i++].resolve();\r\n    return Namespace.prototype.resolveAll.call(this);\r\n};\r\n\r\n/**\r\n * @override\r\n */\r\nType.prototype.get = function get(name) {\r\n    return this.fields[name]\r\n        || this.oneofs && this.oneofs[name]\r\n        || this.nested && this.nested[name]\r\n        || null;\r\n};\r\n\r\n/**\r\n * Adds a nested object to this type.\r\n * @param {ReflectionObject} object Nested object to add\r\n * @returns {Type} `this`\r\n * @throws {TypeError} If arguments are invalid\r\n * @throws {Error} If there is already a nested object with this name or, if a field, when there is already a field with this id\r\n */\r\nType.prototype.add = function add(object) {\r\n\r\n    if (this.get(object.name))\r\n        throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\r\n\r\n    if (object instanceof Field && object.extend === undefined) {\r\n        // NOTE: Extension fields aren't actual fields on the declaring type, but nested objects.\r\n        // The root object takes care of adding distinct sister-fields to the respective extended\r\n        // type instead.\r\n\r\n        // avoids calling the getter if not absolutely necessary because it's called quite frequently\r\n        if (this._fieldsById ? /* istanbul ignore next */ this._fieldsById[object.id] : this.fieldsById[object.id])\r\n            throw Error(\"duplicate id \" + object.id + \" in \" + this);\r\n        if (this.isReservedId(object.id))\r\n            throw Error(\"id \" + object.id + \" is reserved in \" + this);\r\n        if (this.isReservedName(object.name))\r\n            throw Error(\"name '\" + object.name + \"' is reserved in \" + this);\r\n\r\n        if (object.parent)\r\n            object.parent.remove(object);\r\n        this.fields[object.name] = object;\r\n        object.message = this;\r\n        object.onAdd(this);\r\n        return clearCache(this);\r\n    }\r\n    if (object instanceof OneOf) {\r\n        if (!this.oneofs)\r\n            this.oneofs = {};\r\n        this.oneofs[object.name] = object;\r\n        object.onAdd(this);\r\n        return clearCache(this);\r\n    }\r\n    return Namespace.prototype.add.call(this, object);\r\n};\r\n\r\n/**\r\n * Removes a nested object from this type.\r\n * @param {ReflectionObject} object Nested object to remove\r\n * @returns {Type} `this`\r\n * @throws {TypeError} If arguments are invalid\r\n * @throws {Error} If `object` is not a member of this type\r\n */\r\nType.prototype.remove = function remove(object) {\r\n    if (object instanceof Field && object.extend === undefined) {\r\n        // See Type#add for the reason why extension fields are excluded here.\r\n\r\n        /* istanbul ignore if */\r\n        if (!this.fields || this.fields[object.name] !== object)\r\n            throw Error(object + \" is not a member of \" + this);\r\n\r\n        delete this.fields[object.name];\r\n        object.parent = null;\r\n        object.onRemove(this);\r\n        return clearCache(this);\r\n    }\r\n    if (object instanceof OneOf) {\r\n\r\n        /* istanbul ignore if */\r\n        if (!this.oneofs || this.oneofs[object.name] !== object)\r\n            throw Error(object + \" is not a member of \" + this);\r\n\r\n        delete this.oneofs[object.name];\r\n        object.parent = null;\r\n        object.onRemove(this);\r\n        return clearCache(this);\r\n    }\r\n    return Namespace.prototype.remove.call(this, object);\r\n};\r\n\r\n/**\r\n * Tests if the specified id is reserved.\r\n * @param {number} id Id to test\r\n * @returns {boolean} `true` if reserved, otherwise `false`\r\n */\r\nType.prototype.isReservedId = function isReservedId(id) {\r\n    return Namespace.isReservedId(this.reserved, id);\r\n};\r\n\r\n/**\r\n * Tests if the specified name is reserved.\r\n * @param {string} name Name to test\r\n * @returns {boolean} `true` if reserved, otherwise `false`\r\n */\r\nType.prototype.isReservedName = function isReservedName(name) {\r\n    return Namespace.isReservedName(this.reserved, name);\r\n};\r\n\r\n/**\r\n * Creates a new message of this type using the specified properties.\r\n * @param {Object.<string,*>} [properties] Properties to set\r\n * @returns {Message<{}>} Message instance\r\n */\r\nType.prototype.create = function create(properties) {\r\n    return new this.ctor(properties);\r\n};\r\n\r\n/**\r\n * Sets up {@link Type#encode|encode}, {@link Type#decode|decode} and {@link Type#verify|verify}.\r\n * @returns {Type} `this`\r\n */\r\nType.prototype.setup = function setup() {\r\n    // Sets up everything at once so that the prototype chain does not have to be re-evaluated\r\n    // multiple times (V8, soft-deopt prototype-check).\r\n\r\n    var fullName = this.fullName,\r\n        types    = [];\r\n    for (var i = 0; i < /* initializes */ this.fieldsArray.length; ++i)\r\n        types.push(this._fieldsArray[i].resolve().resolvedType);\r\n\r\n    // Replace setup methods with type-specific generated functions\r\n    this.encode = encoder(this)({\r\n        Writer : Writer,\r\n        types  : types,\r\n        util   : util\r\n    });\r\n    this.decode = decoder(this)({\r\n        Reader : Reader,\r\n        types  : types,\r\n        util   : util\r\n    });\r\n    this.verify = verifier(this)({\r\n        types : types,\r\n        util  : util\r\n    });\r\n    this.fromObject = converter.fromObject(this)({\r\n        types : types,\r\n        util  : util\r\n    });\r\n    this.toObject = converter.toObject(this)({\r\n        types : types,\r\n        util  : util\r\n    });\r\n\r\n    // Inject custom wrappers for common types\r\n    var wrapper = wrappers[fullName];\r\n    if (wrapper) {\r\n        var originalThis = Object.create(this);\r\n        // if (wrapper.fromObject) {\r\n            originalThis.fromObject = this.fromObject;\r\n            this.fromObject = wrapper.fromObject.bind(originalThis);\r\n        // }\r\n        // if (wrapper.toObject) {\r\n            originalThis.toObject = this.toObject;\r\n            this.toObject = wrapper.toObject.bind(originalThis);\r\n        // }\r\n    }\r\n\r\n    return this;\r\n};\r\n\r\n/**\r\n * Encodes a message of this type. Does not implicitly {@link Type#verify|verify} messages.\r\n * @param {Message<{}>|Object.<string,*>} message Message instance or plain object\r\n * @param {Writer} [writer] Writer to encode to\r\n * @returns {Writer} writer\r\n */\r\nType.prototype.encode = function encode_setup(message, writer) {\r\n    return this.setup().encode(message, writer); // overrides this method\r\n};\r\n\r\n/**\r\n * Encodes a message of this type preceeded by its byte length as a varint. Does not implicitly {@link Type#verify|verify} messages.\r\n * @param {Message<{}>|Object.<string,*>} message Message instance or plain object\r\n * @param {Writer} [writer] Writer to encode to\r\n * @returns {Writer} writer\r\n */\r\nType.prototype.encodeDelimited = function encodeDelimited(message, writer) {\r\n    return this.encode(message, writer && writer.len ? writer.fork() : writer).ldelim();\r\n};\r\n\r\n/**\r\n * Decodes a message of this type.\r\n * @param {Reader|Uint8Array} reader Reader or buffer to decode from\r\n * @param {number} [length] Length of the message, if known beforehand\r\n * @returns {Message<{}>} Decoded message\r\n * @throws {Error} If the payload is not a reader or valid buffer\r\n * @throws {util.ProtocolError<{}>} If required fields are missing\r\n */\r\nType.prototype.decode = function decode_setup(reader, length) {\r\n    return this.setup().decode(reader, length); // overrides this method\r\n};\r\n\r\n/**\r\n * Decodes a message of this type preceeded by its byte length as a varint.\r\n * @param {Reader|Uint8Array} reader Reader or buffer to decode from\r\n * @returns {Message<{}>} Decoded message\r\n * @throws {Error} If the payload is not a reader or valid buffer\r\n * @throws {util.ProtocolError} If required fields are missing\r\n */\r\nType.prototype.decodeDelimited = function decodeDelimited(reader) {\r\n    if (!(reader instanceof Reader))\r\n        reader = Reader.create(reader);\r\n    return this.decode(reader, reader.uint32());\r\n};\r\n\r\n/**\r\n * Verifies that field values are valid and that required fields are present.\r\n * @param {Object.<string,*>} message Plain object to verify\r\n * @returns {null|string} `null` if valid, otherwise the reason why it is not\r\n */\r\nType.prototype.verify = function verify_setup(message) {\r\n    return this.setup().verify(message); // overrides this method\r\n};\r\n\r\n/**\r\n * Creates a new message of this type from a plain object. Also converts values to their respective internal types.\r\n * @param {Object.<string,*>} object Plain object to convert\r\n * @returns {Message<{}>} Message instance\r\n */\r\nType.prototype.fromObject = function fromObject(object) {\r\n    return this.setup().fromObject(object);\r\n};\r\n\r\n/**\r\n * Conversion options as used by {@link Type#toObject} and {@link Message.toObject}.\r\n * @interface IConversionOptions\r\n * @property {Function} [longs] Long conversion type.\r\n * Valid values are `String` and `Number` (the global types).\r\n * Defaults to copy the present value, which is a possibly unsafe number without and a {@link Long} with a long library.\r\n * @property {Function} [enums] Enum value conversion type.\r\n * Only valid value is `String` (the global type).\r\n * Defaults to copy the present value, which is the numeric id.\r\n * @property {Function} [bytes] Bytes value conversion type.\r\n * Valid values are `Array` and (a base64 encoded) `String` (the global types).\r\n * Defaults to copy the present value, which usually is a Buffer under node and an Uint8Array in the browser.\r\n * @property {boolean} [defaults=false] Also sets default values on the resulting object\r\n * @property {boolean} [arrays=false] Sets empty arrays for missing repeated fields even if `defaults=false`\r\n * @property {boolean} [objects=false] Sets empty objects for missing map fields even if `defaults=false`\r\n * @property {boolean} [oneofs=false] Includes virtual oneof properties set to the present field's name, if any\r\n * @property {boolean} [json=false] Performs additional JSON compatibility conversions, i.e. NaN and Infinity to strings\r\n */\r\n\r\n/**\r\n * Creates a plain object from a message of this type. Also converts values to other types if specified.\r\n * @param {Message<{}>} message Message instance\r\n * @param {IConversionOptions} [options] Conversion options\r\n * @returns {Object.<string,*>} Plain object\r\n */\r\nType.prototype.toObject = function toObject(message, options) {\r\n    return this.setup().toObject(message, options);\r\n};\r\n\r\n/**\r\n * Decorator function as returned by {@link Type.d} (TypeScript).\r\n * @typedef TypeDecorator\r\n * @type {function}\r\n * @param {Constructor<T>} target Target constructor\r\n * @returns {undefined}\r\n * @template T extends Message<T>\r\n */\r\n\r\n/**\r\n * Type decorator (TypeScript).\r\n * @param {string} [typeName] Type name, defaults to the constructor's name\r\n * @returns {TypeDecorator<T>} Decorator function\r\n * @template T extends Message<T>\r\n */\r\nType.d = function decorateType(typeName) {\r\n    return function typeDecorator(target) {\r\n        util.decorateType(target, typeName);\r\n    };\r\n};\r\n", "\"use strict\";\r\n\r\n/**\r\n * Common type constants.\r\n * @namespace\r\n */\r\nvar types = exports;\r\n\r\nvar util = require(37);\r\n\r\nvar s = [\r\n    \"double\",   // 0\r\n    \"float\",    // 1\r\n    \"int32\",    // 2\r\n    \"uint32\",   // 3\r\n    \"sint32\",   // 4\r\n    \"fixed32\",  // 5\r\n    \"sfixed32\", // 6\r\n    \"int64\",    // 7\r\n    \"uint64\",   // 8\r\n    \"sint64\",   // 9\r\n    \"fixed64\",  // 10\r\n    \"sfixed64\", // 11\r\n    \"bool\",     // 12\r\n    \"string\",   // 13\r\n    \"bytes\"     // 14\r\n];\r\n\r\nfunction bake(values, offset) {\r\n    var i = 0, o = {};\r\n    offset |= 0;\r\n    while (i < values.length) o[s[i + offset]] = values[i++];\r\n    return o;\r\n}\r\n\r\n/**\r\n * Basic type wire types.\r\n * @type {Object.<string,number>}\r\n * @const\r\n * @property {number} double=1 Fixed64 wire type\r\n * @property {number} float=5 Fixed32 wire type\r\n * @property {number} int32=0 Varint wire type\r\n * @property {number} uint32=0 Varint wire type\r\n * @property {number} sint32=0 Varint wire type\r\n * @property {number} fixed32=5 Fixed32 wire type\r\n * @property {number} sfixed32=5 Fixed32 wire type\r\n * @property {number} int64=0 Varint wire type\r\n * @property {number} uint64=0 Varint wire type\r\n * @property {number} sint64=0 Varint wire type\r\n * @property {number} fixed64=1 Fixed64 wire type\r\n * @property {number} sfixed64=1 Fixed64 wire type\r\n * @property {number} bool=0 Varint wire type\r\n * @property {number} string=2 Ldelim wire type\r\n * @property {number} bytes=2 Ldelim wire type\r\n */\r\ntypes.basic = bake([\r\n    /* double   */ 1,\r\n    /* float    */ 5,\r\n    /* int32    */ 0,\r\n    /* uint32   */ 0,\r\n    /* sint32   */ 0,\r\n    /* fixed32  */ 5,\r\n    /* sfixed32 */ 5,\r\n    /* int64    */ 0,\r\n    /* uint64   */ 0,\r\n    /* sint64   */ 0,\r\n    /* fixed64  */ 1,\r\n    /* sfixed64 */ 1,\r\n    /* bool     */ 0,\r\n    /* string   */ 2,\r\n    /* bytes    */ 2\r\n]);\r\n\r\n/**\r\n * Basic type defaults.\r\n * @type {Object.<string,*>}\r\n * @const\r\n * @property {number} double=0 Double default\r\n * @property {number} float=0 Float default\r\n * @property {number} int32=0 Int32 default\r\n * @property {number} uint32=0 Uint32 default\r\n * @property {number} sint32=0 Sint32 default\r\n * @property {number} fixed32=0 Fixed32 default\r\n * @property {number} sfixed32=0 Sfixed32 default\r\n * @property {number} int64=0 Int64 default\r\n * @property {number} uint64=0 Uint64 default\r\n * @property {number} sint64=0 Sint32 default\r\n * @property {number} fixed64=0 Fixed64 default\r\n * @property {number} sfixed64=0 Sfixed64 default\r\n * @property {boolean} bool=false Bool default\r\n * @property {string} string=\"\" String default\r\n * @property {Array.<number>} bytes=Array(0) Bytes default\r\n * @property {null} message=null Message default\r\n */\r\ntypes.defaults = bake([\r\n    /* double   */ 0,\r\n    /* float    */ 0,\r\n    /* int32    */ 0,\r\n    /* uint32   */ 0,\r\n    /* sint32   */ 0,\r\n    /* fixed32  */ 0,\r\n    /* sfixed32 */ 0,\r\n    /* int64    */ 0,\r\n    /* uint64   */ 0,\r\n    /* sint64   */ 0,\r\n    /* fixed64  */ 0,\r\n    /* sfixed64 */ 0,\r\n    /* bool     */ false,\r\n    /* string   */ \"\",\r\n    /* bytes    */ util.emptyArray,\r\n    /* message  */ null\r\n]);\r\n\r\n/**\r\n * Basic long type wire types.\r\n * @type {Object.<string,number>}\r\n * @const\r\n * @property {number} int64=0 Varint wire type\r\n * @property {number} uint64=0 Varint wire type\r\n * @property {number} sint64=0 Varint wire type\r\n * @property {number} fixed64=1 Fixed64 wire type\r\n * @property {number} sfixed64=1 Fixed64 wire type\r\n */\r\ntypes.long = bake([\r\n    /* int64    */ 0,\r\n    /* uint64   */ 0,\r\n    /* sint64   */ 0,\r\n    /* fixed64  */ 1,\r\n    /* sfixed64 */ 1\r\n], 7);\r\n\r\n/**\r\n * Allowed types for map keys with their associated wire type.\r\n * @type {Object.<string,number>}\r\n * @const\r\n * @property {number} int32=0 Varint wire type\r\n * @property {number} uint32=0 Varint wire type\r\n * @property {number} sint32=0 Varint wire type\r\n * @property {number} fixed32=5 Fixed32 wire type\r\n * @property {number} sfixed32=5 Fixed32 wire type\r\n * @property {number} int64=0 Varint wire type\r\n * @property {number} uint64=0 Varint wire type\r\n * @property {number} sint64=0 Varint wire type\r\n * @property {number} fixed64=1 Fixed64 wire type\r\n * @property {number} sfixed64=1 Fixed64 wire type\r\n * @property {number} bool=0 Varint wire type\r\n * @property {number} string=2 Ldelim wire type\r\n */\r\ntypes.mapKey = bake([\r\n    /* int32    */ 0,\r\n    /* uint32   */ 0,\r\n    /* sint32   */ 0,\r\n    /* fixed32  */ 5,\r\n    /* sfixed32 */ 5,\r\n    /* int64    */ 0,\r\n    /* uint64   */ 0,\r\n    /* sint64   */ 0,\r\n    /* fixed64  */ 1,\r\n    /* sfixed64 */ 1,\r\n    /* bool     */ 0,\r\n    /* string   */ 2\r\n], 2);\r\n\r\n/**\r\n * Allowed types for packed repeated fields with their associated wire type.\r\n * @type {Object.<string,number>}\r\n * @const\r\n * @property {number} double=1 Fixed64 wire type\r\n * @property {number} float=5 Fixed32 wire type\r\n * @property {number} int32=0 Varint wire type\r\n * @property {number} uint32=0 Varint wire type\r\n * @property {number} sint32=0 Varint wire type\r\n * @property {number} fixed32=5 Fixed32 wire type\r\n * @property {number} sfixed32=5 Fixed32 wire type\r\n * @property {number} int64=0 Varint wire type\r\n * @property {number} uint64=0 Varint wire type\r\n * @property {number} sint64=0 Varint wire type\r\n * @property {number} fixed64=1 Fixed64 wire type\r\n * @property {number} sfixed64=1 Fixed64 wire type\r\n * @property {number} bool=0 Varint wire type\r\n */\r\ntypes.packed = bake([\r\n    /* double   */ 1,\r\n    /* float    */ 5,\r\n    /* int32    */ 0,\r\n    /* uint32   */ 0,\r\n    /* sint32   */ 0,\r\n    /* fixed32  */ 5,\r\n    /* sfixed32 */ 5,\r\n    /* int64    */ 0,\r\n    /* uint64   */ 0,\r\n    /* sint64   */ 0,\r\n    /* fixed64  */ 1,\r\n    /* sfixed64 */ 1,\r\n    /* bool     */ 0\r\n]);\r\n", "\"use strict\";\r\n\r\n/**\r\n * Various utility functions.\r\n * @namespace\r\n */\r\nvar util = module.exports = require(39);\r\n\r\nvar roots = require(30);\r\n\r\nvar Type, // cyclic\r\n    Enum;\r\n\r\nutil.codegen = require(3);\r\nutil.fetch   = require(5);\r\nutil.path    = require(8);\r\n\r\n/**\r\n * Node's fs module if available.\r\n * @type {Object.<string,*>}\r\n */\r\nutil.fs = util.inquire(\"fs\");\r\n\r\n/**\r\n * Converts an object's values to an array.\r\n * @param {Object.<string,*>} object Object to convert\r\n * @returns {Array.<*>} Converted array\r\n */\r\nutil.toArray = function toArray(object) {\r\n    if (object) {\r\n        var keys  = Object.keys(object),\r\n            array = new Array(keys.length),\r\n            index = 0;\r\n        while (index < keys.length)\r\n            array[index] = object[keys[index++]];\r\n        return array;\r\n    }\r\n    return [];\r\n};\r\n\r\n/**\r\n * Converts an array of keys immediately followed by their respective value to an object, omitting undefined values.\r\n * @param {Array.<*>} array Array to convert\r\n * @returns {Object.<string,*>} Converted object\r\n */\r\nutil.toObject = function toObject(array) {\r\n    var object = {},\r\n        index  = 0;\r\n    while (index < array.length) {\r\n        var key = array[index++],\r\n            val = array[index++];\r\n        if (val !== undefined)\r\n            object[key] = val;\r\n    }\r\n    return object;\r\n};\r\n\r\nvar safePropBackslashRe = /\\\\/g,\r\n    safePropQuoteRe     = /\"/g;\r\n\r\n/**\r\n * Tests whether the specified name is a reserved word in JS.\r\n * @param {string} name Name to test\r\n * @returns {boolean} `true` if reserved, otherwise `false`\r\n */\r\nutil.isReserved = function isReserved(name) {\r\n    return /^(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$/.test(name);\r\n};\r\n\r\n/**\r\n * Returns a safe property accessor for the specified property name.\r\n * @param {string} prop Property name\r\n * @returns {string} Safe accessor\r\n */\r\nutil.safeProp = function safeProp(prop) {\r\n    if (!/^[$\\w_]+$/.test(prop) || util.isReserved(prop))\r\n        return \"[\\\"\" + prop.replace(safePropBackslashRe, \"\\\\\\\\\").replace(safePropQuoteRe, \"\\\\\\\"\") + \"\\\"]\";\r\n    return \".\" + prop;\r\n};\r\n\r\n/**\r\n * Converts the first character of a string to upper case.\r\n * @param {string} str String to convert\r\n * @returns {string} Converted string\r\n */\r\nutil.ucFirst = function ucFirst(str) {\r\n    return str.charAt(0).toUpperCase() + str.substring(1);\r\n};\r\n\r\nvar camelCaseRe = /_([a-z])/g;\r\n\r\n/**\r\n * Converts a string to camel case.\r\n * @param {string} str String to convert\r\n * @returns {string} Converted string\r\n */\r\nutil.camelCase = function camelCase(str) {\r\n    return str.substring(0, 1)\r\n         + str.substring(1)\r\n               .replace(camelCaseRe, function($0, $1) { return $1.toUpperCase(); });\r\n};\r\n\r\n/**\r\n * Compares reflected fields by id.\r\n * @param {Field} a First field\r\n * @param {Field} b Second field\r\n * @returns {number} Comparison value\r\n */\r\nutil.compareFieldsById = function compareFieldsById(a, b) {\r\n    return a.id - b.id;\r\n};\r\n\r\n/**\r\n * Decorator helper for types (TypeScript).\r\n * @param {Constructor<T>} ctor Constructor function\r\n * @param {string} [typeName] Type name, defaults to the constructor's name\r\n * @returns {Type} Reflected type\r\n * @template T extends Message<T>\r\n * @property {Root} root Decorators root\r\n */\r\nutil.decorateType = function decorateType(ctor, typeName) {\r\n\r\n    /* istanbul ignore if */\r\n    if (ctor.$type) {\r\n        if (typeName && ctor.$type.name !== typeName) {\r\n            util.decorateRoot.remove(ctor.$type);\r\n            ctor.$type.name = typeName;\r\n            util.decorateRoot.add(ctor.$type);\r\n        }\r\n        return ctor.$type;\r\n    }\r\n\r\n    /* istanbul ignore next */\r\n    if (!Type)\r\n        Type = require(35);\r\n\r\n    var type = new Type(typeName || ctor.name);\r\n    util.decorateRoot.add(type);\r\n    type.ctor = ctor; // sets up .encode, .decode etc.\r\n    Object.defineProperty(ctor, \"$type\", { value: type, enumerable: false });\r\n    Object.defineProperty(ctor.prototype, \"$type\", { value: type, enumerable: false });\r\n    return type;\r\n};\r\n\r\nvar decorateEnumIndex = 0;\r\n\r\n/**\r\n * Decorator helper for enums (TypeScript).\r\n * @param {Object} object Enum object\r\n * @returns {Enum} Reflected enum\r\n */\r\nutil.decorateEnum = function decorateEnum(object) {\r\n\r\n    /* istanbul ignore if */\r\n    if (object.$type)\r\n        return object.$type;\r\n\r\n    /* istanbul ignore next */\r\n    if (!Enum)\r\n        Enum = require(15);\r\n\r\n    var enm = new Enum(\"Enum\" + decorateEnumIndex++, object);\r\n    util.decorateRoot.add(enm);\r\n    Object.defineProperty(object, \"$type\", { value: enm, enumerable: false });\r\n    return enm;\r\n};\r\n\r\n/**\r\n * Decorator root (TypeScript).\r\n * @name util.decorateRoot\r\n * @type {Root}\r\n * @readonly\r\n */\r\nObject.defineProperty(util, \"decorateRoot\", {\r\n    get: function() {\r\n        return roots[\"decorated\"] || (roots[\"decorated\"] = new (require(29))());\r\n    }\r\n});\r\n", "\"use strict\";\r\nmodule.exports = LongBits;\r\n\r\nvar util = require(39);\r\n\r\n/**\r\n * Constructs new long bits.\r\n * @classdesc Helper class for working with the low and high bits of a 64 bit value.\r\n * @memberof util\r\n * @constructor\r\n * @param {number} lo Low 32 bits, unsigned\r\n * @param {number} hi High 32 bits, unsigned\r\n */\r\nfunction LongBits(lo, hi) {\r\n\r\n    // note that the casts below are theoretically unnecessary as of today, but older statically\r\n    // generated converter code might still call the ctor with signed 32bits. kept for compat.\r\n\r\n    /**\r\n     * Low bits.\r\n     * @type {number}\r\n     */\r\n    this.lo = lo >>> 0;\r\n\r\n    /**\r\n     * High bits.\r\n     * @type {number}\r\n     */\r\n    this.hi = hi >>> 0;\r\n}\r\n\r\n/**\r\n * Zero bits.\r\n * @memberof util.LongBits\r\n * @type {util.LongBits}\r\n */\r\nvar zero = LongBits.zero = new LongBits(0, 0);\r\n\r\nzero.toNumber = function() { return 0; };\r\nzero.zzEncode = zero.zzDecode = function() { return this; };\r\nzero.length = function() { return 1; };\r\n\r\n/**\r\n * Zero hash.\r\n * @memberof util.LongBits\r\n * @type {string}\r\n */\r\nvar zeroHash = LongBits.zeroHash = \"\\0\\0\\0\\0\\0\\0\\0\\0\";\r\n\r\n/**\r\n * Constructs new long bits from the specified number.\r\n * @param {number} value Value\r\n * @returns {util.LongBits} Instance\r\n */\r\nLongBits.fromNumber = function fromNumber(value) {\r\n    if (value === 0)\r\n        return zero;\r\n    var sign = value < 0;\r\n    if (sign)\r\n        value = -value;\r\n    var lo = value >>> 0,\r\n        hi = (value - lo) / 4294967296 >>> 0;\r\n    if (sign) {\r\n        hi = ~hi >>> 0;\r\n        lo = ~lo >>> 0;\r\n        if (++lo > 4294967295) {\r\n            lo = 0;\r\n            if (++hi > 4294967295)\r\n                hi = 0;\r\n        }\r\n    }\r\n    return new LongBits(lo, hi);\r\n};\r\n\r\n/**\r\n * Constructs new long bits from a number, long or string.\r\n * @param {Long|number|string} value Value\r\n * @returns {util.LongBits} Instance\r\n */\r\nLongBits.from = function from(value) {\r\n    if (typeof value === \"number\")\r\n        return LongBits.fromNumber(value);\r\n    if (util.isString(value)) {\r\n        /* istanbul ignore else */\r\n        if (util.Long)\r\n            value = util.Long.fromString(value);\r\n        else\r\n            return LongBits.fromNumber(parseInt(value, 10));\r\n    }\r\n    return value.low || value.high ? new LongBits(value.low >>> 0, value.high >>> 0) : zero;\r\n};\r\n\r\n/**\r\n * Converts this long bits to a possibly unsafe JavaScript number.\r\n * @param {boolean} [unsigned=false] Whether unsigned or not\r\n * @returns {number} Possibly unsafe number\r\n */\r\nLongBits.prototype.toNumber = function toNumber(unsigned) {\r\n    if (!unsigned && this.hi >>> 31) {\r\n        var lo = ~this.lo + 1 >>> 0,\r\n            hi = ~this.hi     >>> 0;\r\n        if (!lo)\r\n            hi = hi + 1 >>> 0;\r\n        return -(lo + hi * 4294967296);\r\n    }\r\n    return this.lo + this.hi * 4294967296;\r\n};\r\n\r\n/**\r\n * Converts this long bits to a long.\r\n * @param {boolean} [unsigned=false] Whether unsigned or not\r\n * @returns {Long} Long\r\n */\r\nLongBits.prototype.toLong = function toLong(unsigned) {\r\n    return util.Long\r\n        ? new util.Long(this.lo | 0, this.hi | 0, Boolean(unsigned))\r\n        /* istanbul ignore next */\r\n        : { low: this.lo | 0, high: this.hi | 0, unsigned: Boolean(unsigned) };\r\n};\r\n\r\nvar charCodeAt = String.prototype.charCodeAt;\r\n\r\n/**\r\n * Constructs new long bits from the specified 8 characters long hash.\r\n * @param {string} hash Hash\r\n * @returns {util.LongBits} Bits\r\n */\r\nLongBits.fromHash = function fromHash(hash) {\r\n    if (hash === zeroHash)\r\n        return zero;\r\n    return new LongBits(\r\n        ( charCodeAt.call(hash, 0)\r\n        | charCodeAt.call(hash, 1) << 8\r\n        | charCodeAt.call(hash, 2) << 16\r\n        | charCodeAt.call(hash, 3) << 24) >>> 0\r\n    ,\r\n        ( charCodeAt.call(hash, 4)\r\n        | charCodeAt.call(hash, 5) << 8\r\n        | charCodeAt.call(hash, 6) << 16\r\n        | charCodeAt.call(hash, 7) << 24) >>> 0\r\n    );\r\n};\r\n\r\n/**\r\n * Converts this long bits to a 8 characters long hash.\r\n * @returns {string} Hash\r\n */\r\nLongBits.prototype.toHash = function toHash() {\r\n    return String.fromCharCode(\r\n        this.lo        & 255,\r\n        this.lo >>> 8  & 255,\r\n        this.lo >>> 16 & 255,\r\n        this.lo >>> 24      ,\r\n        this.hi        & 255,\r\n        this.hi >>> 8  & 255,\r\n        this.hi >>> 16 & 255,\r\n        this.hi >>> 24\r\n    );\r\n};\r\n\r\n/**\r\n * Zig-zag encodes this long bits.\r\n * @returns {util.LongBits} `this`\r\n */\r\nLongBits.prototype.zzEncode = function zzEncode() {\r\n    var mask =   this.hi >> 31;\r\n    this.hi  = ((this.hi << 1 | this.lo >>> 31) ^ mask) >>> 0;\r\n    this.lo  = ( this.lo << 1                   ^ mask) >>> 0;\r\n    return this;\r\n};\r\n\r\n/**\r\n * Zig-zag decodes this long bits.\r\n * @returns {util.LongBits} `this`\r\n */\r\nLongBits.prototype.zzDecode = function zzDecode() {\r\n    var mask = -(this.lo & 1);\r\n    this.lo  = ((this.lo >>> 1 | this.hi << 31) ^ mask) >>> 0;\r\n    this.hi  = ( this.hi >>> 1                  ^ mask) >>> 0;\r\n    return this;\r\n};\r\n\r\n/**\r\n * Calculates the length of this longbits when encoded as a varint.\r\n * @returns {number} Length\r\n */\r\nLongBits.prototype.length = function length() {\r\n    var part0 =  this.lo,\r\n        part1 = (this.lo >>> 28 | this.hi << 4) >>> 0,\r\n        part2 =  this.hi >>> 24;\r\n    return part2 === 0\r\n         ? part1 === 0\r\n           ? part0 < 16384\r\n             ? part0 < 128 ? 1 : 2\r\n             : part0 < 2097152 ? 3 : 4\r\n           : part1 < 16384\r\n             ? part1 < 128 ? 5 : 6\r\n             : part1 < 2097152 ? 7 : 8\r\n         : part2 < 128 ? 9 : 10;\r\n};\r\n", "\"use strict\";\r\nvar util = exports;\r\n\r\n// used to return a Promise where callback is omitted\r\nutil.asPromise = require(1);\r\n\r\n// converts to / from base64 encoded strings\r\nutil.base64 = require(2);\r\n\r\n// base class of rpc.Service\r\nutil.EventEmitter = require(4);\r\n\r\n// float handling accross browsers\r\nutil.float = require(6);\r\n\r\n// requires modules optionally and hides the call from bundlers\r\nutil.inquire = require(7);\r\n\r\n// converts to / from utf8 encoded strings\r\nutil.utf8 = require(10);\r\n\r\n// provides a node-like buffer pool in the browser\r\nutil.pool = require(9);\r\n\r\n// utility to work with the low and high bits of a 64 bit value\r\nutil.LongBits = require(38);\r\n\r\n// global object reference\r\nutil.global = typeof window !== \"undefined\" && window\r\n           || typeof global !== \"undefined\" && global\r\n           || typeof self   !== \"undefined\" && self\r\n           || this; // eslint-disable-line no-invalid-this\r\n\r\n/**\r\n * An immuable empty array.\r\n * @memberof util\r\n * @type {Array.<*>}\r\n * @const\r\n */\r\nutil.emptyArray = Object.freeze ? Object.freeze([]) : /* istanbul ignore next */ []; // used on prototypes\r\n\r\n/**\r\n * An immutable empty object.\r\n * @type {Object}\r\n * @const\r\n */\r\nutil.emptyObject = Object.freeze ? Object.freeze({}) : /* istanbul ignore next */ {}; // used on prototypes\r\n\r\n/**\r\n * Whether running within node or not.\r\n * @memberof util\r\n * @type {boolean}\r\n * @const\r\n */\r\nutil.isNode = Boolean(util.global.process && util.global.process.versions && util.global.process.versions.node);\r\n\r\n/**\r\n * Tests if the specified value is an integer.\r\n * @function\r\n * @param {*} value Value to test\r\n * @returns {boolean} `true` if the value is an integer\r\n */\r\nutil.isInteger = Number.isInteger || /* istanbul ignore next */ function isInteger(value) {\r\n    return typeof value === \"number\" && isFinite(value) && Math.floor(value) === value;\r\n};\r\n\r\n/**\r\n * Tests if the specified value is a string.\r\n * @param {*} value Value to test\r\n * @returns {boolean} `true` if the value is a string\r\n */\r\nutil.isString = function isString(value) {\r\n    return typeof value === \"string\" || value instanceof String;\r\n};\r\n\r\n/**\r\n * Tests if the specified value is a non-null object.\r\n * @param {*} value Value to test\r\n * @returns {boolean} `true` if the value is a non-null object\r\n */\r\nutil.isObject = function isObject(value) {\r\n    return value && typeof value === \"object\";\r\n};\r\n\r\n/**\r\n * Checks if a property on a message is considered to be present.\r\n * This is an alias of {@link util.isSet}.\r\n * @function\r\n * @param {Object} obj Plain object or message instance\r\n * @param {string} prop Property name\r\n * @returns {boolean} `true` if considered to be present, otherwise `false`\r\n */\r\nutil.isset =\r\n\r\n/**\r\n * Checks if a property on a message is considered to be present.\r\n * @param {Object} obj Plain object or message instance\r\n * @param {string} prop Property name\r\n * @returns {boolean} `true` if considered to be present, otherwise `false`\r\n */\r\nutil.isSet = function isSet(obj, prop) {\r\n    var value = obj[prop];\r\n    if (value != null && obj.hasOwnProperty(prop)) // eslint-disable-line eqeqeq, no-prototype-builtins\r\n        return typeof value !== \"object\" || (Array.isArray(value) ? value.length : Object.keys(value).length) > 0;\r\n    return false;\r\n};\r\n\r\n/**\r\n * Any compatible Buffer instance.\r\n * This is a minimal stand-alone definition of a Buffer instance. The actual type is that exported by node's typings.\r\n * @interface Buffer\r\n * @extends Uint8Array\r\n */\r\n\r\n/**\r\n * Node's Buffer class if available.\r\n * @type {Constructor<Buffer>}\r\n */\r\nutil.Buffer = (function() {\r\n    try {\r\n        var Buffer = util.inquire(\"buffer\").Buffer;\r\n        // refuse to use non-node buffers if not explicitly assigned (perf reasons):\r\n        return Buffer.prototype.utf8Write ? Buffer : /* istanbul ignore next */ null;\r\n    } catch (e) {\r\n        /* istanbul ignore next */\r\n        return null;\r\n    }\r\n})();\r\n\r\n// Internal alias of or polyfull for Buffer.from.\r\nutil._Buffer_from = null;\r\n\r\n// Internal alias of or polyfill for Buffer.allocUnsafe.\r\nutil._Buffer_allocUnsafe = null;\r\n\r\n/**\r\n * Creates a new buffer of whatever type supported by the environment.\r\n * @param {number|number[]} [sizeOrArray=0] Buffer size or number array\r\n * @returns {Uint8Array|Buffer} Buffer\r\n */\r\nutil.newBuffer = function newBuffer(sizeOrArray) {\r\n    /* istanbul ignore next */\r\n    return typeof sizeOrArray === \"number\"\r\n        ? util.Buffer\r\n            ? util._Buffer_allocUnsafe(sizeOrArray)\r\n            : new util.Array(sizeOrArray)\r\n        : util.Buffer\r\n            ? util._Buffer_from(sizeOrArray)\r\n            : typeof Uint8Array === \"undefined\"\r\n                ? sizeOrArray\r\n                : new Uint8Array(sizeOrArray);\r\n};\r\n\r\n/**\r\n * Array implementation used in the browser. `Uint8Array` if supported, otherwise `Array`.\r\n * @type {Constructor<Uint8Array>}\r\n */\r\nutil.Array = typeof Uint8Array !== \"undefined\" ? Uint8Array /* istanbul ignore next */ : Array;\r\n\r\n/**\r\n * Any compatible Long instance.\r\n * This is a minimal stand-alone definition of a Long instance. The actual type is that exported by long.js.\r\n * @interface Long\r\n * @property {number} low Low bits\r\n * @property {number} high High bits\r\n * @property {boolean} unsigned Whether unsigned or not\r\n */\r\n\r\n/**\r\n * Long.js's Long class if available.\r\n * @type {Constructor<Long>}\r\n */\r\nutil.Long = /* istanbul ignore next */ util.global.dcodeIO && /* istanbul ignore next */ util.global.dcodeIO.Long\r\n         || /* istanbul ignore next */ util.global.Long\r\n         || util.inquire(\"long\");\r\n\r\n/**\r\n * Regular expression used to verify 2 bit (`bool`) map keys.\r\n * @type {RegExp}\r\n * @const\r\n */\r\nutil.key2Re = /^true|false|0|1$/;\r\n\r\n/**\r\n * Regular expression used to verify 32 bit (`int32` etc.) map keys.\r\n * @type {RegExp}\r\n * @const\r\n */\r\nutil.key32Re = /^-?(?:0|[1-9][0-9]*)$/;\r\n\r\n/**\r\n * Regular expression used to verify 64 bit (`int64` etc.) map keys.\r\n * @type {RegExp}\r\n * @const\r\n */\r\nutil.key64Re = /^(?:[\\\\x00-\\\\xff]{8}|-?(?:0|[1-9][0-9]*))$/;\r\n\r\n/**\r\n * Converts a number or long to an 8 characters long hash string.\r\n * @param {Long|number} value Value to convert\r\n * @returns {string} Hash\r\n */\r\nutil.longToHash = function longToHash(value) {\r\n    return value\r\n        ? util.LongBits.from(value).toHash()\r\n        : util.LongBits.zeroHash;\r\n};\r\n\r\n/**\r\n * Converts an 8 characters long hash string to a long or number.\r\n * @param {string} hash Hash\r\n * @param {boolean} [unsigned=false] Whether unsigned or not\r\n * @returns {Long|number} Original value\r\n */\r\nutil.longFromHash = function longFromHash(hash, unsigned) {\r\n    var bits = util.LongBits.fromHash(hash);\r\n    if (util.Long)\r\n        return util.Long.fromBits(bits.lo, bits.hi, unsigned);\r\n    return bits.toNumber(Boolean(unsigned));\r\n};\r\n\r\n/**\r\n * Merges the properties of the source object into the destination object.\r\n * @memberof util\r\n * @param {Object.<string,*>} dst Destination object\r\n * @param {Object.<string,*>} src Source object\r\n * @param {boolean} [ifNotSet=false] Merges only if the key is not already set\r\n * @returns {Object.<string,*>} Destination object\r\n */\r\nfunction merge(dst, src, ifNotSet) { // used by converters\r\n    for (var keys = Object.keys(src), i = 0; i < keys.length; ++i)\r\n        if (dst[keys[i]] === undefined || !ifNotSet)\r\n            dst[keys[i]] = src[keys[i]];\r\n    return dst;\r\n}\r\n\r\nutil.merge = merge;\r\n\r\n/**\r\n * Converts the first character of a string to lower case.\r\n * @param {string} str String to convert\r\n * @returns {string} Converted string\r\n */\r\nutil.lcFirst = function lcFirst(str) {\r\n    return str.charAt(0).toLowerCase() + str.substring(1);\r\n};\r\n\r\n/**\r\n * Creates a custom error constructor.\r\n * @memberof util\r\n * @param {string} name Error name\r\n * @returns {Constructor<Error>} Custom error constructor\r\n */\r\nfunction newError(name) {\r\n\r\n    function CustomError(message, properties) {\r\n\r\n        if (!(this instanceof CustomError))\r\n            return new CustomError(message, properties);\r\n\r\n        // Error.call(this, message);\r\n        // ^ just returns a new error instance because the ctor can be called as a function\r\n\r\n        Object.defineProperty(this, \"message\", { get: function() { return message; } });\r\n\r\n        /* istanbul ignore next */\r\n        if (Error.captureStackTrace) // node\r\n            Error.captureStackTrace(this, CustomError);\r\n        else\r\n            Object.defineProperty(this, \"stack\", { value: (new Error()).stack || \"\" });\r\n\r\n        if (properties)\r\n            merge(this, properties);\r\n    }\r\n\r\n    (CustomError.prototype = Object.create(Error.prototype)).constructor = CustomError;\r\n\r\n    Object.defineProperty(CustomError.prototype, \"name\", { get: function() { return name; } });\r\n\r\n    CustomError.prototype.toString = function toString() {\r\n        return this.name + \": \" + this.message;\r\n    };\r\n\r\n    return CustomError;\r\n}\r\n\r\nutil.newError = newError;\r\n\r\n/**\r\n * Constructs a new protocol error.\r\n * @classdesc Error subclass indicating a protocol specifc error.\r\n * @memberof util\r\n * @extends Error\r\n * @template T extends Message<T>\r\n * @constructor\r\n * @param {string} message Error message\r\n * @param {Object.<string,*>} [properties] Additional properties\r\n * @example\r\n * try {\r\n *     MyMessage.decode(someBuffer); // throws if required fields are missing\r\n * } catch (e) {\r\n *     if (e instanceof ProtocolError && e.instance)\r\n *         console.log(\"decoded so far: \" + JSON.stringify(e.instance));\r\n * }\r\n */\r\nutil.ProtocolError = newError(\"ProtocolError\");\r\n\r\n/**\r\n * So far decoded message instance.\r\n * @name util.ProtocolError#instance\r\n * @type {Message<T>}\r\n */\r\n\r\n/**\r\n * A OneOf getter as returned by {@link util.oneOfGetter}.\r\n * @typedef OneOfGetter\r\n * @type {function}\r\n * @returns {string|undefined} Set field name, if any\r\n */\r\n\r\n/**\r\n * Builds a getter for a oneof's present field name.\r\n * @param {string[]} fieldNames Field names\r\n * @returns {OneOfGetter} Unbound getter\r\n */\r\nutil.oneOfGetter = function getOneOf(fieldNames) {\r\n    var fieldMap = {};\r\n    for (var i = 0; i < fieldNames.length; ++i)\r\n        fieldMap[fieldNames[i]] = 1;\r\n\r\n    /**\r\n     * @returns {string|undefined} Set field name, if any\r\n     * @this Object\r\n     * @ignore\r\n     */\r\n    return function() { // eslint-disable-line consistent-return\r\n        for (var keys = Object.keys(this), i = keys.length - 1; i > -1; --i)\r\n            if (fieldMap[keys[i]] === 1 && this[keys[i]] !== undefined && this[keys[i]] !== null)\r\n                return keys[i];\r\n    };\r\n};\r\n\r\n/**\r\n * A OneOf setter as returned by {@link util.oneOfSetter}.\r\n * @typedef OneOfSetter\r\n * @type {function}\r\n * @param {string|undefined} value Field name\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Builds a setter for a oneof's present field name.\r\n * @param {string[]} fieldNames Field names\r\n * @returns {OneOfSetter} Unbound setter\r\n */\r\nutil.oneOfSetter = function setOneOf(fieldNames) {\r\n\r\n    /**\r\n     * @param {string} name Field name\r\n     * @returns {undefined}\r\n     * @this Object\r\n     * @ignore\r\n     */\r\n    return function(name) {\r\n        for (var i = 0; i < fieldNames.length; ++i)\r\n            if (fieldNames[i] !== name)\r\n                delete this[fieldNames[i]];\r\n    };\r\n};\r\n\r\n/**\r\n * Default conversion options used for {@link Message#toJSON} implementations.\r\n *\r\n * These options are close to proto3's JSON mapping with the exception that internal types like Any are handled just like messages. More precisely:\r\n *\r\n * - Longs become strings\r\n * - Enums become string keys\r\n * - Bytes become base64 encoded strings\r\n * - (Sub-)Messages become plain objects\r\n * - Maps become plain objects with all string keys\r\n * - Repeated fields become arrays\r\n * - NaN and Infinity for float and double fields become strings\r\n *\r\n * @type {IConversionOptions}\r\n * @see https://developers.google.com/protocol-buffers/docs/proto3?hl=en#json\r\n */\r\nutil.toJSONOptions = {\r\n    longs: String,\r\n    enums: String,\r\n    bytes: String,\r\n    json: true\r\n};\r\n\r\n// Sets up buffer utility according to the environment (called in index-minimal)\r\nutil._configure = function() {\r\n    var Buffer = util.Buffer;\r\n    /* istanbul ignore if */\r\n    if (!Buffer) {\r\n        util._Buffer_from = util._Buffer_allocUnsafe = null;\r\n        return;\r\n    }\r\n    // because node 4.x buffers are incompatible & immutable\r\n    // see: https://github.com/dcodeIO/protobuf.js/pull/665\r\n    util._Buffer_from = Buffer.from !== Uint8Array.from && Buffer.from ||\r\n        /* istanbul ignore next */\r\n        function Buffer_from(value, encoding) {\r\n            return new Buffer(value, encoding);\r\n        };\r\n    util._Buffer_allocUnsafe = Buffer.allocUnsafe ||\r\n        /* istanbul ignore next */\r\n        function Buffer_allocUnsafe(size) {\r\n            return new Buffer(size);\r\n        };\r\n};\r\n", "\"use strict\";\r\nmodule.exports = verifier;\r\n\r\nvar Enum      = require(15),\r\n    util      = require(37);\r\n\r\nfunction invalid(field, expected) {\r\n    return field.name + \": \" + expected + (field.repeated && expected !== \"array\" ? \"[]\" : field.map && expected !== \"object\" ? \"{k:\"+field.keyType+\"}\" : \"\") + \" expected\";\r\n}\r\n\r\n/**\r\n * Generates a partial value verifier.\r\n * @param {Codegen} gen Codegen instance\r\n * @param {Field} field Reflected field\r\n * @param {number} fieldIndex Field index\r\n * @param {string} ref Variable reference\r\n * @returns {Codegen} Codegen instance\r\n * @ignore\r\n */\r\nfunction genVerifyValue(gen, field, fieldIndex, ref) {\r\n    /* eslint-disable no-unexpected-multiline */\r\n    if (field.resolvedType) {\r\n        if (field.resolvedType instanceof Enum) { gen\r\n            (\"switch(%s){\", ref)\r\n                (\"default:\")\r\n                    (\"return%j\", invalid(field, \"enum value\"));\r\n            for (var keys = Object.keys(field.resolvedType.values), j = 0; j < keys.length; ++j) gen\r\n                (\"case %i:\", field.resolvedType.values[keys[j]]);\r\n            gen\r\n                    (\"break\")\r\n            (\"}\");\r\n        } else {\r\n            gen\r\n            (\"{\")\r\n                (\"var e=types[%i].verify(%s);\", fieldIndex, ref)\r\n                (\"if(e)\")\r\n                    (\"return%j+e\", field.name + \".\")\r\n            (\"}\");\r\n        }\r\n    } else {\r\n        switch (field.type) {\r\n            case \"int32\":\r\n            case \"uint32\":\r\n            case \"sint32\":\r\n            case \"fixed32\":\r\n            case \"sfixed32\": gen\r\n                (\"if(!util.isInteger(%s))\", ref)\r\n                    (\"return%j\", invalid(field, \"integer\"));\r\n                break;\r\n            case \"int64\":\r\n            case \"uint64\":\r\n            case \"sint64\":\r\n            case \"fixed64\":\r\n            case \"sfixed64\": gen\r\n                (\"if(!util.isInteger(%s)&&!(%s&&util.isInteger(%s.low)&&util.isInteger(%s.high)))\", ref, ref, ref, ref)\r\n                    (\"return%j\", invalid(field, \"integer|Long\"));\r\n                break;\r\n            case \"float\":\r\n            case \"double\": gen\r\n                (\"if(typeof %s!==\\\"number\\\")\", ref)\r\n                    (\"return%j\", invalid(field, \"number\"));\r\n                break;\r\n            case \"bool\": gen\r\n                (\"if(typeof %s!==\\\"boolean\\\")\", ref)\r\n                    (\"return%j\", invalid(field, \"boolean\"));\r\n                break;\r\n            case \"string\": gen\r\n                (\"if(!util.isString(%s))\", ref)\r\n                    (\"return%j\", invalid(field, \"string\"));\r\n                break;\r\n            case \"bytes\": gen\r\n                (\"if(!(%s&&typeof %s.length===\\\"number\\\"||util.isString(%s)))\", ref, ref, ref)\r\n                    (\"return%j\", invalid(field, \"buffer\"));\r\n                break;\r\n        }\r\n    }\r\n    return gen;\r\n    /* eslint-enable no-unexpected-multiline */\r\n}\r\n\r\n/**\r\n * Generates a partial key verifier.\r\n * @param {Codegen} gen Codegen instance\r\n * @param {Field} field Reflected field\r\n * @param {string} ref Variable reference\r\n * @returns {Codegen} Codegen instance\r\n * @ignore\r\n */\r\nfunction genVerifyKey(gen, field, ref) {\r\n    /* eslint-disable no-unexpected-multiline */\r\n    switch (field.keyType) {\r\n        case \"int32\":\r\n        case \"uint32\":\r\n        case \"sint32\":\r\n        case \"fixed32\":\r\n        case \"sfixed32\": gen\r\n            (\"if(!util.key32Re.test(%s))\", ref)\r\n                (\"return%j\", invalid(field, \"integer key\"));\r\n            break;\r\n        case \"int64\":\r\n        case \"uint64\":\r\n        case \"sint64\":\r\n        case \"fixed64\":\r\n        case \"sfixed64\": gen\r\n            (\"if(!util.key64Re.test(%s))\", ref) // see comment above: x is ok, d is not\r\n                (\"return%j\", invalid(field, \"integer|Long key\"));\r\n            break;\r\n        case \"bool\": gen\r\n            (\"if(!util.key2Re.test(%s))\", ref)\r\n                (\"return%j\", invalid(field, \"boolean key\"));\r\n            break;\r\n    }\r\n    return gen;\r\n    /* eslint-enable no-unexpected-multiline */\r\n}\r\n\r\n/**\r\n * Generates a verifier specific to the specified message type.\r\n * @param {Type} mtype Message type\r\n * @returns {Codegen} Codegen instance\r\n */\r\nfunction verifier(mtype) {\r\n    /* eslint-disable no-unexpected-multiline */\r\n\r\n    var gen = util.codegen([\"m\"], mtype.name + \"$verify\")\r\n    (\"if(typeof m!==\\\"object\\\"||m===null)\")\r\n        (\"return%j\", \"object expected\");\r\n    var oneofs = mtype.oneofsArray,\r\n        seenFirstField = {};\r\n    if (oneofs.length) gen\r\n    (\"var p={}\");\r\n\r\n    for (var i = 0; i < /* initializes */ mtype.fieldsArray.length; ++i) {\r\n        var field = mtype._fieldsArray[i].resolve(),\r\n            ref   = \"m\" + util.safeProp(field.name);\r\n\r\n        if (field.optional) gen\r\n        (\"if(%s!=null&&m.hasOwnProperty(%j)){\", ref, field.name); // !== undefined && !== null\r\n\r\n        // map fields\r\n        if (field.map) { gen\r\n            (\"if(!util.isObject(%s))\", ref)\r\n                (\"return%j\", invalid(field, \"object\"))\r\n            (\"var k=Object.keys(%s)\", ref)\r\n            (\"for(var i=0;i<k.length;++i){\");\r\n                genVerifyKey(gen, field, \"k[i]\");\r\n                genVerifyValue(gen, field, i, ref + \"[k[i]]\")\r\n            (\"}\");\r\n\r\n        // repeated fields\r\n        } else if (field.repeated) { gen\r\n            (\"if(!Array.isArray(%s))\", ref)\r\n                (\"return%j\", invalid(field, \"array\"))\r\n            (\"for(var i=0;i<%s.length;++i){\", ref);\r\n                genVerifyValue(gen, field, i, ref + \"[i]\")\r\n            (\"}\");\r\n\r\n        // required or present fields\r\n        } else {\r\n            if (field.partOf) {\r\n                var oneofProp = util.safeProp(field.partOf.name);\r\n                if (seenFirstField[field.partOf.name] === 1) gen\r\n            (\"if(p%s===1)\", oneofProp)\r\n                (\"return%j\", field.partOf.name + \": multiple values\");\r\n                seenFirstField[field.partOf.name] = 1;\r\n                gen\r\n            (\"p%s=1\", oneofProp);\r\n            }\r\n            genVerifyValue(gen, field, i, ref);\r\n        }\r\n        if (field.optional) gen\r\n        (\"}\");\r\n    }\r\n    return gen\r\n    (\"return null\");\r\n    /* eslint-enable no-unexpected-multiline */\r\n}", "\"use strict\";\r\n\r\n/**\r\n * Wrappers for common types.\r\n * @type {Object.<string,IWrapper>}\r\n * @const\r\n */\r\nvar wrappers = exports;\r\n\r\nvar Message = require(21);\r\n\r\n/**\r\n * From object converter part of an {@link IWrapper}.\r\n * @typedef WrapperFromObjectConverter\r\n * @type {function}\r\n * @param {Object.<string,*>} object Plain object\r\n * @returns {Message<{}>} Message instance\r\n * @this Type\r\n */\r\n\r\n/**\r\n * To object converter part of an {@link IWrapper}.\r\n * @typedef WrapperToObjectConverter\r\n * @type {function}\r\n * @param {Message<{}>} message Message instance\r\n * @param {IConversionOptions} [options] Conversion options\r\n * @returns {Object.<string,*>} Plain object\r\n * @this Type\r\n */\r\n\r\n/**\r\n * Common type wrapper part of {@link wrappers}.\r\n * @interface IWrapper\r\n * @property {WrapperFromObjectConverter} [fromObject] From object converter\r\n * @property {WrapperToObjectConverter} [toObject] To object converter\r\n */\r\n\r\n// Custom wrapper for Any\r\nwrappers[\".google.protobuf.Any\"] = {\r\n\r\n    fromObject: function(object) {\r\n\r\n        // unwrap value type if mapped\r\n        if (object && object[\"@type\"]) {\r\n            var type = this.lookup(object[\"@type\"]);\r\n            /* istanbul ignore else */\r\n            if (type) {\r\n                // type_url does not accept leading \".\"\r\n                var type_url = object[\"@type\"].charAt(0) === \".\" ?\r\n                    object[\"@type\"].substr(1) : object[\"@type\"];\r\n                // type_url prefix is optional, but path seperator is required\r\n                return this.create({\r\n                    type_url: \"/\" + type_url,\r\n                    value: type.encode(type.fromObject(object)).finish()\r\n                });\r\n            }\r\n        }\r\n\r\n        return this.fromObject(object);\r\n    },\r\n\r\n    toObject: function(message, options) {\r\n\r\n        // decode value if requested and unmapped\r\n        if (options && options.json && message.type_url && message.value) {\r\n            // Only use fully qualified type name after the last '/'\r\n            var name = message.type_url.substring(message.type_url.lastIndexOf(\"/\") + 1);\r\n            var type = this.lookup(name);\r\n            /* istanbul ignore else */\r\n            if (type)\r\n                message = type.decode(message.value);\r\n        }\r\n\r\n        // wrap value if unmapped\r\n        if (!(message instanceof this.ctor) && message instanceof Message) {\r\n            var object = message.$type.toObject(message, options);\r\n            object[\"@type\"] = message.$type.fullName;\r\n            return object;\r\n        }\r\n\r\n        return this.toObject(message, options);\r\n    }\r\n};\r\n", "\"use strict\";\r\nmodule.exports = Writer;\r\n\r\nvar util      = require(39);\r\n\r\nvar BufferWriter; // cyclic\r\n\r\nvar LongBits  = util.LongBits,\r\n    base64    = util.base64,\r\n    utf8      = util.utf8;\r\n\r\n/**\r\n * Constructs a new writer operation instance.\r\n * @classdesc Scheduled writer operation.\r\n * @constructor\r\n * @param {function(*, Uint8Array, number)} fn Function to call\r\n * @param {number} len Value byte length\r\n * @param {*} val Value to write\r\n * @ignore\r\n */\r\nfunction Op(fn, len, val) {\r\n\r\n    /**\r\n     * Function to call.\r\n     * @type {function(Uint8Array, number, *)}\r\n     */\r\n    this.fn = fn;\r\n\r\n    /**\r\n     * Value byte length.\r\n     * @type {number}\r\n     */\r\n    this.len = len;\r\n\r\n    /**\r\n     * Next operation.\r\n     * @type {Writer.Op|undefined}\r\n     */\r\n    this.next = undefined;\r\n\r\n    /**\r\n     * Value to write.\r\n     * @type {*}\r\n     */\r\n    this.val = val; // type varies\r\n}\r\n\r\n/* istanbul ignore next */\r\nfunction noop() {} // eslint-disable-line no-empty-function\r\n\r\n/**\r\n * Constructs a new writer state instance.\r\n * @classdesc Copied writer state.\r\n * @memberof Writer\r\n * @constructor\r\n * @param {Writer} writer Writer to copy state from\r\n * @ignore\r\n */\r\nfunction State(writer) {\r\n\r\n    /**\r\n     * Current head.\r\n     * @type {Writer.Op}\r\n     */\r\n    this.head = writer.head;\r\n\r\n    /**\r\n     * Current tail.\r\n     * @type {Writer.Op}\r\n     */\r\n    this.tail = writer.tail;\r\n\r\n    /**\r\n     * Current buffer length.\r\n     * @type {number}\r\n     */\r\n    this.len = writer.len;\r\n\r\n    /**\r\n     * Next state.\r\n     * @type {State|null}\r\n     */\r\n    this.next = writer.states;\r\n}\r\n\r\n/**\r\n * Constructs a new writer instance.\r\n * @classdesc Wire format writer using `Uint8Array` if available, otherwise `Array`.\r\n * @constructor\r\n */\r\nfunction Writer() {\r\n\r\n    /**\r\n     * Current length.\r\n     * @type {number}\r\n     */\r\n    this.len = 0;\r\n\r\n    /**\r\n     * Operations head.\r\n     * @type {Object}\r\n     */\r\n    this.head = new Op(noop, 0, 0);\r\n\r\n    /**\r\n     * Operations tail\r\n     * @type {Object}\r\n     */\r\n    this.tail = this.head;\r\n\r\n    /**\r\n     * Linked forked states.\r\n     * @type {Object|null}\r\n     */\r\n    this.states = null;\r\n\r\n    // When a value is written, the writer calculates its byte length and puts it into a linked\r\n    // list of operations to perform when finish() is called. This both allows us to allocate\r\n    // buffers of the exact required size and reduces the amount of work we have to do compared\r\n    // to first calculating over objects and then encoding over objects. In our case, the encoding\r\n    // part is just a linked list walk calling operations with already prepared values.\r\n}\r\n\r\n/**\r\n * Creates a new writer.\r\n * @function\r\n * @returns {BufferWriter|Writer} A {@link BufferWriter} when Buffers are supported, otherwise a {@link Writer}\r\n */\r\nWriter.create = util.Buffer\r\n    ? function create_buffer_setup() {\r\n        return (Writer.create = function create_buffer() {\r\n            return new BufferWriter();\r\n        })();\r\n    }\r\n    /* istanbul ignore next */\r\n    : function create_array() {\r\n        return new Writer();\r\n    };\r\n\r\n/**\r\n * Allocates a buffer of the specified size.\r\n * @param {number} size Buffer size\r\n * @returns {Uint8Array} Buffer\r\n */\r\nWriter.alloc = function alloc(size) {\r\n    return new util.Array(size);\r\n};\r\n\r\n// Use Uint8Array buffer pool in the browser, just like node does with buffers\r\n/* istanbul ignore else */\r\nif (util.Array !== Array)\r\n    Writer.alloc = util.pool(Writer.alloc, util.Array.prototype.subarray);\r\n\r\n/**\r\n * Pushes a new operation to the queue.\r\n * @param {function(Uint8Array, number, *)} fn Function to call\r\n * @param {number} len Value byte length\r\n * @param {number} val Value to write\r\n * @returns {Writer} `this`\r\n * @private\r\n */\r\nWriter.prototype._push = function push(fn, len, val) {\r\n    this.tail = this.tail.next = new Op(fn, len, val);\r\n    this.len += len;\r\n    return this;\r\n};\r\n\r\nfunction writeByte(val, buf, pos) {\r\n    buf[pos] = val & 255;\r\n}\r\n\r\nfunction writeVarint32(val, buf, pos) {\r\n    while (val > 127) {\r\n        buf[pos++] = val & 127 | 128;\r\n        val >>>= 7;\r\n    }\r\n    buf[pos] = val;\r\n}\r\n\r\n/**\r\n * Constructs a new varint writer operation instance.\r\n * @classdesc Scheduled varint writer operation.\r\n * @extends Op\r\n * @constructor\r\n * @param {number} len Value byte length\r\n * @param {number} val Value to write\r\n * @ignore\r\n */\r\nfunction VarintOp(len, val) {\r\n    this.len = len;\r\n    this.next = undefined;\r\n    this.val = val;\r\n}\r\n\r\nVarintOp.prototype = Object.create(Op.prototype);\r\nVarintOp.prototype.fn = writeVarint32;\r\n\r\n/**\r\n * Writes an unsigned 32 bit value as a varint.\r\n * @param {number} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.uint32 = function write_uint32(value) {\r\n    // here, the call to this.push has been inlined and a varint specific Op subclass is used.\r\n    // uint32 is by far the most frequently used operation and benefits significantly from this.\r\n    this.len += (this.tail = this.tail.next = new VarintOp(\r\n        (value = value >>> 0)\r\n                < 128       ? 1\r\n        : value < 16384     ? 2\r\n        : value < 2097152   ? 3\r\n        : value < 268435456 ? 4\r\n        :                     5,\r\n    value)).len;\r\n    return this;\r\n};\r\n\r\n/**\r\n * Writes a signed 32 bit value as a varint.\r\n * @function\r\n * @param {number} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.int32 = function write_int32(value) {\r\n    return value < 0\r\n        ? this._push(writeVarint64, 10, LongBits.fromNumber(value)) // 10 bytes per spec\r\n        : this.uint32(value);\r\n};\r\n\r\n/**\r\n * Writes a 32 bit value as a varint, zig-zag encoded.\r\n * @param {number} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.sint32 = function write_sint32(value) {\r\n    return this.uint32((value << 1 ^ value >> 31) >>> 0);\r\n};\r\n\r\nfunction writeVarint64(val, buf, pos) {\r\n    while (val.hi) {\r\n        buf[pos++] = val.lo & 127 | 128;\r\n        val.lo = (val.lo >>> 7 | val.hi << 25) >>> 0;\r\n        val.hi >>>= 7;\r\n    }\r\n    while (val.lo > 127) {\r\n        buf[pos++] = val.lo & 127 | 128;\r\n        val.lo = val.lo >>> 7;\r\n    }\r\n    buf[pos++] = val.lo;\r\n}\r\n\r\n/**\r\n * Writes an unsigned 64 bit value as a varint.\r\n * @param {Long|number|string} value Value to write\r\n * @returns {Writer} `this`\r\n * @throws {TypeError} If `value` is a string and no long library is present.\r\n */\r\nWriter.prototype.uint64 = function write_uint64(value) {\r\n    var bits = LongBits.from(value);\r\n    return this._push(writeVarint64, bits.length(), bits);\r\n};\r\n\r\n/**\r\n * Writes a signed 64 bit value as a varint.\r\n * @function\r\n * @param {Long|number|string} value Value to write\r\n * @returns {Writer} `this`\r\n * @throws {TypeError} If `value` is a string and no long library is present.\r\n */\r\nWriter.prototype.int64 = Writer.prototype.uint64;\r\n\r\n/**\r\n * Writes a signed 64 bit value as a varint, zig-zag encoded.\r\n * @param {Long|number|string} value Value to write\r\n * @returns {Writer} `this`\r\n * @throws {TypeError} If `value` is a string and no long library is present.\r\n */\r\nWriter.prototype.sint64 = function write_sint64(value) {\r\n    var bits = LongBits.from(value).zzEncode();\r\n    return this._push(writeVarint64, bits.length(), bits);\r\n};\r\n\r\n/**\r\n * Writes a boolish value as a varint.\r\n * @param {boolean} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.bool = function write_bool(value) {\r\n    return this._push(writeByte, 1, value ? 1 : 0);\r\n};\r\n\r\nfunction writeFixed32(val, buf, pos) {\r\n    buf[pos    ] =  val         & 255;\r\n    buf[pos + 1] =  val >>> 8   & 255;\r\n    buf[pos + 2] =  val >>> 16  & 255;\r\n    buf[pos + 3] =  val >>> 24;\r\n}\r\n\r\n/**\r\n * Writes an unsigned 32 bit value as fixed 32 bits.\r\n * @param {number} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.fixed32 = function write_fixed32(value) {\r\n    return this._push(writeFixed32, 4, value >>> 0);\r\n};\r\n\r\n/**\r\n * Writes a signed 32 bit value as fixed 32 bits.\r\n * @function\r\n * @param {number} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.sfixed32 = Writer.prototype.fixed32;\r\n\r\n/**\r\n * Writes an unsigned 64 bit value as fixed 64 bits.\r\n * @param {Long|number|string} value Value to write\r\n * @returns {Writer} `this`\r\n * @throws {TypeError} If `value` is a string and no long library is present.\r\n */\r\nWriter.prototype.fixed64 = function write_fixed64(value) {\r\n    var bits = LongBits.from(value);\r\n    return this._push(writeFixed32, 4, bits.lo)._push(writeFixed32, 4, bits.hi);\r\n};\r\n\r\n/**\r\n * Writes a signed 64 bit value as fixed 64 bits.\r\n * @function\r\n * @param {Long|number|string} value Value to write\r\n * @returns {Writer} `this`\r\n * @throws {TypeError} If `value` is a string and no long library is present.\r\n */\r\nWriter.prototype.sfixed64 = Writer.prototype.fixed64;\r\n\r\n/**\r\n * Writes a float (32 bit).\r\n * @function\r\n * @param {number} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.float = function write_float(value) {\r\n    return this._push(util.float.writeFloatLE, 4, value);\r\n};\r\n\r\n/**\r\n * Writes a double (64 bit float).\r\n * @function\r\n * @param {number} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.double = function write_double(value) {\r\n    return this._push(util.float.writeDoubleLE, 8, value);\r\n};\r\n\r\nvar writeBytes = util.Array.prototype.set\r\n    ? function writeBytes_set(val, buf, pos) {\r\n        buf.set(val, pos); // also works for plain array values\r\n    }\r\n    /* istanbul ignore next */\r\n    : function writeBytes_for(val, buf, pos) {\r\n        for (var i = 0; i < val.length; ++i)\r\n            buf[pos + i] = val[i];\r\n    };\r\n\r\n/**\r\n * Writes a sequence of bytes.\r\n * @param {Uint8Array|string} value Buffer or base64 encoded string to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.bytes = function write_bytes(value) {\r\n    var len = value.length >>> 0;\r\n    if (!len)\r\n        return this._push(writeByte, 1, 0);\r\n    if (util.isString(value)) {\r\n        var buf = Writer.alloc(len = base64.length(value));\r\n        base64.decode(value, buf, 0);\r\n        value = buf;\r\n    }\r\n    return this.uint32(len)._push(writeBytes, len, value);\r\n};\r\n\r\n/**\r\n * Writes a string.\r\n * @param {string} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.string = function write_string(value) {\r\n    var len = utf8.length(value);\r\n    return len\r\n        ? this.uint32(len)._push(utf8.write, len, value)\r\n        : this._push(writeByte, 1, 0);\r\n};\r\n\r\n/**\r\n * Forks this writer's state by pushing it to a stack.\r\n * Calling {@link Writer#reset|reset} or {@link Writer#ldelim|ldelim} resets the writer to the previous state.\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.fork = function fork() {\r\n    this.states = new State(this);\r\n    this.head = this.tail = new Op(noop, 0, 0);\r\n    this.len = 0;\r\n    return this;\r\n};\r\n\r\n/**\r\n * Resets this instance to the last state.\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.reset = function reset() {\r\n    if (this.states) {\r\n        this.head   = this.states.head;\r\n        this.tail   = this.states.tail;\r\n        this.len    = this.states.len;\r\n        this.states = this.states.next;\r\n    } else {\r\n        this.head = this.tail = new Op(noop, 0, 0);\r\n        this.len  = 0;\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Resets to the last state and appends the fork state's current write length as a varint followed by its operations.\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.ldelim = function ldelim() {\r\n    var head = this.head,\r\n        tail = this.tail,\r\n        len  = this.len;\r\n    this.reset().uint32(len);\r\n    if (len) {\r\n        this.tail.next = head.next; // skip noop\r\n        this.tail = tail;\r\n        this.len += len;\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Finishes the write operation.\r\n * @returns {Uint8Array} Finished buffer\r\n */\r\nWriter.prototype.finish = function finish() {\r\n    var head = this.head.next, // skip noop\r\n        buf  = this.constructor.alloc(this.len),\r\n        pos  = 0;\r\n    while (head) {\r\n        head.fn(head.val, buf, pos);\r\n        pos += head.len;\r\n        head = head.next;\r\n    }\r\n    // this.head = this.tail = null;\r\n    return buf;\r\n};\r\n\r\nWriter._configure = function(BufferWriter_) {\r\n    BufferWriter = BufferWriter_;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = <PERSON><PERSON>erWriter;\r\n\r\n// extends Writer\r\nvar Writer = require(42);\r\n(BufferWriter.prototype = Object.create(Writer.prototype)).constructor = BufferWriter;\r\n\r\nvar util = require(39);\r\n\r\nvar Buffer = util.Buffer;\r\n\r\n/**\r\n * Constructs a new buffer writer instance.\r\n * @classdesc Wire format writer using node buffers.\r\n * @extends Writer\r\n * @constructor\r\n */\r\nfunction BufferWriter() {\r\n    Writer.call(this);\r\n}\r\n\r\n/**\r\n * Allocates a buffer of the specified size.\r\n * @param {number} size Buffer size\r\n * @returns {Buffer} Buffer\r\n */\r\nBufferWriter.alloc = function alloc_buffer(size) {\r\n    return (BufferWriter.alloc = util._Buffer_allocUnsafe)(size);\r\n};\r\n\r\nvar writeBytesBuffer = Buffer && Buffer.prototype instanceof Uint8Array && Buffer.prototype.set.name === \"set\"\r\n    ? function writeBytesBuffer_set(val, buf, pos) {\r\n        buf.set(val, pos); // faster than copy (requires node >= 4 where Buffers extend Uint8Array and set is properly inherited)\r\n                           // also works for plain array values\r\n    }\r\n    /* istanbul ignore next */\r\n    : function writeBytesBuffer_copy(val, buf, pos) {\r\n        if (val.copy) // Buffer values\r\n            val.copy(buf, pos, 0, val.length);\r\n        else for (var i = 0; i < val.length;) // plain array values\r\n            buf[pos++] = val[i++];\r\n    };\r\n\r\n/**\r\n * @override\r\n */\r\nBufferWriter.prototype.bytes = function write_bytes_buffer(value) {\r\n    if (util.isString(value))\r\n        value = util._Buffer_from(value, \"base64\");\r\n    var len = value.length >>> 0;\r\n    this.uint32(len);\r\n    if (len)\r\n        this._push(writeBytesBuffer, len, value);\r\n    return this;\r\n};\r\n\r\nfunction writeStringBuffer(val, buf, pos) {\r\n    if (val.length < 40) // plain js is faster for short strings (probably due to redundant assertions)\r\n        util.utf8.write(val, buf, pos);\r\n    else\r\n        buf.utf8Write(val, pos);\r\n}\r\n\r\n/**\r\n * @override\r\n */\r\nBufferWriter.prototype.string = function write_string_buffer(value) {\r\n    var len = Buffer.byteLength(value);\r\n    this.uint32(len);\r\n    if (len)\r\n        this._push(writeStringBuffer, len, value);\r\n    return this;\r\n};\r\n\r\n\r\n/**\r\n * Finishes the write operation.\r\n * @name BufferWriter#finish\r\n * @function\r\n * @returns {Buffer} Finished buffer\r\n */\r\n"], "sourceRoot": "."}