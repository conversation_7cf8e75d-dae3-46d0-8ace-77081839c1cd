// public/js/mqtt.js
document.addEventListener("DOMContentLoaded", function () {
    const mqttConfig = window.mqttConfig || {
        host: "ffe180c2d6054ac78559eccdc9597e4f.s1.eu.hivemq.cloud",
        port: 8884,
        clientId: "laravel_mqtt_client_" + Date.now(),
        username: "absensi-sekolah",
        password: "Acekolah123",
    };

    const client = new Paho.MQTT.Client(
        mqttConfig.host,
        mqttConfig.port,
        mqttConfig.clientId
    );

    client.onConnectionLost = onConnectionLost;
    client.onMessageArrived = onMessageArrived;

    client.connect({
        onSuccess: onConnect,
        userName: mqttConfig.username,
        password: mqttConfig.password,
        useSSL: true,
    });

    function onConnect() {
        console.log("Terhubung ke MQTT broker: " + mqttConfig.host);
        const statusElement = document.getElementById("mqtt-status");
        if (statusElement) {
            statusElement.innerHTML = "Terhubung ke MQTT broker!";
        }

        if (window.location.pathname === "/admin/alats/create") {
            client.subscribe("absensi/register");
            console.log("Subscribed to topic: absensi/register");
        }

        if (window.location.pathname === "/admin/gurus/create") {
            // Langkah 1: Mengirim perintah ke absensi/serial/in
            const commandMessage = new Paho.MQTT.Message(
                '{"command": "create_guru"}'
            );
            commandMessage.destinationName = "absensi/serial/in";
            client.send(commandMessage);
            console.log(
                "Perintah 'create_guru' dikirim ke topik: absensi/serial/in"
            );

            // Langkah 2: Berlangganan ke absensi/register
            client.subscribe("absensi/register");
            console.log("Subscribed to topic: absensi/register");
        }

        if (window.location.pathname === "/admin/absensi-gurus") {
            client.subscribe("absensi/data");
            console.log("Subscribed to topic: absensi/data");
        }
    }

    function onConnectionLost(responseObject) {
        if (responseObject.errorCode !== 0) {
            console.log("Koneksi terputus: " + responseObject.errorMessage);
        }
    }

    function onMessageArrived(message) {
        if (
            message.destinationName === "absensi/register" &&
            window.location.pathname === "/admin/alats/create"
        ) {
            try {
                const payload = JSON.parse(message.payloadString);
                const deviceId = payload.device_id;
                console.log(
                    "Menerima pesan MQTT dengan device_id: " + deviceId
                );
                setTimeout(() => {
                    if (window.Livewire) {
                        console.log("Livewire ditemukan");
                        window.Livewire.dispatch("updateUuidOptions", [
                            deviceId,
                        ]); // Kirim sebagai array
                        console.log(
                            "Event Livewire 'updateUuidOptions' dikirim dengan device_id: " +
                                deviceId
                        );
                    } else {
                        console.log(
                            "Livewire belum dimuat, mencoba Choices.js"
                        );
                        const selectField =
                            document.querySelector("#data\\.UUid");
                        if (selectField && window.Choices) {
                            const choicesInstance =
                                window.Choices.getInstance(selectField);
                            if (choicesInstance) {
                                console.log("Choices.js instance ditemukan");
                                choicesInstance.setChoices(
                                    [
                                        {
                                            value: deviceId,
                                            label: deviceId,
                                            selected: true,
                                        },
                                    ],
                                    "value",
                                    "label",
                                    false
                                );
                            } else {
                                console.log(
                                    "Choices.js instance tidak ditemukan"
                                );
                            }
                        } else {
                            console.log(
                                "Select atau Choices.js tidak ditemukan"
                            );
                        }
                    }
                }, 3000); // Tunggu 3 detik untuk memastikan Livewire dimuat
            } catch (error) {
                console.error("Gagal parsing JSON: ", error);
            }
        }

        if (
            message.destinationName === "absensi/register" &&
            window.location.pathname === "/admin/gurus/create"
        ) {
            try {
                const payload = JSON.parse(message.payloadString);
                const UUidGuru = payload.UUIDguru;
                const deviceId = payload.device_id;
                console.log("Menerima pesan MQTT dengan UUIDguru: " + UUidGuru);
                setTimeout(() => {
                    if (window.Livewire) {
                        console.log("Livewire ditemukan");
                        // Kirim UUIDguru dan deviceId (UUID alat) ke Livewire
                        window.Livewire.dispatch("updateUuidGuru", [UUidGuru, deviceId]);
                        console.log(
                            "Event Livewire 'updateUuidGuru' dikirim dengan UUIDguru: " +
                                UUidGuru + " dan device_id: " + deviceId
                        );
                    } else {
                        console.log(
                            "Livewire belum dimuat, mencoba Choices.js"
                        );
                        const selectField =
                            document.querySelector("#data\\.UUid");
                        if (selectField && window.Choices) {
                            const choicesInstance =
                                window.Choices.getInstance(selectField);
                            if (choicesInstance) {
                                console.log("Choices.js instance ditemukan");
                                choicesInstance.setChoices(
                                    [
                                        {
                                            value: UUidGuru,
                                            label: UUidGuru,
                                            selected: true,
                                        },
                                    ],
                                    "value",
                                    "label",
                                    false
                                );
                            } else {
                                console.log(
                                    "Choices.js instance tidak ditemukan"
                                );
                            }
                        } else {
                            console.log(
                                "Select atau Choices.js tidak ditemukan"
                            );
                        }
                    }
                }, 3000);
            } catch (error) {
                console.error("Gagal parsing JSON: ", error);
            }
        }

        // Penanganan untuk /admin/absensi-gurus
        if (
            message.destinationName === "absensi/data" &&
            window.location.pathname === "/admin/absensi-gurus"
        ) {
            try {
                const payload = JSON.parse(message.payloadString);
                const UUidGuru = payload.UUIDguru;
                const action = payload.action;
                const timestamp = payload.timestamp;
                console.log("Menerima pesan MQTT dari absensi/data: ", payload);

                if (window.Livewire) {
                    console.log("Mengirim data absensi ke Livewire...");
                    window.Livewire.dispatch("processAbsensi", {
                        uuid: UUidGuru,
                        action: action,
                        timestamp: timestamp,
                    });
                } else {
                    console.error(
                        "Livewire tidak ditemukan untuk proses absensi"
                    );
                }
            } catch (error) {
                console.error("Gagal parsing JSON dari absensi/data: ", error);
            }
        }
    }
});
