<?php

namespace App\Policies;

use App\Models\Alat;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class AlatPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Alat $alat): bool
    {
        return true;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        $currentUser =  auth()->user();
        return $currentUser->id == 1 ? true : false;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Alat $alat): bool
    {
        $currentUser =  auth()->user();
        return $currentUser->id == 1 ? true : false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Alat $alat): bool
    {
        $currentUser =  auth()->user();
        return $currentUser->id == 1 ? true : false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Alat $alat): bool
    {
        $currentUser =  auth()->user();
        return $currentUser->id == 1 ? true : false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Alat $alat): bool
    {
        $currentUser =  auth()->user();
        return $currentUser->id == 1 ? true : false;
    }
}