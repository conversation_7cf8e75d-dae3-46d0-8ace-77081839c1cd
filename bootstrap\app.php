<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        //
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        // Handle database constraint violations with user-friendly notifications
        $exceptions->render(function (\Illuminate\Database\QueryException $e, \Illuminate\Http\Request $request) {
            // Only handle for web requests (Filament admin)
            if ($request->expectsJson() || !$request->is('admin/*')) {
                return null;
            }

            $friendlyException = \App\Exceptions\DatabaseConstraintException::fromQueryException($e);

            // Show user-friendly notification
            \Filament\Notifications\Notification::make()
                ->title('Operasi Gagal')
                ->body($friendlyException->getMessage())
                ->danger()
                ->persistent()
                ->send();

            // Log the original error for debugging
            \Log::error('Database constraint violation', [
                'error' => $e->getMessage(),
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'user_id' => auth()->id(),
                'url' => $request->url(),
            ]);

            // Redirect back to prevent error page
            return redirect()->back()->withInput();
        });

        // Handle general exceptions for better user experience
        $exceptions->render(function (\Throwable $e, \Illuminate\Http\Request $request) {
            // Only handle for web requests (Filament admin) and not in debug mode
            if ($request->expectsJson() || !$request->is('admin/*') || config('app.debug')) {
                return null;
            }

            // Log the error
            \Log::error('Application error', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'user_id' => auth()->id(),
                'url' => $request->url(),
            ]);

            // Show generic error notification
            \Filament\Notifications\Notification::make()
                ->title('Terjadi Kesalahan')
                ->body('Terjadi kesalahan sistem. Silakan coba lagi atau hubungi administrator.')
                ->danger()
                ->send();

            return redirect()->back();
        });
    })->create();
