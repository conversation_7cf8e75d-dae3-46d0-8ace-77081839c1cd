<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('settings', function (Blueprint $table) {
            $table->boolean('whatsapp_enabled')->default(false)->after('no_Hp');
            $table->string('whatsapp_server_url')->default('http://localhost:3001')->after('whatsapp_enabled');
            $table->text('whatsapp_template_masuk')->nullable()->after('whatsapp_server_url');
            $table->text('whatsapp_template_pulang')->nullable()->after('whatsapp_template_masuk');
            $table->text('whatsapp_template_reminder')->nullable()->after('whatsapp_template_pulang');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('settings', function (Blueprint $table) {
            $table->dropColumn([
                'whatsapp_enabled',
                'whatsapp_server_url',
                'whatsapp_template_masuk',
                'whatsapp_template_pulang',
                'whatsapp_template_reminder'
            ]);
        });
    }
};
