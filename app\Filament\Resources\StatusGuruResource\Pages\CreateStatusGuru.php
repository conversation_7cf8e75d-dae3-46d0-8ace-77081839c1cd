<?php

namespace App\Filament\Resources\StatusGuruResource\Pages;

use App\Filament\Resources\StatusGuruResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateStatusGuru extends CreateRecord
{
    protected static string $resource = StatusGuruResource::class;
        protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}