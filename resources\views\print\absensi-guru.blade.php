<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON><PERSON></title>
    <style>
        @media print {
            @page {
                size: A4 landscape;
                margin: 0.8cm;
            }

            .no-print {
                display: none !important;
            }

            body {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 11px;
            margin: 0;
            padding: 15px;
            background: #f8f9fa;
            color: #333;
            line-height: 1.4;
        }

        .container {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 3px solid #2563eb;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
        }

        .header-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
        }

        .header-logo img {
            max-height: 60px;
            margin-right: 20px;
        }

        .header h1 {
            margin: 0;
            font-size: 22px;
            font-weight: 700;
            color: #1e40af;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .header h2 {
            margin: 8px 0;
            font-size: 18px;
            font-weight: 600;
            color: #374151;
            text-transform: uppercase;
        }

        .header .school-info {
            margin-top: 12px;
            font-size: 12px;
            color: #6b7280;
            font-style: italic;
        }

        .filter-info {
            margin-bottom: 20px;
            padding: 15px;
            background: linear-gradient(135deg, #eff6ff, #dbeafe);
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }

        .filter-info h3 {
            margin: 0 0 12px 0;
            font-size: 14px;
            font-weight: 600;
            color: #1e40af;
        }

        .filter-item {
            display: inline-block;
            margin-right: 25px;
            margin-bottom: 8px;
            padding: 4px 8px;
            background: white;
            border-radius: 4px;
            font-size: 11px;
        }

        .filter-item strong {
            color: #374151;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 25px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        th, td {
            border: 1px solid #e5e7eb;
            padding: 10px 8px;
            text-align: left;
            vertical-align: middle;
        }

        th {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            font-weight: 600;
            text-align: center;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tbody tr:nth-child(even) {
            background-color: #f8fafc;
        }

        tbody tr:hover {
            background-color: #e0f2fe;
        }

        .text-center {
            text-align: center;
        }

        .text-right {
            text-align: right;
        }

        .status-hadir {
            color: #059669;
            font-weight: 600;
            background: #d1fae5;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
        }

        .status-sakit {
            color: #d97706;
            font-weight: 600;
            background: #fef3c7;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
        }

        .status-izin {
            color: #2563eb;
            font-weight: 600;
            background: #dbeafe;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
        }

        .status-alfa {
            color: #dc2626;
            font-weight: 600;
            background: #fee2e2;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
        }

        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: end;
        }

        .print-info {
            font-size: 10px;
            color: #6b7280;
        }

        .signature {
            text-align: center;
            min-width: 200px;
        }

        .signature-box {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            background: #f9fafb;
        }

        .signature-title {
            font-weight: 600;
            margin-bottom: 50px;
            color: #374151;
        }

        .signature-line {
            border-bottom: 2px solid #374151;
            margin-bottom: 8px;
            height: 2px;
        }

        .signature-name {
            font-weight: 600;
            color: #1f2937;
        }

        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .print-button:hover {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }

        .summary {
            margin-bottom: 25px;
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 15px;
        }

        .summary-item {
            text-align: center;
            padding: 15px 10px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }

        .summary-item:hover {
            transform: translateY(-2px);
        }

        .summary-item.total {
            background: linear-gradient(135deg, #6366f1, #4f46e5);
            color: white;
        }

        .summary-item.hadir {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }

        .summary-item.sakit {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
        }

        .summary-item.izin {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
        }

        .summary-item.alfa {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }

        .summary-number {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .summary-label {
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    </style>
</head>
<body>
    <!-- Print Button -->
    <button class="print-button no-print" onclick="window.print()">
        🖨️ Print Laporan
    </button>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-logo">
                @if($setting && $setting->gambar)
                    <img src="{{ asset('storage/' . $setting->gambar) }}" alt="Logo Sekolah">
                @endif
                <div>
                    <h1>{{ $setting->nama_sekolah ?? 'SEKOLAH' }}</h1>
                    <h2>Laporan Absensi Guru</h2>
                </div>
            </div>
            <div class="school-info">
                @if($setting)
                    <div>📍 {{ $setting->alamat }}</div>
                    <div>📧 {{ $setting->email }} | 📱 {{ $setting->no_Hp }}</div>
                @endif
            </div>
        </div>

    <!-- Filter Information -->
    @if(array_filter($filters))
    <div class="filter-info no-print">
        <h3>Filter yang Diterapkan:</h3>
        @if($filters['tanggal_dari'] || $filters['tanggal_sampai'])
            <div class="filter-item">
                <strong>Periode:</strong> 
                {{ $filters['tanggal_dari'] ? \Carbon\Carbon::parse($filters['tanggal_dari'])->format('d/m/Y') : '...' }} 
                s/d 
                {{ $filters['tanggal_sampai'] ? \Carbon\Carbon::parse($filters['tanggal_sampai'])->format('d/m/Y') : '...' }}
            </div>
        @endif
        @if($filters['status'])
            <div class="filter-item">
                <strong>Status:</strong> {{ $filters['status'] }}
            </div>
        @endif
    </div>
    @endif

        <!-- Summary Statistics -->
        <div class="summary">
            <div class="summary-item total">
                <div class="summary-number">{{ $absensiData->count() }}</div>
                <div class="summary-label">Total Record</div>
            </div>
            <div class="summary-item hadir">
                <div class="summary-number">{{ $absensiData->where('status', 'Hadir')->count() }}</div>
                <div class="summary-label">Hadir</div>
            </div>
            <div class="summary-item sakit">
                <div class="summary-number">{{ $absensiData->where('status', 'Sakit')->count() }}</div>
                <div class="summary-label">Sakit</div>
            </div>
            <div class="summary-item izin">
                <div class="summary-number">{{ $absensiData->where('status', 'Izin')->count() }}</div>
                <div class="summary-label">Izin</div>
            </div>
            <div class="summary-item alfa">
                <div class="summary-number">{{ $absensiData->where('status', 'Alfa')->count() }}</div>
                <div class="summary-label">Alfa</div>
            </div>
        </div>

    <!-- Data Table -->
    <table>
        <thead>
            <tr>
                <th style="width: 5%">No</th>
                <th style="width: 20%">Nama Guru</th>
                <th style="width: 15%">Jabatan</th>
                <th style="width: 15%">Divisi</th>
                <th style="width: 10%">Tanggal</th>
                <th style="width: 10%">Hari</th>
                <th style="width: 8%">Jam Masuk</th>
                <th style="width: 8%">Jam Pulang</th>
                <th style="width: 7%">Status</th>
                <th style="width: 7%">Durasi</th>
            </tr>
        </thead>
        <tbody>
            @forelse($absensiData as $index => $absensi)
                @php
                    $tanggal = \Carbon\Carbon::parse($absensi->tanggal);
                    $jamMasuk = $absensi->absensi_masuk ? \Carbon\Carbon::parse($absensi->absensi_masuk)->format('H:i') : '-';
                    $jamPulang = $absensi->absensi_pulang ? \Carbon\Carbon::parse($absensi->absensi_pulang)->format('H:i') : '-';
                    
                    // Hitung durasi kerja
                    $durasi = '-';
                    if ($absensi->absensi_masuk && $absensi->absensi_pulang) {
                        $masuk = \Carbon\Carbon::parse($absensi->absensi_masuk);
                        $pulang = \Carbon\Carbon::parse($absensi->absensi_pulang);
                        $diff = $masuk->diff($pulang);
                        $durasi = $diff->format('%H:%I');
                    }
                @endphp
                <tr>
                    <td class="text-center">{{ $index + 1 }}</td>
                    <td>{{ $absensi->guru->nama_guru }}</td>
                    <td>{{ $absensi->guru->jabatan->nama_jabatan ?? '-' }}</td>
                    <td>{{ $absensi->guru->divisi->nama_divisi ?? '-' }}</td>
                    <td class="text-center">{{ $tanggal->format('d/m/Y') }}</td>
                    <td class="text-center">{{ $tanggal->translatedFormat('l') }}</td>
                    <td class="text-center">{{ $jamMasuk }}</td>
                    <td class="text-center">{{ $jamPulang }}</td>
                    <td class="text-center status-{{ strtolower($absensi->status) }}">{{ $absensi->status }}</td>
                    <td class="text-center">{{ $durasi }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="10" class="text-center">Tidak ada data absensi</td>
                </tr>
            @endforelse
        </tbody>
    </table>

        <!-- Footer -->
        <div class="footer">
            <div class="print-info">
                <div><strong>Dicetak pada:</strong> {{ $printDate }}</div>
                <div><strong>Sistem:</strong> {{ $setting->nama_sekolah ?? 'Sistem Absensi' }}</div>
            </div>
            <div class="signature">
                <div class="signature-box">
                    <div class="signature-title">Kepala Sekolah</div>
                    <div class="signature-line"></div>
                    <div class="signature-name">(...........................)</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
