<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON><PERSON></title>
    <style>
        @media print {
            @page {
                size: A4 landscape;
                margin: 1cm;
            }
            
            .no-print {
                display: none !important;
            }
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }

        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
        }

        .header h2 {
            margin: 5px 0;
            font-size: 16px;
            font-weight: normal;
        }

        .header .school-info {
            margin-top: 10px;
            font-size: 11px;
            color: #666;
        }

        .filter-info {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }

        .filter-info h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
        }

        .filter-item {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 5px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th, td {
            border: 1px solid #333;
            padding: 8px;
            text-align: left;
            vertical-align: middle;
        }

        th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
        }

        .text-center {
            text-align: center;
        }

        .text-right {
            text-align: right;
        }

        .status-hadir { color: #22c55e; font-weight: bold; }
        .status-sakit { color: #f59e0b; font-weight: bold; }
        .status-izin { color: #3b82f6; font-weight: bold; }
        .status-alfa { color: #ef4444; font-weight: bold; }

        .footer {
            margin-top: 30px;
            text-align: right;
        }

        .signature {
            margin-top: 50px;
            text-align: right;
        }

        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .print-button:hover {
            background: #2563eb;
        }

        .summary {
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
        }

        .summary-item {
            text-align: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            min-width: 100px;
        }

        .summary-number {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .summary-label {
            font-size: 11px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <!-- Print Button -->
    <button class="print-button no-print" onclick="window.print()">
        <i>🖨️</i> Print
    </button>

    <!-- Header -->
    <div class="header">
        <h1>{{ $setting->nama_sekolah ?? 'SEKOLAH' }}</h1>
        <h2>LAPORAN ABSENSI GURU</h2>
        <div class="school-info">
            @if($setting)
                <div>{{ $setting->alamat }}</div>
                <div>Email: {{ $setting->email }} | HP: {{ $setting->no_Hp }}</div>
            @endif
        </div>
    </div>

    <!-- Filter Information -->
    @if(array_filter($filters))
    <div class="filter-info no-print">
        <h3>Filter yang Diterapkan:</h3>
        @if($filters['tanggal_dari'] || $filters['tanggal_sampai'])
            <div class="filter-item">
                <strong>Periode:</strong> 
                {{ $filters['tanggal_dari'] ? \Carbon\Carbon::parse($filters['tanggal_dari'])->format('d/m/Y') : '...' }} 
                s/d 
                {{ $filters['tanggal_sampai'] ? \Carbon\Carbon::parse($filters['tanggal_sampai'])->format('d/m/Y') : '...' }}
            </div>
        @endif
        @if($filters['status'])
            <div class="filter-item">
                <strong>Status:</strong> {{ $filters['status'] }}
            </div>
        @endif
    </div>
    @endif

    <!-- Summary Statistics -->
    <div class="summary">
        <div class="summary-item">
            <div class="summary-number">{{ $absensiData->count() }}</div>
            <div class="summary-label">Total Record</div>
        </div>
        <div class="summary-item">
            <div class="summary-number status-hadir">{{ $absensiData->where('status', 'Hadir')->count() }}</div>
            <div class="summary-label">Hadir</div>
        </div>
        <div class="summary-item">
            <div class="summary-number status-sakit">{{ $absensiData->where('status', 'Sakit')->count() }}</div>
            <div class="summary-label">Sakit</div>
        </div>
        <div class="summary-item">
            <div class="summary-number status-izin">{{ $absensiData->where('status', 'Izin')->count() }}</div>
            <div class="summary-label">Izin</div>
        </div>
        <div class="summary-item">
            <div class="summary-number status-alfa">{{ $absensiData->where('status', 'Alfa')->count() }}</div>
            <div class="summary-label">Alfa</div>
        </div>
    </div>

    <!-- Data Table -->
    <table>
        <thead>
            <tr>
                <th style="width: 5%">No</th>
                <th style="width: 20%">Nama Guru</th>
                <th style="width: 15%">Jabatan</th>
                <th style="width: 15%">Divisi</th>
                <th style="width: 10%">Tanggal</th>
                <th style="width: 10%">Hari</th>
                <th style="width: 8%">Jam Masuk</th>
                <th style="width: 8%">Jam Pulang</th>
                <th style="width: 7%">Status</th>
                <th style="width: 7%">Durasi</th>
            </tr>
        </thead>
        <tbody>
            @forelse($absensiData as $index => $absensi)
                @php
                    $tanggal = \Carbon\Carbon::parse($absensi->tanggal);
                    $jamMasuk = $absensi->absensi_masuk ? \Carbon\Carbon::parse($absensi->absensi_masuk)->format('H:i') : '-';
                    $jamPulang = $absensi->absensi_pulang ? \Carbon\Carbon::parse($absensi->absensi_pulang)->format('H:i') : '-';
                    
                    // Hitung durasi kerja
                    $durasi = '-';
                    if ($absensi->absensi_masuk && $absensi->absensi_pulang) {
                        $masuk = \Carbon\Carbon::parse($absensi->absensi_masuk);
                        $pulang = \Carbon\Carbon::parse($absensi->absensi_pulang);
                        $diff = $masuk->diff($pulang);
                        $durasi = $diff->format('%H:%I');
                    }
                @endphp
                <tr>
                    <td class="text-center">{{ $index + 1 }}</td>
                    <td>{{ $absensi->guru->nama_guru }}</td>
                    <td>{{ $absensi->guru->jabatan->nama_jabatan ?? '-' }}</td>
                    <td>{{ $absensi->guru->divisi->nama_divisi ?? '-' }}</td>
                    <td class="text-center">{{ $tanggal->format('d/m/Y') }}</td>
                    <td class="text-center">{{ $tanggal->translatedFormat('l') }}</td>
                    <td class="text-center">{{ $jamMasuk }}</td>
                    <td class="text-center">{{ $jamPulang }}</td>
                    <td class="text-center status-{{ strtolower($absensi->status) }}">{{ $absensi->status }}</td>
                    <td class="text-center">{{ $durasi }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="10" class="text-center">Tidak ada data absensi</td>
                </tr>
            @endforelse
        </tbody>
    </table>

    <!-- Footer -->
    <div class="footer">
        <div>Dicetak pada: {{ $printDate }}</div>
        <div class="signature">
            <div style="margin-top: 60px;">
                <div>_________________________</div>
                <div>Kepala Sekolah</div>
            </div>
        </div>
    </div>

    <script>
        // Auto print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
