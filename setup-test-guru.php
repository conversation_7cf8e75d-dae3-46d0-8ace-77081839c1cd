<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Guru;

echo "=== Setup Test Guru untuk WhatsApp ===\n\n";

// Ambil guru pertama
$guru = Guru::first();

if ($guru) {
    echo "Guru ditemukan: {$guru->nama_guru}\n";
    
    // Update dengan nomor WhatsApp test
    $guru->no_whatsapp = '081234567890'; // Ganti dengan nomor WhatsApp yang valid
    $guru->whatsapp_notification = true;
    $guru->save();
    
    echo "✓ Nomor WhatsApp test ditambahkan: {$guru->no_whatsapp}\n";
    echo "✓ Notifikasi WhatsApp diaktifkan\n";
    
    echo "\nSekarang coba jalankan: php test-whatsapp.php\n";
} else {
    echo "✗ Tidak ada guru ditemukan. <PERSON><PERSON>an tambah guru terlebih dahulu.\n";
}

echo "\n=== Setup Complete ===\n";
