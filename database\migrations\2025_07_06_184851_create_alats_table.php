<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('alats', function (Blueprint $table) {
            $table->id();
            $table->uuid('UUid');
            $table->foreignId('setting_id')->constrained('settings');
            $table->string('nama_alat');
            $table->string('mode_alat');
            $table->string('tipe_pengguna');
            $table->enum('status', ['aktif', 'tidak aktif']);
            $table->enum('timezone', ['WIB', 'WITA', 'WIT']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('alats');
    }
};