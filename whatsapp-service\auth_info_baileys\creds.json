{"noiseKey": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "0CwSgcixW2PUz7PDRlUIcM4liiHqf/j7LKATK6Hir1c="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "whY0vZaQ3g94sPyIjJHdUu4pgTfUsqASkfkfqtoXswU="}}, "pairingEphemeralKeyPair": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "oK2QG1f8EVJgN7CESEvMl+cqyQELP9hTIoL1nW1upVQ="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "UPf+zCXOyQamXA9s8C0gQnCnXMnjOttGNQfflHIMWwU="}}, "signedIdentityKey": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "iIAQQycSmpxvo1nF+LUVqlsSnKhgjKAucrBxYsbQV3Q="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "FQApFi6xufHCF1IEXInfFUOcwylQzE5zOnBWXY7ZEE0="}}, "signedPreKey": {"keyPair": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "iFWNGlR5VqjNAfzCR4T7JLILehuDThTBpmzMTT/7EVI="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "121UG3lip/vW/cNhQWTXf27M06LnSgy86JqAX8JHJwI="}}, "signature": {"type": "<PERSON><PERSON><PERSON>", "data": "sXqDfFTL6HKAiQWXE/7AdeS+G50e4x9ZRL2/jFTfTDMlZCigMcTVUeqs9hZe3IoUIRnY0SXAi0EcMRvPEkvIBg=="}, "keyId": 1}, "registrationId": 73, "advSecretKey": "aYx3/lAxPcp80i+bGnQD7a5R7GvJUZGnDU/1Hdc7Lmc=", "processedHistoryMessages": [], "nextPreKeyId": 9, "firstUnuploadedPreKeyId": 9, "accountSyncCounter": 0, "accountSettings": {"unarchiveChats": false}, "registered": false, "account": {"details": "CJHyh88HEN/n2cMGGAUgACgA", "accountSignatureKey": "iKgaRSJTIZqR+sqsAQof+6n7L5ZD5JUz0ZRSyVbjRwY=", "accountSignature": "8C3Ve9qVv/FCRckM1mJx1a9xPbbe7ydGulJLu42dPuOw2r6Y9pydlnodMkLkgucAX3YVY9U3s0tG2QgpfoYuBQ==", "deviceSignature": "vZ1tUl19KZom0gUy/7uhP6MNN3AUxBZjjNsW0j2lsDeaSvI45vOb3wRAvPH8JLpaLFVXLWeG0b2HstzemxssAQ=="}, "me": {"id": "*************:<EMAIL>", "lid": "***************:64@lid", "name": "<PERSON><PERSON><PERSON>"}, "signalIdentities": [{"identifier": {"name": "*************:<EMAIL>", "deviceId": 0}, "identifierKey": {"type": "<PERSON><PERSON><PERSON>", "data": "BYioGkUiUyGakfrKrAEKH/up+y+WQ+SVM9GUUslW40cG"}}], "platform": "android", "routingInfo": {"type": "<PERSON><PERSON><PERSON>", "data": "CAIIBQ=="}, "lastAccountSyncTimestamp": **********, "lastPropHash": "1K4hH4"}