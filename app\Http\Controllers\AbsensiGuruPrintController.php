<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\AbsensiGuru;
use App\Models\Setting;
use Carbon\Carbon;

class AbsensiGuruPrintController extends Controller
{
    public function print(Request $request)
    {
        $query = AbsensiGuru::with(['guru.jabatan', 'guru.divisi']);

        // Apply filters from request
        if ($request->filled('guru_id')) {
            $query->where('guru_id', $request->guru_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('tanggal_dari')) {
            $query->whereDate('tanggal', '>=', $request->tanggal_dari);
        }

        if ($request->filled('tanggal_sampai')) {
            $query->whereDate('tanggal', '<=', $request->tanggal_sampai);
        }

        $absensiData = $query->orderBy('tanggal', 'desc')
                           ->orderBy('absensi_masuk', 'asc')
                           ->get();

        // Get school settings
        $setting = Setting::getActive();

        $data = [
            'absensiData' => $absensiData,
            'setting' => $setting,
            'filters' => [
                'guru_id' => $request->guru_id,
                'status' => $request->status,
                'tanggal_dari' => $request->tanggal_dari,
                'tanggal_sampai' => $request->tanggal_sampai,
            ],
            'printDate' => Carbon::now()->format('d F Y H:i:s'),
        ];

        return view('print.absensi-guru', $data);
    }

    public function exportCsv(Request $request)
    {
        $query = AbsensiGuru::with(['guru.jabatan', 'guru.divisi']);

        // Apply filters from request
        if ($request->filled('guru_id')) {
            $query->where('guru_id', $request->guru_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('tanggal_dari')) {
            $query->whereDate('tanggal', '>=', $request->tanggal_dari);
        }

        if ($request->filled('tanggal_sampai')) {
            $query->whereDate('tanggal', '<=', $request->tanggal_sampai);
        }

        $absensiData = $query->orderBy('tanggal', 'desc')
                           ->orderBy('absensi_masuk', 'asc')
                           ->get();

        $filename = 'absensi-guru-' . date('Y-m-d-H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($absensiData) {
            $file = fopen('php://output', 'w');

            // Add BOM for UTF-8
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // Header
            fputcsv($file, [
                'No',
                'Nama Guru',
                'Jabatan',
                'Divisi',
                'Tanggal',
                'Hari',
                'Jam Masuk',
                'Jam Pulang',
                'Status',
                'Durasi Kerja'
            ]);

            // Data
            foreach ($absensiData as $index => $absensi) {
                $tanggal = Carbon::parse($absensi->tanggal);
                $jamMasuk = $absensi->absensi_masuk ? Carbon::parse($absensi->absensi_masuk)->format('H:i:s') : '-';
                $jamPulang = $absensi->absensi_pulang ? Carbon::parse($absensi->absensi_pulang)->format('H:i:s') : '-';

                // Hitung durasi kerja
                $durasi = '-';
                if ($absensi->absensi_masuk && $absensi->absensi_pulang) {
                    $masuk = Carbon::parse($absensi->absensi_masuk);
                    $pulang = Carbon::parse($absensi->absensi_pulang);
                    $diff = $masuk->diff($pulang);
                    $durasi = $diff->format('%H:%I:%S');
                }

                fputcsv($file, [
                    $index + 1,
                    $absensi->guru->nama_guru,
                    $absensi->guru->jabatan->nama_jabatan ?? '-',
                    $absensi->guru->divisi->nama_divisi ?? '-',
                    $tanggal->format('d/m/Y'),
                    $tanggal->translatedFormat('l'),
                    $jamMasuk,
                    $jamPulang,
                    $absensi->status,
                    $durasi,
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
