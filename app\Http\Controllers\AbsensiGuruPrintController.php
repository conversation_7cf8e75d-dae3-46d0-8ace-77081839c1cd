<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\AbsensiGuru;
use App\Models\Setting;
use Carbon\Carbon;

class AbsensiGuruPrintController extends Controller
{
    public function print(Request $request)
    {
        $query = AbsensiGuru::with(['guru.jabatan', 'guru.divisi']);

        // Apply filters from request
        if ($request->filled('guru_id')) {
            $query->where('guru_id', $request->guru_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('tanggal_dari')) {
            $query->whereDate('tanggal', '>=', $request->tanggal_dari);
        }

        if ($request->filled('tanggal_sampai')) {
            $query->whereDate('tanggal', '<=', $request->tanggal_sampai);
        }

        $absensiData = $query->orderBy('tanggal', 'desc')
                           ->orderBy('absensi_masuk', 'asc')
                           ->get();

        // Get school settings
        $setting = Setting::getActive();

        $data = [
            'absensiData' => $absensiData,
            'setting' => $setting,
            'filters' => [
                'guru_id' => $request->guru_id,
                'status' => $request->status,
                'tanggal_dari' => $request->tanggal_dari,
                'tanggal_sampai' => $request->tanggal_sampai,
            ],
            'printDate' => Carbon::now()->format('d F Y H:i:s'),
        ];

        return view('print.absensi-guru', $data);
    }
}
