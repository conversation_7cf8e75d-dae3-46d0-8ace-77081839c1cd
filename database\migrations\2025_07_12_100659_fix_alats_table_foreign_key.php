<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('alats', function (Blueprint $table) {
            // Hapus foreign key lama jika ada
            if (Schema::hasColumn('alats', 'settings_id')) {
                $table->dropForeign('alats_sekolah_id_foreign');
                $table->dropColumn('settings_id');
            }

            // Tambahkan kolom setting_id yang baru
            $table->foreignId('setting_id')->after('UUid')->constrained('settings');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
       Schema::table('alats', function (Blueprint $table) {
            // Hapus setting_id
            $table->dropForeign(['setting_id']);
            $table->dropColumn('setting_id');

            // Kembalikan ke settings_id
            $table->foreignId('settings_id')->after('UUid')->constrained('sekolahs');
        });
    }
};