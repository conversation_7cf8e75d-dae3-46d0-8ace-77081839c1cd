{"name": "hookified", "version": "1.10.0", "description": "Event Emitting and Middleware Hooks", "type": "module", "main": "dist/node/index.cjs", "module": "dist/node/index.js", "exports": {".": {"import": "./dist/node/index.js", "require": "./dist/node/index.cjs"}, "./browser": {"import": "./dist/browser/index.js", "default": "./dist/browser/index.global.js"}}, "types": "dist/node/index.d.ts", "scripts": {"test": "xo --fix && vitest run --coverage", "test:ci": "xo && vitest run --coverage", "clean": "rimraf ./dist ./coverage ./site/dist", "build": "rimraf ./dist && tsup", "benchmark": "pnpm benchmark:hooks && pnpm benchmark:emit", "benchmark:hooks": "pnpm tsx benchmark/hook.ts", "benchmark:emit": "pnpm tsx benchmark/emit.ts", "website:build": "docula build", "website:serve": "docula serve", "prepare": "pnpm build"}, "keywords": ["hooks", "eventemitter", "eventemitter3", "middleware", "events", "hooks", "event-hooks", "hook-system", "event-driven", "event-emitter", "hook-handlers", "middleware", "emit", "event-management", "before-after-hooks", "event-listeners", "custom-hooks", "hook-utilities", "typescript-hooks"], "repository": {"type": "git", "url": "git+https://github.com/jaredwray/hookified.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/jaredwray/hookified/issues"}, "homepage": "https://github.com/jaredwray/hookified#readme", "devDependencies": {"@monstermann/tinybench-pretty-printer": "^0.1.0", "@types/node": "^24.0.7", "@vitest/coverage-v8": "^3.2.4", "docula": "^0.13.0", "emittery": "^1.2.0", "eventemitter3": "^5.0.1", "hookable": "^5.5.3", "pino": "^9.7.0", "rimraf": "^6.0.1", "tinybench": "^4.0.1", "tsup": "^8.5.0", "tsx": "^4.20.3", "typescript": "^5.8.3", "vitest": "^3.2.4", "xo": "^1.1.1"}, "files": ["dist", "LICENSE"], "pnpm": {"onlyBuiltDependencies": ["esbuild", "unrs-resolver"]}}