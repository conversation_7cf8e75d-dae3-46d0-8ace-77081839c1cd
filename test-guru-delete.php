<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Models\Guru;
use App\Models\SubKelas;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Testing Guru Delete with Foreign Key Constraints ===\n\n";

// Cek guru yang ada
$gurus = Guru::withTrashed()->get(['id', 'nama_guru', 'deleted_at']);
echo "Available Gurus:\n";
foreach ($gurus as $guru) {
    $status = $guru->deleted_at ? ' (DELETED)' : '';
    echo "ID: {$guru->id} | Name: {$guru->nama_guru}{$status}\n";
}

echo "\n=== Checking Sub Kelas Relations ===\n";
$subKelas = SubKelas::with('guru')->get(['id', 'nama_subkelas', 'Walikelas_id']);
foreach ($subKelas as $sk) {
    $guruName = $sk->guru ? $sk->guru->nama_guru : 'NULL';
    echo "Sub Kelas: {$sk->nama_subkelas} | Wali Kelas ID: {$sk->Walikelas_id} | Guru: {$guruName}\n";
}

echo "\n=== Testing Soft Delete ===\n";
// Test soft delete pada guru yang tidak menjadi wali kelas
$testGuru = Guru::whereDoesntHave('subKelas')->first();

if ($testGuru) {
    echo "Testing soft delete on Guru: {$testGuru->nama_guru} (ID: {$testGuru->id})\n";
    
    try {
        $testGuru->delete(); // Soft delete
        echo "✓ Soft delete successful\n";
        
        // Verify soft delete
        $deletedGuru = Guru::withTrashed()->find($testGuru->id);
        if ($deletedGuru->deleted_at) {
            echo "✓ Guru is soft deleted (deleted_at: {$deletedGuru->deleted_at})\n";
        }
        
        // Test restore
        $deletedGuru->restore();
        echo "✓ Guru restored successfully\n";
        
    } catch (\Exception $e) {
        echo "✗ Error during soft delete: " . $e->getMessage() . "\n";
    }
} else {
    echo "No guru available for testing (all are wali kelas)\n";
}

echo "\n=== Testing Cascade Delete ===\n";
// Test cascade delete - hapus guru yang menjadi wali kelas
$waliKelasGuru = Guru::whereHas('subKelas')->first();

if ($waliKelasGuru) {
    echo "Testing cascade delete on Wali Kelas: {$waliKelasGuru->nama_guru} (ID: {$waliKelasGuru->id})\n";
    
    $subKelasCount = $waliKelasGuru->subKelas()->count();
    echo "This guru is wali kelas for {$subKelasCount} sub kelas\n";
    
    try {
        // Force delete to test cascade
        $waliKelasGuru->forceDelete();
        echo "✓ Force delete successful - cascade should have removed related sub_kelas\n";
        
        // Check if sub_kelas were deleted
        $remainingSubKelas = SubKelas::where('Walikelas_id', $waliKelasGuru->id)->count();
        if ($remainingSubKelas == 0) {
            echo "✓ Cascade delete worked - related sub_kelas were removed\n";
        } else {
            echo "✗ Cascade delete failed - {$remainingSubKelas} sub_kelas still exist\n";
        }
        
    } catch (\Exception $e) {
        echo "✗ Error during force delete: " . $e->getMessage() . "\n";
    }
} else {
    echo "No wali kelas guru available for testing\n";
}

echo "\n=== Final State ===\n";
$finalGurus = Guru::withTrashed()->get(['id', 'nama_guru', 'deleted_at']);
echo "Gurus after test:\n";
foreach ($finalGurus as $guru) {
    $status = $guru->deleted_at ? ' (DELETED)' : '';
    echo "ID: {$guru->id} | Name: {$guru->nama_guru}{$status}\n";
}

$finalSubKelas = SubKelas::with('guru')->get(['id', 'nama_subkelas', 'Walikelas_id']);
echo "\nSub Kelas after test:\n";
foreach ($finalSubKelas as $sk) {
    $guruName = $sk->guru ? $sk->guru->nama_guru : 'NULL';
    echo "Sub Kelas: {$sk->nama_subkelas} | Wali Kelas ID: {$sk->Walikelas_id} | Guru: {$guruName}\n";
}
