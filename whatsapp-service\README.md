# WhatsApp Baileys Service

Service Node.js untuk integrasi WhatsApp dengan <PERSON>vel menggunakan Baileys.

## 🚀 Setup & Installation

### 1. Install Dependencies
```bash
cd whatsapp-service
npm install
```

### 2. Start Service
```bash
# Development mode
npm run dev

# Production mode
npm start
```

### 3. Scan QR Code
- Buka terminal dan scan QR code yang muncul dengan WhatsApp
- Setelah terscan, service akan terhubung otomatis

## 📡 API Endpoints

### Status Check
```
GET http://localhost:3001/status
```
Response:
```json
{
  "status": "connected",
  "connected": true,
  "qr": null,
  "timestamp": "2025-07-15T15:30:00.000Z"
}
```

### Send Message
```
POST http://localhost:3001/send-message
Content-Type: application/json

{
  "number": "08123456789",
  "message": "Hello from Laravel!"
}
```

### Health Check
```
GET http://localhost:3001/health
```

### Logout
```
POST http://localhost:3001/logout
```

## 🔧 Configuration

### Environment Variables
```bash
PORT=3001  # Default port
```

### Laravel Integration
1. Update `.env` Laravel:
```env
WHATSAPP_SERVICE_URL=http://localhost:3001
```

2. Aktifkan WhatsApp di admin panel:
   - Login ke admin panel
   - Buka menu WhatsApp
   - Toggle "Aktifkan WhatsApp" = ON
   - Set URL server: `http://localhost:3001`

## 📱 Features

### ✅ Auto Notifications
- **Absen Masuk**: Notifikasi saat guru absen masuk
- **Absen Pulang**: Notifikasi saat guru absen pulang
- **Reminder**: Pengingat untuk guru yang belum absen

### ✅ Template Messages
- Template pesan dapat dikustomisasi di admin panel
- Support placeholder: `{nama_guru}`, `{nama_sekolah}`, `{tanggal}`, `{waktu}`, `{durasi}`

### ✅ Individual Settings
- Setiap guru dapat mengatur nomor WhatsApp
- Toggle on/off notifikasi per guru

## 🛠️ Troubleshooting

### Service tidak terhubung
1. Pastikan port 3001 tidak digunakan aplikasi lain
2. Restart service: `npm run dev`
3. Scan ulang QR code

### Pesan tidak terkirim
1. Cek status koneksi: `GET /status`
2. Pastikan nomor WhatsApp valid (format: 08xxxxxxxxx)
3. Cek log di terminal service

### QR Code tidak muncul
1. Hapus folder `auth_info_baileys`
2. Restart service
3. QR code akan muncul untuk scan ulang

## 📝 Logs

Service akan membuat log file:
- `wa-logs.txt` - Log aktivitas WhatsApp
- Console output - Real-time status

## 🔒 Security

- Session disimpan di folder `auth_info_baileys`
- Jangan share folder session ke public
- Gunakan HTTPS untuk production

## 🚀 Production Deployment

### Using PM2
```bash
npm install -g pm2
pm2 start index.js --name "whatsapp-service"
pm2 startup
pm2 save
```

### Using Docker
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 3001
CMD ["npm", "start"]
```

## 📞 Support

Jika ada masalah:
1. Cek log di terminal
2. Restart service
3. Pastikan WhatsApp Web tidak login di browser lain
