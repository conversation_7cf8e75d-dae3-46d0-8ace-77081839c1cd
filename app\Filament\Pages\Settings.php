<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Section;
use Filament\Notifications\Notification;
use App\Models\Setting;

class Settings extends Page
{
    protected static string $view = 'filament.pages.settings';
    protected static ?string $navigationGroup = 'Umum';
    protected static ?string $navigationIcon = 'heroicon-o-cog';
    protected static ?string $navigationLabel = 'Pengaturan';
    public $nama_sekolah;
    public $nama_absensi;
    public $mulai_masuk_siswa;
    public $jam_masuk_siswa;
    public $jam_pulang_siswa;
    public $batas_pulang_siswa;
    public $mulai_masuk_guru;
    public $jam_masuk_guru;
    public $jam_keluar_guru;
    public $batas_keluar_guru;
    public $potongan_telat;
    public $jam_mulai_lembur;
    public $jam_akhir_lembur;
    public $durasi_lembur;
    public $nominal_lembur;
    public $no_Hp;
    public $email;
    public $alamat;
    public $hari_libur;
    public $timezone;
    public $gambar;

    protected function getFormSchema(): array
    {
        return [
            TextInput::make('nama_sekolah')
                ->label('Nama Sekolah')
                ->required(),
            TextInput::make('nama_absensi')
                ->label('Nama Absensi')
                ->required(),
            TimePicker::make('mulai_masuk_siswa')
                ->label('Mulai Masuk Siswa'),
            TimePicker::make('jam_masuk_siswa')
                ->label('Jam Masuk Siswa'),
            TimePicker::make('jam_pulang_siswa')
                ->label('Jam Pulang Siswa'),
            TimePicker::make('batas_pulang_siswa')
                ->label('Batas Pulang Siswa'),
            TimePicker::make('mulai_masuk_guru')
                ->label('Mulai Masuk Guru'),
            TimePicker::make('jam_masuk_guru')
                ->label('Jam Masuk Guru'),
            TimePicker::make('jam_keluar_guru')
                ->label('Jam Keluar Guru'),
            TimePicker::make('batas_keluar_guru')
                ->label('Batas Keluar Guru'),
            TextInput::make('potongan_telat')
                ->label('Potongan Telat')
                ->prefix('Rp')
                ->hidden(), // Hidden untuk pengembangan selanjutnya
            TimePicker::make('jam_mulai_lembur')
                ->label('Jam Mulai Lembur'),
            TimePicker::make('jam_akhir_lembur')
                ->label('Jam Akhir Lembur'),
            Select::make('durasi_lembur')
                ->label('Durasi Lembur')
                ->options([
                    '15 Menit' => '15 Menit',
                    '30 Menit' => '30 Menit',
                    '60 Menit' => '60 Menit',
                    '120 Menit' => '120 Menit',
                ]),
            TextInput::make('nominal_lembur')
                ->label('Nominal Lembur')
                ->prefix('Rp')
                ->hidden(), // Hidden untuk pengembangan selanjutnya
            TextInput::make('no_Hp')
                ->label('No. HP')
                ->tel()
                ->prefix('+62'),
            TextInput::make('email')
                ->label('Email')
                ->email(),
            TextInput::make('alamat')
                ->label('Alamat'),
            Select::make('hari_libur')
                ->label('Hari Libur')
                ->options([
                    'Senin' => 'Senin',
                    'Selasa' => 'Selasa',
                    'Rabu' => 'Rabu',
                    'Kamis' => 'Kamis',
                    'Jumat' => 'Jumat',
                    'Sabtu' => 'Sabtu',
                    'Minggu' => 'Minggu',
                ]),
            Select::make('timezone')
                ->label('Timezone')
                ->options([
                    'WIB' => 'WIB',
                    'WITA' => 'WITA',
                    'WIT' => 'WIT',
                ]),
            FileUpload::make('gambar')
                ->label('Logo Sekolah')
                ->disk('public')
                ->directory('settings')
                ->image()
                ->imageEditor()
                ->imageEditorAspectRatios([
                    '1:1',
                    '16:9',
                    '4:3',
                ])
                ->maxSize(2048) // 2MB max
                ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp'])
                ->helperText('Upload logo sekolah (max 2MB). Format: JPG, PNG, GIF, WebP')
                ->columnSpanFull()
                ->deletable(true)
                ->downloadable(true)
                ->openable(true),


        ];
    }
    public function submit()
    {
        try {
            $data = $this->form->getState();

            // Ambil atau buat setting
            $setting = Setting::firstOrNew(['id' => 1]);

            // Update hanya field yang ada di form dan tidak null
            foreach ($data as $key => $value) {
                if ($value !== null && $value !== '') {
                    $setting->$key = $value;
                }
            }

            $setting->save();

            Notification::make()
                ->title('Pengaturan berhasil disimpan!')
                ->success()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Gagal menyimpan pengaturan')
                ->body('Error: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
    public function mount()
    {
        $settings = Setting::first();
        if ($settings) {
        $this->nama_sekolah = $settings->nama_sekolah ?? '';
        $this->nama_absensi = $settings->nama_absensi ?? '';
        $this->mulai_masuk_siswa = $settings->mulai_masuk_siswa ?? '';
        $this->jam_masuk_siswa = $settings->jam_masuk_siswa ?? '';
        $this->jam_pulang_siswa = $settings->jam_pulang_siswa ?? '';
        $this->batas_pulang_siswa = $settings->batas_pulang_siswa ?? '';
        $this->mulai_masuk_guru = $settings->mulai_masuk_guru ?? '';
        $this->jam_masuk_guru = $settings->jam_masuk_guru ?? '';
        $this->jam_keluar_guru = $settings->jam_keluar_guru ?? '';
        $this->batas_keluar_guru = $settings->batas_keluar_guru ?? '';
        $this->potongan_telat = $settings->potongan_telat ?? '';
        $this->jam_mulai_lembur = $settings->jam_mulai_lembur ?? '';
        $this->jam_akhir_lembur = $settings->jam_akhir_lembur ?? '';
        $this->durasi_lembur = $settings->durasi_lembur ?? '';
        $this->nominal_lembur = $settings->nominal_lembur ?? '';
        $this->no_Hp = $settings->no_Hp ?? '';
        $this->email = $settings->email ?? '';
        $this->alamat = $settings->alamat ?? '';
        $this->hari_libur = $settings->hari_libur ?? '';
        $this->timezone = $settings->timezone ?? '';
        $this->gambar = $settings->gambar ?? '';
        }
        $this->form->fill([
        'nama_sekolah' => $this->nama_sekolah,
        'nama_absensi' => $this->nama_absensi,
        'mulai_masuk_siswa' => $this->mulai_masuk_siswa,
        'jam_masuk_siswa' => $this->jam_masuk_siswa,
        'jam_pulang_siswa' => $this->jam_pulang_siswa,
        'batas_pulang_siswa' => $this->batas_pulang_siswa,
        'mulai_masuk_guru' => $this->mulai_masuk_guru,
        'jam_masuk_guru' => $this->jam_masuk_guru,
        'jam_keluar_guru' => $this->jam_keluar_guru,
        'batas_keluar_guru' => $this->batas_keluar_guru,
        'potongan_telat' => $this->potongan_telat,
        'jam_mulai_lembur' => $this->jam_mulai_lembur,
        'jam_akhir_lembur' => $this->jam_akhir_lembur,
        'durasi_lembur' => $this->durasi_lembur,
        'nominal_lembur' => $this->nominal_lembur,
        'no_Hp' => $this->no_Hp,
        'email' => $this->email,
        'alamat' => $this->alamat,
        'hari_libur' => $this->hari_libur,
        'timezone' => $this->timezone,
        'gambar' => $this->gambar,
    ]);
    }
}