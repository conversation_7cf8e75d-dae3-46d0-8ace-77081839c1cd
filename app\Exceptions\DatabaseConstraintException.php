<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Database\QueryException;

class DatabaseConstraintException extends Exception
{
    public static function fromQueryException(QueryException $e): self
    {
        $message = self::getFriendlyMessage($e);
        return new self($message, $e->getCode(), $e);
    }

    private static function getFriendlyMessage(QueryException $e): string
    {
        $errorCode = $e->errorInfo[1] ?? null;
        $sqlMessage = $e->getMessage();

        // Foreign key constraint violations
        if ($errorCode === 1451) {
            if (str_contains($sqlMessage, 'sub_kelas')) {
                return 'Tidak dapat menghapus guru karena masih menjadi wali kelas. Silakan ubah wali kelas terlebih dahulu atau gunakan soft delete.';
            }
            if (str_contains($sqlMessage, 'absensi_gurus')) {
                return 'Tidak dapat menghapus guru karena memiliki data absensi. Gunakan soft delete untuk menyembunyikan data.';
            }
            if (str_contains($sqlMessage, 'siswas')) {
                return 'Tidak dapat menghapus kelas karena masih memiliki siswa. Pindahkan siswa terlebih dahulu.';
            }
            return 'Tidak dapat menghapus data karena masih digunakan oleh data lain. Silakan hapus data terkait terlebih dahulu.';
        }

        // Duplicate entry violations
        if ($errorCode === 1062) {
            if (str_contains($sqlMessage, 'NUPTK')) {
                return 'NUPTK sudah digunakan oleh guru lain. Silakan gunakan NUPTK yang berbeda.';
            }
            if (str_contains($sqlMessage, 'NIS')) {
                return 'NIS sudah digunakan oleh siswa lain. Silakan gunakan NIS yang berbeda.';
            }
            if (str_contains($sqlMessage, 'UUid')) {
                return 'UUID sudah digunakan. Silakan gunakan UUID yang berbeda.';
            }
            return 'Data sudah ada dalam sistem. Silakan gunakan data yang berbeda.';
        }

        // Column not found
        if ($errorCode === 1054) {
            return 'Terjadi kesalahan struktur database. Silakan hubungi administrator sistem.';
        }

        // Table not found
        if ($errorCode === 1146) {
            return 'Tabel database tidak ditemukan. Silakan hubungi administrator sistem.';
        }

        // Data too long
        if ($errorCode === 1406) {
            return 'Data yang dimasukkan terlalu panjang. Silakan kurangi jumlah karakter.';
        }

        // Cannot add or update child row
        if ($errorCode === 1452) {
            return 'Data yang direferensikan tidak ditemukan. Silakan pilih data yang valid.';
        }

        // Default message for other database errors
        return 'Terjadi kesalahan database. Silakan coba lagi atau hubungi administrator sistem.';
    }
}
