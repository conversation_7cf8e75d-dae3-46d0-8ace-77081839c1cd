<?php

namespace App\Filament\Widgets;

use App\Models\AbsensiGuru;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Support\Carbon;

class RecentActivities extends BaseWidget
{
    protected static ?string $heading = 'Aktivitas Absensi Terbaru';
    protected static ?int $sort = 4;
    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                AbsensiGuru::query()
                    ->with(['guru.jabatan', 'guru.divisi'])
                    ->whereDate('tanggal', '>=', Carbon::now()->subDays(3))
                    ->orderBy('updated_at', 'desc')
                    ->limit(10)
            )
            ->columns([
                Tables\Columns\ImageColumn::make('guru.gambar')
                    ->label('Foto')
                    ->circular()
                    ->size(40)
                    ->defaultImageUrl(url('/images/default-avatar.png')),
                    
                Tables\Columns\TextColumn::make('guru.nama_guru')
                    ->label('Nama Guru')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),
                    
                Tables\Columns\TextColumn::make('guru.jabatan.nama_jabatan')
                    ->label('Jabatan')
                    ->badge()
                    ->color('primary'),
                    
                Tables\Columns\TextColumn::make('tanggal')
                    ->label('Tanggal')
                    ->date('d M Y')
                    ->sortable(),
                    
                Tables\Columns\TextColumn::make('absensi_masuk')
                    ->label('Jam Masuk')
                    ->time('H:i')
                    ->badge()
                    ->color(fn ($record) => $record->absensi_masuk ? 'success' : 'gray'),
                    
                Tables\Columns\TextColumn::make('absensi_pulang')
                    ->label('Jam Pulang')
                    ->time('H:i')
                    ->badge()
                    ->color(fn ($record) => $record->absensi_pulang ? 'info' : 'gray'),
                    
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Hadir' => 'success',
                        'Sakit' => 'warning',
                        'Izin' => 'info',
                        'Alfa' => 'danger',
                        default => 'gray',
                    }),
                    
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Terakhir Update')
                    ->since()
                    ->sortable(),
            ])
            ->defaultSort('updated_at', 'desc')
            ->striped()
            ->paginated(false);
    }
}
