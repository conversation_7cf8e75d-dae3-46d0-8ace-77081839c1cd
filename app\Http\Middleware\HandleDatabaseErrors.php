<?php

namespace App\Http\Middleware;

use Closure;
use Throwable;
use Illuminate\Http\Request;
use Illuminate\Database\QueryException;
use Filament\Notifications\Notification;
use App\Exceptions\DatabaseConstraintException;
use Symfony\Component\HttpFoundation\Response;

class HandleDatabaseErrors
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            return $next($request);
        } catch (QueryException $e) {
            // Only handle for Filament admin requests
            if (!$request->is('admin/*')) {
                throw $e;
            }

            $friendlyException = DatabaseConstraintException::fromQueryException($e);
            
            // Show user-friendly notification
            Notification::make()
                ->title('Operasi Gagal')
                ->body($friendlyException->getMessage())
                ->danger()
                ->persistent()
                ->send();

            // Log the original error for debugging
            \Log::error('Database constraint violation in middleware', [
                'error' => $e->getMessage(),
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'user_id' => auth()->id(),
                'url' => $request->url(),
                'method' => $request->method(),
            ]);

            // Redirect back to prevent error page
            return redirect()->back()->withInput();
        } catch (Throwable $e) {
            // Only handle for Filament admin requests and not in debug mode
            if (!$request->is('admin/*') || config('app.debug')) {
                throw $e;
            }

            // Log the error
            \Log::error('Application error in middleware', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'user_id' => auth()->id(),
                'url' => $request->url(),
                'method' => $request->method(),
            ]);

            // Show generic error notification
            Notification::make()
                ->title('Terjadi Kesalahan')
                ->body('Terjadi kesalahan sistem. Silakan coba lagi atau hubungi administrator.')
                ->danger()
                ->send();

            return redirect()->back();
        }
    }
}
