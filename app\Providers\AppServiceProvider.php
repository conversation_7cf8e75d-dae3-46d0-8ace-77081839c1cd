<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;


class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
//
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register observers
        // Temporarily disabled to prevent duplicate absensi records from MQTT
        // \App\Models\AbsensiGuru::observe(\App\Observers\AbsensiGuruObserver::class);
    }
}