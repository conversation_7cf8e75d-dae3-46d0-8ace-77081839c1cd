<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;


class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register WhatsApp services
        $this->app->singleton(\App\Services\WhatsAppService::class);
        $this->app->singleton(\App\Services\WhatsAppProcessService::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register observers
        // Temporarily disabled to prevent duplicate absensi records from MQTT
        // \App\Models\AbsensiGuru::observe(\App\Observers\AbsensiGuruObserver::class);
    }
}