<?php

namespace App\Http\Livewire;

use Livewire\Component;
use App\Models\Guru;
use App\Models\AbsensiGuru;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class AbsensiGuruTable extends Component
{
    public $listeners = ['processAbsensi'];

    public function processAbsensi($data)
    {
        $uuid = $data['uuid'];
        $action = $data['action'];
        $timestamp = $data['timestamp'];

        // Cari guru berdasarkan UUID
        $guru = Guru::where('UUid', $uuid)->first();

        if (!$guru) {
            Log::warning("UUID {$uuid} belum terdaftar di tabel guru.");
            $this->dispatchBrowserEvent('log-message', [
                'message' => "UUID {$uuid} belum terdaftar",
            ]);
            return;
        }

        // Konversi timestamp ke format tanggal dan waktu
        $dateTime = Carbon::createFromTimestamp($timestamp)->setTimezone('Asia/Jakarta');
        $tanggal = $dateTime->toDateString();
        $waktu = $dateTime->toTimeString();

        // Cek apakah sudah ada absensi untuk guru dan tanggal ini
        $absensi = AbsensiGuru::where('guru_id', $guru->id)
            ->where('tanggal', $tanggal)
            ->first();

        if ($action === 'check_in') {
            if ($absensi) {
                // Jika absensi sudah ada, perbarui absensi_masuk jika belum diisi
                if (!$absensi->absensi_masuk) {
                    $absensi->update([
                        'absensi_masuk' => $waktu,
                        'status' => 'Hadir',
                    ]);
                }
            } else {
                // Buat absensi baru untuk check-in
                AbsensiGuru::create([
                    'guru_id' => $guru->id,
                    'tanggal' => $tanggal,
                    'absensi_masuk' => $waktu,
                    'status' => 'Hadir',
                ]);
            }
        } elseif ($action === 'check_out') {
            if ($absensi) {
                // Jika absensi sudah ada, perbarui absensi_pulang
                $absensi->update([
                    'absensi_pulang' => $waktu,
                    'status' => 'Hadir',
                ]);
            } else {
                // Buat absensi baru untuk check-out (jika belum ada check-in)
                AbsensiGuru::create([
                    'guru_id' => $guru->id,
                    'tanggal' => $tanggal,
                    'absensi_pulang' => $waktu,
                    'status' => 'Hadir',
                ]);
            }
        }

        // Emit event untuk memperbarui tabel Filament
        $this->dispatchBrowserEvent('refresh-table');
    }

    public function render()
    {
        return view('livewire.absensi-guru-table');
    }
}