<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('nama_sekolah');
            $table->string('nama_absensi');
            $table->time('mulai_masuk_siswa')->nullable();
            $table->time('jam_masuk_siswa')->nullable();
            $table->time('jam_pulang_siswa')->nullable();
            $table->time('batas_pulang_siswa')->nullable();
            $table->time('mulai_masuk_guru')->nullable();
            $table->time('jam_masuk_guru')->nullable();
            $table->time('jam_keluar_guru')->nullable();
            $table->time('batas_keluar_guru')->nullable();
            $table->string('potongan_telat')->unique();
            $table->time('jam_mulai_lembur')->nullable();
            $table->time('jam_akhir_lembur')->nullable();
            $table->enum('durasi_lembur', ['15 Menit', '30 Menit', '60 Menit', '120 Menit']);
            $table->string('nominal_lembur')->unique();
            $table->string('no_Hp');
            $table->string('email')->unique();
            $table->string('alamat');
            $table->enum('hari_libur', ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu']);
            $table->enum('timezone', ['WIB', 'WITA', 'WIT']);
            $table->string('gambar')->nullable();
            $table->string('pesan_hadir_siswa');
            $table->string('pesan_pulang_siswa'); // Perbaiki typo: 'pualng_siswa' menjadi 'pulang_siswa'
            $table->string('pesan_tidak_absen_siswa'); // Perbaiki penamaan untuk konsistensi
            $table->string('pesan_tidak_absen_pulang_siswa');
            $table->string('pesan_hadir_guru');
            $table->string('pesan_pulang_guru'); // Perbaiki typo: 'pualng_guru' menjadi 'pulang_guru'
            $table->string('pesan_tidak_absen_guru');
            $table->string('pesan_tidak_absen_pulang_guru');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};