<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Models\AbsensiGuru;
use App\Models\Guru;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Cleaning Duplicate Absensi Records ===\n";

$today = date('Y-07-15'); // Target specific date

// Find duplicates
$duplicates = AbsensiGuru::whereDate('tanggal', $today)
    ->selectRaw('guru_id, tanggal, COUNT(*) as count')
    ->groupBy('guru_id', 'tanggal')
    ->having('count', '>', 1)
    ->get();

if ($duplicates->count() > 0) {
    echo "Found " . $duplicates->count() . " duplicate groups\n\n";
    
    foreach ($duplicates as $duplicate) {
        $guru = Guru::find($duplicate->guru_id);
        echo "Processing duplicates for Guru: {$guru->nama_guru} | Date: {$duplicate->tanggal}\n";
        
        // Get all records for this guru and date
        $records = AbsensiGuru::where('guru_id', $duplicate->guru_id)
            ->whereDate('tanggal', $duplicate->tanggal)
            ->orderBy('id')
            ->get();
            
        echo "Found {$records->count()} records:\n";
        foreach ($records as $record) {
            echo "  - ID: {$record->id} | Masuk: {$record->absensi_masuk} | Pulang: {$record->absensi_pulang}\n";
        }
        
        // Keep the first record, delete the rest
        $keepRecord = $records->first();
        $deleteRecords = $records->slice(1);
        
        echo "Keeping record ID: {$keepRecord->id}\n";
        echo "Deleting " . $deleteRecords->count() . " duplicate records: ";
        
        foreach ($deleteRecords as $deleteRecord) {
            echo "{$deleteRecord->id} ";
            $deleteRecord->delete();
        }
        echo "\n\n";
    }
    
    echo "Cleanup completed!\n";
} else {
    echo "No duplicates found.\n";
}

// Show final state
echo "\n=== Final State ===\n";
$finalRecords = AbsensiGuru::whereDate('tanggal', $today)
    ->orderBy('id', 'desc')
    ->get(['id', 'guru_id', 'tanggal', 'absensi_masuk', 'absensi_pulang']);

echo "Total records after cleanup: " . $finalRecords->count() . "\n";
foreach ($finalRecords as $record) {
    $guru = Guru::find($record->guru_id);
    echo "ID: {$record->id} | Guru: {$guru->nama_guru} | Masuk: {$record->absensi_masuk} | Pulang: {$record->absensi_pulang}\n";
}
