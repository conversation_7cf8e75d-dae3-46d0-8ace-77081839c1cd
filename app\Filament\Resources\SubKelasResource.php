<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use App\Models\SubKelas;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Select;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\SubKelasResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\SubKelasResource\RelationManagers;

class SubKelasResource extends Resource
{
    protected static ?string $model = SubKelas::class;
    protected static ?string $navigationLabel = 'Sub Kelas';
    protected static ?string $navigationGroup = 'Data Master';
    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('nama_subkelas')
                ->label('Nama Divisi')
                ->required()
                ->columnSpan(2),
                Select::make('Walikelas_id')
                ->label('Wali Kelas')
                ->relationship( 'guru', 'nama_guru')
                ->searchable()
                ->required()
                ->preload(),
                Select::make('Kelas_id')
                ->label('Kelas')
                ->relationship( 'kelas', 'nama_kelas')
                ->required()
                ->preload()
                ->searchable()
                ->createOptionForm([
                    TextInput::make('nama_kelas')
                    ->label('Nama Kelas')
                    ->required(),
                    Radio::make('status')
                    ->label('Status')
                    ->options([
                        'aktif' => 'Aktif',
                        'tidak aktif' => 'Tidak Aktif',
                    ]),
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('nama_subkelas')
                    ->label('Nama Sub Kelas')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('guru.nama_guru')
                    ->label('Wali Kelas')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('kelas.nama_kelas')
                    ->label('Kelas')
                    ->searchable()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSubKelas::route('/'),
            'create' => Pages\CreateSubKelas::route('/create'),
            'edit' => Pages\EditSubKelas::route('/{record}/edit'),
        ];
    }
}