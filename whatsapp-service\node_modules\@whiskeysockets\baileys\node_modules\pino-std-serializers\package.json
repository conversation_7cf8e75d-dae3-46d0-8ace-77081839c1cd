{"name": "pino-std-serializers", "version": "7.0.0", "description": "A collection of standard object serializers for Pino", "main": "index.js", "type": "commonjs", "types": "index.d.ts", "scripts": {"lint": "standard | snazzy", "lint-ci": "standard", "test": "borp -p 'test/**/*.js'", "test-ci": "borp --coverage -p 'test/**/*.js'", "test-types": "tsc && tsd"}, "repository": {"type": "git", "url": "git+ssh://**************/pinojs/pino-std-serializers.git"}, "keywords": ["pino", "logging"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/pinojs/pino-std-serializers/issues"}, "homepage": "https://github.com/pinojs/pino-std-serializers#readme", "precommit": ["lint", "test", "test-types"], "devDependencies": {"@matteo.collina/tspl": "^0.1.1", "@types/node": "^20.11.17", "borp": "^0.9.1", "pre-commit": "^1.2.2", "snazzy": "^9.0.0", "standard": "^17.1.0", "tsd": "^0.31.0", "typescript": "^5.3.3"}, "tsd": {"directory": "test/types"}}