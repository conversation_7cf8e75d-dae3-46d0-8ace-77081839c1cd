{"name": "whatsapp-baileys-service", "version": "1.0.0", "description": "WhatsApp service using Baileys for Laravel integration", "main": "index.js", "type": "commonjs", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@whiskeysockets/baileys": "^6.6.0", "express": "^4.18.2", "cors": "^2.8.5", "qrcode-terminal": "^0.12.0", "pino": "^8.16.1", "axios": "^1.6.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["whatsapp", "baileys", "laravel", "api"], "author": "Laravel Absensi System", "license": "MIT"}