<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Models\Setting;
use App\Services\WhatsAppProcessService;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Testing Final WhatsApp Toggle Fix ===\n\n";

$processService = app(WhatsAppProcessService::class);

echo "1. Initial State Check:\n";
$setting = Setting::getActive();
$status = $processService->getStatus();

echo "Database: " . ($setting->whatsapp_enabled ? 'ENABLED' : 'DISABLED') . "\n";
echo "Service: " . ($status['process_running'] ? 'RUNNING' : 'STOPPED') . "\n";
echo "Connection: " . ($status['connection_status'] ?? 'unknown') . "\n";

echo "\n2. Testing Service Stop:\n";
$stopResult = $processService->stopService();
echo "Stop Result: " . ($stopResult['success'] ? 'SUCCESS' : 'FAILED') . "\n";

// Update database to match
$setting->whatsapp_enabled = false;
$setting->save();
echo "Database updated to: DISABLED\n";

echo "\n3. Verify Stop State:\n";
sleep(2); // Wait for service to fully stop
$status = $processService->getStatus();
echo "Service Status: " . ($status['process_running'] ? 'RUNNING' : 'STOPPED') . "\n";
echo "Database Status: " . ($setting->whatsapp_enabled ? 'ENABLED' : 'DISABLED') . "\n";

if (!$status['process_running'] && !$setting->whatsapp_enabled) {
    echo "✅ Both service and database show DISABLED\n";
} else {
    echo "❌ Mismatch detected\n";
}

echo "\n4. Simulate Page Refresh (Critical Test):\n";
// This simulates what happens when user refreshes the page
$setting = Setting::getActive();
$status = $processService->getStatus();

echo "After refresh simulation:\n";
echo "Database says: " . ($setting->whatsapp_enabled ? 'ENABLED' : 'DISABLED') . "\n";
echo "Service says: " . ($status['process_running'] ? 'RUNNING' : 'STOPPED') . "\n";

if (!$setting->whatsapp_enabled && !$status['process_running']) {
    echo "✅ PERFECT! Both remain DISABLED after refresh\n";
    echo "✅ No unexpected auto-start will occur\n";
} else {
    echo "❌ Problem: State mismatch after refresh\n";
    if ($setting->whatsapp_enabled && !$status['process_running']) {
        echo "❌ Database says ENABLED but service is STOPPED - would cause auto-start\n";
    }
    if (!$setting->whatsapp_enabled && $status['process_running']) {
        echo "❌ Database says DISABLED but service is RUNNING - inconsistent state\n";
    }
}

echo "\n5. Testing Service Start:\n";
$startResult = $processService->startService();
echo "Start Result: " . ($startResult['success'] ? 'SUCCESS' : 'FAILED') . "\n";

// Update database to match
$setting->whatsapp_enabled = true;
$setting->save();
echo "Database updated to: ENABLED\n";

echo "\n6. Verify Start State:\n";
sleep(3); // Wait for service to fully start
$status = $processService->getStatus();
echo "Service Status: " . ($status['process_running'] ? 'RUNNING' : 'STOPPED') . "\n";
echo "Database Status: " . ($setting->whatsapp_enabled ? 'ENABLED' : 'DISABLED') . "\n";

if ($status['process_running'] && $setting->whatsapp_enabled) {
    echo "✅ Both service and database show ENABLED\n";
} else {
    echo "❌ Mismatch detected\n";
}

echo "\n7. Final Refresh Test:\n";
$setting = Setting::getActive();
$status = $processService->getStatus();

echo "Final state after refresh:\n";
echo "Database: " . ($setting->whatsapp_enabled ? 'ENABLED' : 'DISABLED') . "\n";
echo "Service: " . ($status['process_running'] ? 'RUNNING' : 'STOPPED') . "\n";

if ($setting->whatsapp_enabled === $status['process_running']) {
    echo "✅ SYNCHRONIZED! Database and service match\n";
} else {
    echo "❌ Still not synchronized\n";
}

echo "\n=== Fix Verification ===\n";

$testsPassed = 0;
$totalTests = 3;

// Test 1: Service can be stopped
if (!$status['process_running'] || $stopResult['success']) {
    echo "✅ Test 1: Service can be stopped properly\n";
    $testsPassed++;
} else {
    echo "❌ Test 1: Service stop failed\n";
}

// Test 2: State persists after refresh
$setting = Setting::getActive();
$currentServiceStatus = $processService->getStatus();
if ($setting->whatsapp_enabled === $currentServiceStatus['process_running']) {
    echo "✅ Test 2: State persists correctly after refresh\n";
    $testsPassed++;
} else {
    echo "❌ Test 2: State does not persist after refresh\n";
}

// Test 3: No unexpected auto-start
if (!$setting->whatsapp_enabled || $currentServiceStatus['process_running']) {
    echo "✅ Test 3: No unexpected auto-start behavior\n";
    $testsPassed++;
} else {
    echo "❌ Test 3: Unexpected auto-start detected\n";
}

echo "\n=== Results ===\n";
echo "Tests Passed: {$testsPassed}/{$totalTests}\n";

if ($testsPassed === $totalTests) {
    echo "🎉 ALL TESTS PASSED! Toggle persistence fix is working!\n";
    echo "\n✅ Benefits Achieved:\n";
    echo "• Toggle OFF → Service stops and stays stopped\n";
    echo "• Page refresh → Toggle remains OFF\n";
    echo "• No unexpected service restarts\n";
    echo "• Database and service state synchronized\n";
} else {
    echo "❌ Some tests failed. Toggle persistence needs more work.\n";
}

echo "\n=== Ready for Production ===\n";
echo "You can now safely:\n";
echo "1. Toggle WhatsApp OFF in admin panel\n";
echo "2. Refresh the page\n";
echo "3. Toggle will stay OFF (no auto-restart)\n";
echo "4. Service will remain stopped until manually enabled\n";
