<?php

namespace App\Exports;

use App\Models\AbsensiGuru;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class AbsensiGuruExport implements FromQuery, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $filters;

    public function __construct($filters = [])
    {
        $this->filters = $filters;
    }

    public function query()
    {
        $query = AbsensiGuru::query()->with(['guru.jabatan', 'guru.divisi']);

        // Apply filters
        if (!empty($this->filters['status'])) {
            $query->where('status', $this->filters['status']);
        }

        if (!empty($this->filters['tanggal_dari'])) {
            $query->whereDate('tanggal', '>=', $this->filters['tanggal_dari']);
        }

        if (!empty($this->filters['tanggal_sampai'])) {
            $query->whereDate('tanggal', '<=', $this->filters['tanggal_sampai']);
        }

        if (!empty($this->filters['guru_id'])) {
            $query->where('guru_id', $this->filters['guru_id']);
        }

        return $query->orderBy('tanggal', 'desc')->orderBy('absensi_masuk', 'asc');
    }

    public function headings(): array
    {
        return [
            'No',
            'Nama Guru',
            'Jabatan',
            'Divisi',
            'Tanggal',
            'Hari',
            'Jam Masuk',
            'Jam Pulang',
            'Status',
            'Durasi Kerja',
        ];
    }

    public function map($absensi): array
    {
        static $no = 1;
        
        $tanggal = \Carbon\Carbon::parse($absensi->tanggal);
        $jamMasuk = $absensi->absensi_masuk ? \Carbon\Carbon::parse($absensi->absensi_masuk)->format('H:i:s') : '-';
        $jamPulang = $absensi->absensi_pulang ? \Carbon\Carbon::parse($absensi->absensi_pulang)->format('H:i:s') : '-';
        
        // Hitung durasi kerja
        $durasi = '-';
        if ($absensi->absensi_masuk && $absensi->absensi_pulang) {
            $masuk = \Carbon\Carbon::parse($absensi->absensi_masuk);
            $pulang = \Carbon\Carbon::parse($absensi->absensi_pulang);
            $diff = $masuk->diff($pulang);
            $durasi = $diff->format('%H:%I:%S');
        }

        return [
            $no++,
            $absensi->guru->nama_guru,
            $absensi->guru->jabatan->nama_jabatan ?? '-',
            $absensi->guru->divisi->nama_divisi ?? '-',
            $tanggal->format('d/m/Y'),
            $tanggal->translatedFormat('l'),
            $jamMasuk,
            $jamPulang,
            $absensi->status,
            $durasi,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style header row
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4472C4'],
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => '000000'],
                    ],
                ],
            ],
            // Style all data
            'A:J' => [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => '000000'],
                    ],
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
            // Center align untuk kolom tertentu
            'A:A' => ['alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]], // No
            'E:E' => ['alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]], // Tanggal
            'F:F' => ['alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]], // Hari
            'G:G' => ['alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]], // Jam Masuk
            'H:H' => ['alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]], // Jam Pulang
            'I:I' => ['alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]], // Status
            'J:J' => ['alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]], // Durasi
        ];
    }
}
