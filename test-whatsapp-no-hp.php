<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Models\Guru;
use App\Models\AbsensiGuru;
use App\Services\WhatsAppService;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Testing WhatsApp with No_Hp Integration ===\n\n";

// Check database structure
echo "1. Checking Database Structure:\n";
$guru = Guru::first();
if ($guru) {
    $attributes = $guru->getAttributes();
    echo "✓ Available columns in gurus table:\n";
    foreach (array_keys($attributes) as $column) {
        echo "  - {$column}\n";
    }
    
    if (isset($attributes['no_whatsapp'])) {
        echo "❌ no_whatsapp column still exists (should be removed)\n";
    } else {
        echo "✅ no_whatsapp column successfully removed\n";
    }
    
    if (isset($attributes['No_Hp'])) {
        echo "✅ No_Hp column exists and will be used for WhatsApp\n";
    } else {
        echo "❌ No_Hp column not found\n";
    }
} else {
    echo "❌ No guru found in database\n";
}

echo "\n2. Testing WhatsApp Service Integration:\n";

// Test WhatsApp service
$whatsappService = app(WhatsAppService::class);

// Get a guru with phone number
$testGuru = Guru::whereNotNull('No_Hp')
    ->where('whatsapp_notification', true)
    ->first();

if ($testGuru) {
    echo "✓ Found test guru: {$testGuru->nama_guru}\n";
    echo "✓ Phone number: {$testGuru->No_Hp}\n";
    echo "✓ WhatsApp notification: " . ($testGuru->whatsapp_notification ? 'Enabled' : 'Disabled') . "\n";
    
    // Create a test absensi record
    $testAbsensi = new AbsensiGuru([
        'guru_id' => $testGuru->id,
        'tanggal' => date('Y-m-d'),
        'absensi_masuk' => date('H:i:s'),
        'status' => 'Hadir'
    ]);
    $testAbsensi->guru = $testGuru;
    
    echo "\n3. Testing WhatsApp Message Sending:\n";
    
    // Test sending notification
    $result = $whatsappService->sendAbsensiNotification($testAbsensi, 'masuk');
    
    if ($result['success']) {
        echo "✅ WhatsApp notification sent successfully!\n";
        echo "   Message sent to: {$testGuru->No_Hp}\n";
    } else {
        echo "❌ Failed to send WhatsApp notification\n";
        echo "   Error: {$result['error']}\n";
    }
    
} else {
    echo "❌ No guru found with phone number and WhatsApp notification enabled\n";
}

echo "\n4. Testing Service Status:\n";
$status = $whatsappService->getStatus();
echo "WhatsApp Service Status: " . ($status['connected'] ?? false ? 'Connected' : 'Disconnected') . "\n";

echo "\n5. Summary of Changes:\n";
echo "✅ Removed separate no_whatsapp column\n";
echo "✅ Using No_Hp column for WhatsApp notifications\n";
echo "✅ Updated WhatsApp service to use No_Hp\n";
echo "✅ Updated admin forms to show 'No HP / WhatsApp'\n";
echo "✅ Simplified data entry - no duplicate phone number fields\n";

echo "\n6. Benefits:\n";
echo "• Simplified form - only one phone number field needed\n";
echo "• Reduced data redundancy\n";
echo "• Easier data management\n";
echo "• No need to maintain separate WhatsApp numbers\n";

echo "\n=== Test Complete ===\n";
echo "The system now uses No_Hp for both phone calls and WhatsApp notifications!\n";
