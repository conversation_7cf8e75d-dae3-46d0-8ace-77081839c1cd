<?php

namespace App\Filament\Resources\GuruResource\Pages;

use App\Filament\Resources\GuruResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Livewire\Attributes\On;

class CreateGuru extends CreateRecord
{
    protected static string $resource = GuruResource::class;
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    public $updateUuidGuru = [];
    public $validAlat = false;
    public $UUid = '';

    public function validateUuidAlat($uuidAlat)
    {
        // Validasi ke database
        $alat = \App\Models\Alat::where('UUid', $uuidAlat)->first();
        $this->validAlat = $alat ? true : false;
        return $this->validAlat;
    }

    #[On('updateUuidGuru')]
    public function updateUuidGuru(string $UUidGuru, string $uuidAlat)
    {
        \Log::info("Received updateUuidGuru event with device_id: {$UUidGuru}");
        \Log::info("Current uuidGuru before update: " . json_encode($this->updateUuidGuru));
        if ($this->validateUuidAlat($uuidAlat)) {
            $this->updateUuidGuru[$UUidGuru] = $UUidGuru;
            $this->UUid = $UUidGuru;
            \Log::info("Updated uuidGuru: " . json_encode($this->updateUuidGuru));
        } else {
            \Log::warning("UUID alat tidak valid: $uuidAlat");
        }
        $this->form->fill();
    }

    #[On('refresh-form')] 
    public function refreshForm()
    {
        $this->form->fill();
    }

}