<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Process;
use Symfony\Component\Process\Process as SymfonyProcess;

class WhatsAppProcessService
{
    private $process = null;
    private $isRunning = false;
    private $qrCode = null;
    private $connectionStatus = 'disconnected';

    public function __construct()
    {
        $this->checkExistingProcess();
    }

    /**
     * Start WhatsApp service
     */
    public function startService(): array
    {
        try {
            if ($this->isRunning) {
                return [
                    'success' => true,
                    'message' => 'Service already running',
                    'status' => $this->connectionStatus
                ];
            }

            $whatsappPath = base_path('whatsapp-service');
            
            // Check if whatsapp-service directory exists
            if (!is_dir($whatsappPath)) {
                return [
                    'success' => false,
                    'error' => 'WhatsApp service directory not found'
                ];
            }

            // Start the process in background
            $this->process = new SymfonyProcess(
                ['npm', 'start'],
                $whatsappPath,
                null,
                null,
                null
            );

            $this->process->start();
            $this->isRunning = true;

            Log::info('WhatsApp service started', [
                'pid' => $this->process->getPid(),
                'command' => $this->process->getCommandLine()
            ]);

            // Wait a moment for service to initialize
            sleep(3);

            return [
                'success' => true,
                'message' => 'WhatsApp service started successfully',
                'pid' => $this->process->getPid(),
                'status' => 'starting'
            ];

        } catch (\Exception $e) {
            Log::error('Failed to start WhatsApp service', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Stop WhatsApp service
     */
    public function stopService(): array
    {
        try {
            if ($this->process && $this->process->isRunning()) {
                $this->process->stop();
                Log::info('WhatsApp service stopped');
            }

            // Also kill any existing node processes for whatsapp-service
            $this->killExistingProcesses();

            $this->isRunning = false;
            $this->qrCode = null;
            $this->connectionStatus = 'disconnected';

            return [
                'success' => true,
                'message' => 'WhatsApp service stopped successfully'
            ];

        } catch (\Exception $e) {
            Log::error('Failed to stop WhatsApp service', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get service status and QR code
     */
    public function getStatus(): array
    {
        try {
            // Check if process is still running
            if ($this->process && !$this->process->isRunning()) {
                $this->isRunning = false;
            }

            // Try to get status from WhatsApp API
            $whatsappService = app(WhatsAppService::class);
            $apiStatus = $whatsappService->getStatus();

            if (isset($apiStatus['status'])) {
                $this->connectionStatus = $apiStatus['status'];
                $this->qrCode = $apiStatus['qr'] ?? null;
            }

            return [
                'process_running' => $this->isRunning,
                'connection_status' => $this->connectionStatus,
                'connected' => $apiStatus['connected'] ?? false,
                'qr_code' => $this->qrCode,
                'pid' => $this->process ? $this->process->getPid() : null,
                'timestamp' => now()->toISOString()
            ];

        } catch (\Exception $e) {
            return [
                'process_running' => $this->isRunning,
                'connection_status' => 'error',
                'connected' => false,
                'qr_code' => null,
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString()
            ];
        }
    }

    /**
     * Check if there's already a running process
     */
    private function checkExistingProcess(): void
    {
        try {
            // Check if WhatsApp service is already running on port 3001
            $whatsappService = app(WhatsAppService::class);
            $status = $whatsappService->getStatus();
            
            if (isset($status['connected']) && $status['connected']) {
                $this->isRunning = true;
                $this->connectionStatus = $status['status'] ?? 'connected';
                $this->qrCode = $status['qr'] ?? null;
            }
        } catch (\Exception $e) {
            // Service not running or not accessible
            $this->isRunning = false;
        }
    }

    /**
     * Kill existing WhatsApp service processes
     */
    private function killExistingProcesses(): void
    {
        try {
            if (PHP_OS_FAMILY === 'Windows') {
                // Windows: Kill node processes running on port 3001
                Process::run('taskkill /F /IM node.exe');
            } else {
                // Linux/Mac: Kill processes using port 3001
                Process::run('pkill -f "whatsapp-service"');
                Process::run('fuser -k 3001/tcp');
            }
        } catch (\Exception $e) {
            Log::warning('Failed to kill existing processes', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Restart service
     */
    public function restartService(): array
    {
        $stopResult = $this->stopService();
        sleep(2); // Wait for complete shutdown
        return $this->startService();
    }

    /**
     * Check if service is running
     */
    public function isRunning(): bool
    {
        return $this->isRunning && ($this->process ? $this->process->isRunning() : false);
    }

    /**
     * Get QR code for scanning
     */
    public function getQrCode(): ?string
    {
        return $this->qrCode;
    }
}
