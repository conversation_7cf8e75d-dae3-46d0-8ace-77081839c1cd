<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Process;
use Symfony\Component\Process\Process as SymfonyProcess;

class WhatsAppProcessService
{
    private $process = null;
    private $isRunning = false;
    private $qrCode = null;
    private $connectionStatus = 'disconnected';

    public function __construct()
    {
        $this->checkExistingProcess();
    }

    /**
     * Start WhatsApp service
     */
    public function startService(): array
    {
        try {
            if ($this->isRunning) {
                return [
                    'success' => true,
                    'message' => 'Service already running',
                    'status' => $this->connectionStatus
                ];
            }

            $whatsappPath = base_path('whatsapp-service');
            
            // Check if whatsapp-service directory exists
            if (!is_dir($whatsappPath)) {
                return [
                    'success' => false,
                    'error' => 'WhatsApp service directory not found'
                ];
            }

            // Start the process in background
            $this->process = new SymfonyProcess(
                ['npm', 'start'],
                $whatsappPath,
                null,
                null,
                null
            );

            $this->process->start();
            $this->isRunning = true;

            Log::info('WhatsApp service started', [
                'pid' => $this->process->getPid(),
                'command' => $this->process->getCommandLine()
            ]);

            // Wait a moment for service to initialize
            sleep(3);

            return [
                'success' => true,
                'message' => 'WhatsApp service started successfully',
                'pid' => $this->process->getPid(),
                'status' => 'starting'
            ];

        } catch (\Exception $e) {
            Log::error('Failed to start WhatsApp service', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Stop WhatsApp service
     */
    public function stopService(): array
    {
        try {
            // First, try to stop the process gracefully
            if ($this->process && $this->process->isRunning()) {
                $this->process->stop(3, SIGTERM); // Give 3 seconds to stop gracefully
                Log::info('WhatsApp process stopped gracefully');
            }

            // Force kill any remaining node processes
            $this->killExistingProcesses();

            // Wait a moment for processes to fully terminate
            sleep(1);

            // Reset internal state
            $this->isRunning = false;
            $this->qrCode = null;
            $this->connectionStatus = 'disconnected';
            $this->process = null;

            // Verify service is actually stopped by checking API
            $maxRetries = 3;
            $retries = 0;

            while ($retries < $maxRetries) {
                try {
                    $whatsappService = app(WhatsAppService::class);
                    $status = $whatsappService->getStatus();

                    // If we can still connect to API, service is still running
                    if (isset($status['connected']) || isset($status['status'])) {
                        $retries++;
                        if ($retries < $maxRetries) {
                            sleep(1);
                            $this->killExistingProcesses(); // Try killing again
                            continue;
                        } else {
                            Log::warning('Service may still be running after stop attempts');
                        }
                    } else {
                        // API not accessible, service is stopped
                        break;
                    }
                } catch (\Exception $e) {
                    // Exception means API is not accessible, service is stopped
                    break;
                }
            }

            Log::info('WhatsApp service stop completed');

            return [
                'success' => true,
                'message' => 'WhatsApp service stopped successfully'
            ];

        } catch (\Exception $e) {
            Log::error('Failed to stop WhatsApp service', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get service status and QR code
     */
    public function getStatus(): array
    {
        try {
            // Check if process is still running
            if ($this->process && !$this->process->isRunning()) {
                $this->isRunning = false;
            }

            // Try to get status from WhatsApp API to determine if service is actually running
            $whatsappService = app(WhatsAppService::class);
            $apiStatus = $whatsappService->getStatus();

            // If API is accessible, service is running
            if (isset($apiStatus['status']) || isset($apiStatus['connected'])) {
                $this->isRunning = true;
                $this->connectionStatus = $apiStatus['status'] ?? ($apiStatus['connected'] ? 'connected' : 'disconnected');
                $this->qrCode = $apiStatus['qr'] ?? null;
            } else {
                // API not accessible, service is not running
                $this->isRunning = false;
                $this->connectionStatus = 'disconnected';
                $this->qrCode = null;
            }

            return [
                'process_running' => $this->isRunning,
                'connection_status' => $this->connectionStatus,
                'connected' => $apiStatus['connected'] ?? false,
                'qr_code' => $this->qrCode,
                'pid' => $this->process ? $this->process->getPid() : null,
                'timestamp' => now()->toISOString()
            ];

        } catch (\Exception $e) {
            // If we can't connect to API, service is not running
            $this->isRunning = false;
            $this->connectionStatus = 'disconnected';
            $this->qrCode = null;

            return [
                'process_running' => false,
                'connection_status' => 'disconnected',
                'connected' => false,
                'qr_code' => null,
                'error' => $e->getMessage(),
                'timestamp' => now()->toISOString()
            ];
        }
    }

    /**
     * Check if there's already a running process
     */
    private function checkExistingProcess(): void
    {
        try {
            // Check if WhatsApp service is already running on port 3001
            $whatsappService = app(WhatsAppService::class);
            $status = $whatsappService->getStatus();
            
            if (isset($status['connected']) && $status['connected']) {
                $this->isRunning = true;
                $this->connectionStatus = $status['status'] ?? 'connected';
                $this->qrCode = $status['qr'] ?? null;
            }
        } catch (\Exception $e) {
            // Service not running or not accessible
            $this->isRunning = false;
        }
    }

    /**
     * Kill existing WhatsApp service processes
     */
    private function killExistingProcesses(): void
    {
        try {
            if (PHP_OS_FAMILY === 'Windows') {
                // Windows: Kill node processes more specifically
                // First try to kill by port
                Process::run('netstat -ano | findstr :3001 | for /f "tokens=5" %a in (\'more\') do taskkill /F /PID %a', timeout: 5);

                // Then kill any node.exe processes (more aggressive)
                Process::run('taskkill /F /IM node.exe', timeout: 5);

                // Also try to kill by window title if running in console
                Process::run('taskkill /F /FI "WINDOWTITLE eq *whatsapp-service*"', timeout: 5);

            } else {
                // Linux/Mac: Kill processes using port 3001
                Process::run('pkill -f "whatsapp-service"', timeout: 5);
                Process::run('lsof -ti:3001 | xargs kill -9', timeout: 5);
            }

            Log::info('Attempted to kill existing WhatsApp processes');

        } catch (\Exception $e) {
            Log::warning('Failed to kill existing processes', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Restart service
     */
    public function restartService(): array
    {
        $stopResult = $this->stopService();
        sleep(2); // Wait for complete shutdown
        return $this->startService();
    }

    /**
     * Check if service is running
     */
    public function isRunning(): bool
    {
        return $this->isRunning && ($this->process ? $this->process->isRunning() : false);
    }

    /**
     * Get QR code for scanning
     */
    public function getQrCode(): ?string
    {
        return $this->qrCode;
    }
}
