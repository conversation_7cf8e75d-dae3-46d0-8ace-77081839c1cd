<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Setting extends Model
{
    protected $guarded = [];

    protected $casts = [
        'hari_libur_json' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the active school setting
     */
    public static function getActive()
    {
        return static::where('is_active', true)->first();
    }

    /**
     * Check if today is a holiday
     */
    public function isHolidayToday()
    {
        $today = Carbon::now($this->getTimezone())->translatedFormat('l');

        // Check both old format (single day) and new format (array)
        $holidays = $this->hari_libur_json ?? [];
        if (empty($holidays) && $this->hari_libur) {
            $holidays = [$this->hari_libur];
        }

        return in_array($today, $holidays);
    }

    /**
     * Get timezone string for Carbon
     */
    public function getTimezone()
    {
        $timezones = [
            'WIB' => 'Asia/Jakarta',
            'WITA' => 'Asia/Makassar',
            'WIT' => 'Asia/Jayapura'
        ];

        return $timezones[$this->timezone] ?? 'Asia/Jakarta';
    }

    /**
     * Get current time in school timezone
     */
    public function getCurrentTime()
    {
        return Carbon::now($this->getTimezone());
    }

    /**
     * Check if current time is within work hours
     */
    public function isWorkingHours()
    {
        if ($this->isHolidayToday()) {
            return false;
        }

        $now = $this->getCurrentTime();
        $startTime = Carbon::parse($this->mulai_masuk_guru, $this->getTimezone());
        $endTime = Carbon::parse($this->batas_keluar_guru, $this->getTimezone());

        return $now->between($startTime, $endTime);
    }

    /**
     * Get work status for a given time
     */
    public function getWorkStatus($time)
    {
        $time = Carbon::parse($time, $this->getTimezone());

        $mulaiBatas = Carbon::parse($this->mulai_masuk_guru, $this->getTimezone());
        $jamMasuk = Carbon::parse($this->jam_masuk_guru, $this->getTimezone());
        $jamKeluar = Carbon::parse($this->jam_keluar_guru, $this->getTimezone());
        $batasKeluar = Carbon::parse($this->batas_keluar_guru, $this->getTimezone());
        $mulaiLembur = Carbon::parse($this->jam_mulai_lembur, $this->getTimezone());
        $akhirLembur = Carbon::parse($this->jam_akhir_lembur, $this->getTimezone());

        if ($time->lt($mulaiBatas)) {
            return 'terlalu_awal';
        } elseif ($time->between($mulaiBatas, $jamMasuk)) {
            return 'tepat_waktu';
        } elseif ($time->between($jamMasuk, $jamKeluar)) {
            return 'terlambat';
        } elseif ($time->between($jamKeluar, $batasKeluar)) {
            return 'waktu_pulang';
        } elseif ($time->between($batasKeluar, $mulaiLembur)) {
            return 'lewat_jam_kerja';
        } elseif ($time->between($mulaiLembur, $akhirLembur)) {
            return 'lembur';
        } else {
            return 'lewat_lembur';
        }
    }

    /**
     * Get logo URL
     */
    public function getLogoUrlAttribute()
    {
        return $this->logo_sekolah ? asset('storage/' . $this->logo_sekolah) : null;
    }

    /**
     * Get school image URL
     */
    public function getImageUrlAttribute()
    {
        return $this->gambar ? asset('storage/' . $this->gambar) : null;
    }
}