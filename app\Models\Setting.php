<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Setting extends Model
{
    protected $guarded = [];

    /**
     * Get the first/active school setting
     */
    public static function getActive()
    {
        return static::first(); // Ambil setting pertama
    }

    /**
     * Check if today is a holiday
     * Can be set manually via is_holiday_today field or automatically via hari_libur field
     */
    public function isHolidayToday()
    {
        // Check manual holiday setting first (if field exists)
        if (isset($this->attributes['is_holiday_today'])) {
            return (bool) $this->is_holiday_today;
        }

        // Fallback to automatic check based on hari_libur
        $today = Carbon::now($this->getTimezone())->translatedFormat('l');
        return $today === $this->hari_libur;
    }

    /**
     * Get timezone string for Carbon
     */
    public function getTimezone()
    {
        $timezones = [
            'WIB' => 'Asia/Jakarta',
            'WITA' => 'Asia/Makassar',
            'WIT' => 'Asia/Jayapura'
        ];

        return $timezones[$this->timezone] ?? 'Asia/Jakarta';
    }

    /**
     * Get current time in school timezone
     */
    public function getCurrentTime()
    {
        return Carbon::now($this->getTimezone());
    }

    /**
     * Check if current time is within work hours
     */
    public function isWorkingHours()
    {
        if ($this->isHolidayToday()) {
            return false;
        }

        $now = $this->getCurrentTime();
        $startTime = Carbon::parse($this->mulai_masuk_guru, $this->getTimezone());
        $endTime = Carbon::parse($this->batas_keluar_guru, $this->getTimezone());

        return $now->between($startTime, $endTime);
    }

    /**
     * Get work status for a given time
     */
    public function getWorkStatus($time)
    {
        $time = Carbon::parse($time, $this->getTimezone());

        $mulaiBatas = Carbon::parse($this->mulai_masuk_guru, $this->getTimezone());
        $jamMasuk = Carbon::parse($this->jam_masuk_guru, $this->getTimezone());
        $jamKeluar = Carbon::parse($this->jam_keluar_guru, $this->getTimezone());
        $batasKeluar = Carbon::parse($this->batas_keluar_guru, $this->getTimezone());
        $mulaiLembur = Carbon::parse($this->jam_mulai_lembur, $this->getTimezone());
        $akhirLembur = Carbon::parse($this->jam_akhir_lembur, $this->getTimezone());

        if ($time->lt($mulaiBatas)) {
            return 'terlalu_awal';
        } elseif ($time->between($mulaiBatas, $jamMasuk)) {
            return 'tepat_waktu';
        } elseif ($time->between($jamMasuk, $jamKeluar)) {
            return 'terlambat';
        } elseif ($time->between($jamKeluar, $batasKeluar)) {
            return 'waktu_pulang';
        } elseif ($time->between($batasKeluar, $mulaiLembur)) {
            return 'lewat_jam_kerja';
        } elseif ($time->between($mulaiLembur, $akhirLembur)) {
            return 'lembur';
        } else {
            return 'lewat_lembur';
        }
    }

    /**
     * Get logo URL (using gambar field as logo)
     */
    public function getLogoUrlAttribute()
    {
        return $this->gambar ? asset('storage/' . $this->gambar) : null;
    }
}