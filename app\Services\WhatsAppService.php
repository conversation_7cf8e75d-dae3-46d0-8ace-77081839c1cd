<?php

namespace App\Services;

use App\Models\Setting;
use App\Models\Guru;
use App\Models\AbsensiGuru;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class WhatsAppService
{
    protected $setting;
    protected $serverUrl;

    public function __construct()
    {
        $this->setting = Setting::getActive();
        $this->serverUrl = $this->setting->whatsapp_server_url ?? 'http://localhost:3001';
    }

    /**
     * Check if WhatsApp service is enabled
     */
    public function isEnabled(): bool
    {
        return $this->setting && $this->setting->whatsapp_enabled;
    }

    /**
     * Check WhatsApp server status
     */
    public function getStatus(): array
    {
        try {
            $response = Http::timeout(5)->get($this->serverUrl . '/status');
            
            if ($response->successful()) {
                return $response->json();
            }
            
            return [
                'status' => 'error',
                'connected' => false,
                'error' => 'Server not responding'
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'connected' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Send WhatsApp message
     */
    public function sendMessage(string $number, string $message): array
    {
        if (!$this->isEnabled()) {
            return [
                'success' => false,
                'error' => 'WhatsApp service is disabled'
            ];
        }

        try {
            $response = Http::timeout(10)->post($this->serverUrl . '/send-message', [
                'number' => $number,
                'message' => $message
            ]);

            if ($response->successful()) {
                $data = $response->json();
                Log::info('WhatsApp message sent', [
                    'number' => $number,
                    'message' => $message,
                    'response' => $data
                ]);
                return $data;
            }

            $error = $response->json()['error'] ?? 'Unknown error';
            Log::error('WhatsApp message failed', [
                'number' => $number,
                'error' => $error,
                'status' => $response->status()
            ]);

            return [
                'success' => false,
                'error' => $error
            ];

        } catch (\Exception $e) {
            Log::error('WhatsApp service error', [
                'number' => $number,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Send attendance notification to teacher
     */
    public function sendAbsensiNotification(AbsensiGuru $absensi, string $type): array
    {
        if (!$this->isEnabled()) {
            return ['success' => false, 'error' => 'Service disabled'];
        }

        $guru = $absensi->guru;
        
        // Check if teacher has WhatsApp notification enabled
        if (!$guru->whatsapp_notification || !$guru->No_Hp) {
            return ['success' => false, 'error' => 'Teacher notification disabled or no phone number'];
        }

        $message = $this->buildMessage($absensi, $type);

        return $this->sendMessage($guru->No_Hp, $message);
    }

    /**
     * Build message based on type and template
     */
    protected function buildMessage(AbsensiGuru $absensi, string $type): string
    {
        $guru = $absensi->guru;
        $tanggal = Carbon::parse($absensi->tanggal)->translatedFormat('l, d F Y');
        
        switch ($type) {
            case 'masuk':
                $template = $this->setting->whatsapp_template_masuk ?? $this->getDefaultMasukTemplate();
                $waktu = Carbon::parse($absensi->absensi_masuk)->format('H:i');
                break;
                
            case 'pulang':
                $template = $this->setting->whatsapp_template_pulang ?? $this->getDefaultPulangTemplate();
                $waktu = Carbon::parse($absensi->absensi_pulang)->format('H:i');
                break;
                
            case 'reminder':
                $template = $this->setting->whatsapp_template_reminder ?? $this->getDefaultReminderTemplate();
                $waktu = Carbon::now()->format('H:i');
                break;
                
            default:
                $template = 'Notifikasi absensi untuk {nama_guru}';
                $waktu = Carbon::now()->format('H:i');
        }

        // Calculate work duration for pulang type
        $durasi = '';
        if ($type === 'pulang' && $absensi->absensi_masuk && $absensi->absensi_pulang) {
            $masuk = Carbon::parse($absensi->absensi_masuk);
            $pulang = Carbon::parse($absensi->absensi_pulang);
            $diff = $masuk->diff($pulang);
            $durasi = $diff->format('%H jam %I menit');
        }

        // Replace placeholders
        $message = str_replace([
            '{nama_guru}',
            '{nama_sekolah}',
            '{tanggal}',
            '{waktu}',
            '{durasi}',
            '{jabatan}',
            '{divisi}'
        ], [
            $guru->nama_guru,
            $this->setting->nama_sekolah ?? 'Sekolah',
            $tanggal,
            $waktu,
            $durasi,
            $guru->jabatan->nama_jabatan ?? '',
            $guru->divisi->nama_divisi ?? ''
        ], $template);

        return $message;
    }

    /**
     * Default templates
     */
    protected function getDefaultMasukTemplate(): string
    {
        return "🌅 Selamat pagi {nama_guru}!\n\n" .
               "Absensi masuk Anda telah tercatat pada {waktu}, {tanggal}.\n" .
               "Terima kasih telah hadir tepat waktu.\n\n" .
               "Semoga hari ini menjadi hari yang produktif! 💪\n\n" .
               "Salam,\n{nama_sekolah}";
    }

    protected function getDefaultPulangTemplate(): string
    {
        return "🌆 Selamat sore {nama_guru}!\n\n" .
               "Absensi pulang Anda telah tercatat pada {waktu}, {tanggal}.\n" .
               "Durasi kerja hari ini: {durasi}\n\n" .
               "Terima kasih atas dedikasi dan kerja keras Anda hari ini! 🙏\n" .
               "Selamat beristirahat.\n\n" .
               "Salam,\n{nama_sekolah}";
    }

    protected function getDefaultReminderTemplate(): string
    {
        return "⏰ Pengingat Absensi\n\n" .
               "Halo {nama_guru},\n\n" .
               "Kami belum menerima absensi Anda untuk hari ini ({tanggal}).\n" .
               "Mohon segera lakukan absensi melalui sistem yang tersedia.\n\n" .
               "Jika ada kendala, silakan hubungi admin.\n\n" .
               "Terima kasih,\n{nama_sekolah}";
    }

    /**
     * Send reminder to teachers who haven't checked in
     */
    public function sendReminders(): array
    {
        if (!$this->isEnabled()) {
            return ['success' => false, 'error' => 'Service disabled'];
        }

        $today = Carbon::today();
        $results = [];

        // Get teachers who haven't checked in today
        $absentTeachers = Guru::whereDoesntHave('absensiGuru', function ($query) use ($today) {
            $query->whereDate('tanggal', $today);
        })
        ->where('whatsapp_notification', true)
        ->whereNotNull('No_Hp')
        ->get();

        foreach ($absentTeachers as $guru) {
            // Create a dummy absensi record for message building
            $dummyAbsensi = new AbsensiGuru([
                'guru_id' => $guru->id,
                'tanggal' => $today
            ]);
            $dummyAbsensi->guru = $guru;

            $message = $this->buildMessage($dummyAbsensi, 'reminder');
            $result = $this->sendMessage($guru->No_Hp, $message);

            $results[] = [
                'guru' => $guru->nama_guru,
                'number' => $guru->No_Hp,
                'result' => $result
            ];
        }

        return [
            'success' => true,
            'sent' => count($results),
            'results' => $results
        ];
    }
}
