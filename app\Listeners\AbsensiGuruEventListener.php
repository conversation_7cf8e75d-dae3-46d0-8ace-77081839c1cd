<?php

namespace App\Listeners;

use App\Models\AbsensiGuru;
use App\Services\WhatsAppService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class AbsensiGuruEventListener
{
    protected WhatsAppService $whatsappService;

    public function __construct(WhatsAppService $whatsappService)
    {
        $this->whatsappService = $whatsappService;
    }

    public function created($absensiGuru): void
    {
        if (!$absensiGuru instanceof AbsensiGuru) {
            return;
        }

        Log::info('AbsensiGuruEventListener::created called', [
            'absensi_id' => $absensiGuru->id,
            'guru_id' => $absensiGuru->guru_id,
            'absensi_masuk' => $absensiGuru->absensi_masuk
        ]);

        // Only send notification if absensi_masuk is set (check in)
        if ($absensiGuru->absensi_masuk) {
            Log::info('Sending WhatsApp notification for absensi masuk via EventListener', [
                'absensi_id' => $absensiGuru->id
            ]);
            $this->sendNotification($absensiGuru, 'masuk');
        }
    }

    public function updated($absensiGuru): void
    {
        if (!$absensiGuru instanceof AbsensiGuru) {
            return;
        }

        Log::info('AbsensiGuruEventListener::updated called', [
            'absensi_id' => $absensiGuru->id,
            'guru_id' => $absensiGuru->guru_id,
            'absensi_pulang' => $absensiGuru->absensi_pulang,
            'was_changed' => $absensiGuru->wasChanged('absensi_pulang')
        ]);

        // Check if absensi_pulang was just added (check out)
        if ($absensiGuru->wasChanged('absensi_pulang') && $absensiGuru->absensi_pulang) {
            Log::info('Sending WhatsApp notification for absensi pulang via EventListener', [
                'absensi_id' => $absensiGuru->id
            ]);
            $this->sendNotification($absensiGuru, 'pulang');
        }
    }

    protected function sendNotification(AbsensiGuru $absensiGuru, string $type): void
    {
        try {
            // Check if WhatsApp service is enabled
            if (!$this->whatsappService->isEnabled()) {
                Log::info('WhatsApp notification skipped - service disabled', [
                    'guru_id' => $absensiGuru->guru_id,
                    'type' => $type
                ]);
                return;
            }

            // Send notification
            $result = $this->whatsappService->sendAbsensiNotification($absensiGuru, $type);
            
            if ($result['success']) {
                Log::info('WhatsApp notification sent successfully via EventListener', [
                    'guru_id' => $absensiGuru->guru_id,
                    'type' => $type,
                    'absensi_id' => $absensiGuru->id
                ]);
            } else {
                Log::error('WhatsApp notification failed via EventListener', [
                    'guru_id' => $absensiGuru->guru_id,
                    'type' => $type,
                    'error' => $result['error'],
                    'absensi_id' => $absensiGuru->id
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Exception in WhatsApp notification via EventListener', [
                'guru_id' => $absensiGuru->guru_id,
                'type' => $type,
                'error' => $e->getMessage(),
                'absensi_id' => $absensiGuru->id
            ]);
        }
    }
}
