<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('settings', function (Blueprint $table) {
            // Tambah field logo sekolah
            $table->string('logo_sekolah')->nullable()->after('gambar');

            // Tambah field untuk status aktif/nonaktif
            $table->boolean('is_active')->default(true)->after('logo_sekolah');

            // Tambah field keterangan hari libur
            $table->text('keterangan_libur')->nullable()->after('hari_libur');

            // Tambah field untuk multiple hari libur (JSON format)
            $table->json('hari_libur_json')->nullable()->after('keterangan_libur');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('settings', function (Blueprint $table) {
            // Hapus field yang ditambahkan
            $table->dropColumn(['logo_sekolah', 'is_active', 'keterangan_libur', 'hari_libur_json']);
        });
    }
};
