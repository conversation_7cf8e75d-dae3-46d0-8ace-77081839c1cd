<?php

namespace App\Filament\Resources\AlatResource\Pages;

use App\Filament\Resources\AlatResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Livewire\Attributes\On;

class CreateAlat extends CreateRecord
{
    protected static string $resource = AlatResource::class;
    public $uuidOptions = [];

    #[On('updateUuidOptions')]
    public function updateUuidOptions(string $deviceId)
    {
        \Log::info("Received updateUuidOptions event with device_id: {$deviceId}");
        \Log::info("Current uuidOptions before update: " . json_encode($this->uuidOptions));
        $this->uuidOptions[$deviceId] = $deviceId;
        \Log::info("Updated uuidOptions: " . json_encode($this->uuidOptions));
        $this->dispatch('refresh-form');
    }

    #[On('refresh-form')] 
    public function refreshForm()
    {
        $this->form->fill();
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

}