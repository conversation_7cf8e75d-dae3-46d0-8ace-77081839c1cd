<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Setting;
use App\Models\Guru;
use App\Services\WhatsAppService;

echo "=== WhatsApp System Diagnostic ===\n\n";

// 1. Check Settings
echo "1. Checking Settings...\n";
$setting = Setting::first();
if ($setting) {
    echo "   ✓ Settings found\n";
    echo "   - WhatsApp Enabled: " . ($setting->whatsapp_enabled ? 'YES' : 'NO') . "\n";
    echo "   - Server URL: " . ($setting->whatsapp_server_url ?? 'NULL') . "\n";
    echo "   - Template Masuk: " . (empty($setting->whatsapp_template_masuk) ? 'EMPTY' : 'SET') . "\n";
} else {
    echo "   ✗ No settings found\n";
}

echo "\n";

// 2. Check WhatsApp Service
echo "2. Checking WhatsApp Service...\n";
try {
    $whatsappService = new WhatsAppService();
    echo "   ✓ WhatsApp Service initialized\n";
    echo "   - Service Enabled: " . ($whatsappService->isEnabled() ? 'YES' : 'NO') . "\n";
    
    $status = $whatsappService->getStatus();
    echo "   - Connection Status: " . $status['status'] . "\n";
    echo "   - Connected: " . ($status['connected'] ? 'YES' : 'NO') . "\n";
    
} catch (Exception $e) {
    echo "   ✗ Error: " . $e->getMessage() . "\n";
}

echo "\n";

// 3. Check Guru with WhatsApp
echo "3. Checking Guru with WhatsApp...\n";
$gurusWithWA = Guru::whereNotNull('no_whatsapp')
                   ->where('whatsapp_notification', true)
                   ->get();

echo "   - Guru with WhatsApp: " . $gurusWithWA->count() . "\n";
foreach ($gurusWithWA as $guru) {
    echo "     * {$guru->nama_guru}: {$guru->no_whatsapp}\n";
}

echo "\n";

// 4. Test Send Message (if enabled and connected)
if ($setting && $setting->whatsapp_enabled && !empty($gurusWithWA)) {
    echo "4. Testing Message Send...\n";
    
    $testGuru = $gurusWithWA->first();
    if ($testGuru) {
        echo "   - Testing with: {$testGuru->nama_guru} ({$testGuru->no_whatsapp})\n";
        
        try {
            $result = $whatsappService->sendMessage(
                $testGuru->no_whatsapp, 
                "🧪 Test message from Laravel Absensi System\n\nHalo {$testGuru->nama_guru}, ini adalah pesan test untuk memastikan sistem WhatsApp berfungsi dengan baik.\n\nTerima kasih!"
            );
            
            if ($result['success']) {
                echo "   ✓ Test message sent successfully!\n";
            } else {
                echo "   ✗ Test message failed: " . $result['error'] . "\n";
            }
        } catch (Exception $e) {
            echo "   ✗ Exception: " . $e->getMessage() . "\n";
        }
    }
} else {
    echo "4. Skipping message test (service disabled or no guru with WhatsApp)\n";
}

echo "\n=== Diagnostic Complete ===\n";
