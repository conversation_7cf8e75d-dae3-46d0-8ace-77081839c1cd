<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\AbsensiGuru;
use App\Models\Guru;
use Carbon\Carbon;

class PublicDashboardController extends Controller
{
    public function index()
    {
        return view('public.dashboard');
    }

    public function getAbsensiToday()
    {
        $today = Carbon::today();
        
        $absensiToday = AbsensiGuru::with(['guru.jabatan', 'guru.divisi'])
            ->whereDate('tanggal', $today)
            ->orderBy('absensi_masuk', 'asc')
            ->get()
            ->map(function ($absensi) {
                return [
                    'id' => $absensi->id,
                    'nama_guru' => $absensi->guru->nama_guru,
                    'jabatan' => $absensi->guru->jabatan->nama_jabatan ?? '-',
                    'divisi' => $absensi->guru->divisi->nama_divisi ?? '-',
                    'absensi_masuk' => $absensi->absensi_masuk ? Carbon::parse($absensi->absensi_masuk)->format('H:i') : null,
                    'absensi_pulang' => $absensi->absensi_pulang ? Carbon::parse($absensi->absensi_pulang)->format('H:i') : null,
                    'status' => $absensi->status,
                    'foto' => $absensi->guru->gambar ? asset('storage/' . $absensi->guru->gambar) : null,
                ];
            });

        $stats = [
            'total_guru' => Guru::count(),
            'hadir_hari_ini' => $absensiToday->count(),
            'belum_absen' => Guru::count() - $absensiToday->count(),
            'sudah_pulang' => $absensiToday->whereNotNull('absensi_pulang')->count(),
        ];

        return response()->json([
            'absensi' => $absensiToday,
            'stats' => $stats,
            'tanggal' => $today->format('d F Y'),
            'hari' => $today->translatedFormat('l'),
        ]);
    }

    public function getCurrentTime()
    {
        return response()->json([
            'time' => Carbon::now()->format('H:i:s'),
            'date' => Carbon::now()->format('d F Y'),
            'day' => Carbon::now()->translatedFormat('l'),
        ]);
    }
}
