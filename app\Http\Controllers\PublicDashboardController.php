<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\AbsensiGuru;
use App\Models\Guru;
use App\Models\Setting;
use Carbon\Carbon;

class PublicDashboardController extends Controller
{
    public function index()
    {
        return view('public.dashboard');
    }

    public function getAbsensiToday()
    {
        // Get school settings
        $setting = Setting::getActive();
        if (!$setting) {
            return response()->json(['error' => 'School settings not found'], 404);
        }

        $today = Carbon::today($setting->getTimezone());

        $absensiToday = AbsensiGuru::with(['guru.jabatan', 'guru.divisi'])
            ->whereDate('tanggal', $today)
            ->orderBy('absensi_masuk', 'asc')
            ->get()
            ->map(function ($absensi) use ($setting) {
                $statusMasuk = null;
                $statusPulang = null;

                if ($absensi->absensi_masuk) {
                    $statusMasuk = $setting->getWorkStatus($absensi->absensi_masuk);
                }

                if ($absensi->absensi_pulang) {
                    $statusPulang = $setting->getWorkStatus($absensi->absensi_pulang);
                }

                return [
                    'id' => $absensi->id,
                    'nama_guru' => $absensi->guru->nama_guru,
                    'jabatan' => $absensi->guru->jabatan->nama_jabatan ?? '-',
                    'divisi' => $absensi->guru->divisi->nama_divisi ?? '-',
                    'absensi_masuk' => $absensi->absensi_masuk ? Carbon::parse($absensi->absensi_masuk)->format('H:i') : null,
                    'absensi_pulang' => $absensi->absensi_pulang ? Carbon::parse($absensi->absensi_pulang)->format('H:i') : null,
                    'status' => $absensi->status,
                    'status_masuk' => $statusMasuk,
                    'status_pulang' => $statusPulang,
                    'foto' => $absensi->guru->gambar ? asset('storage/' . $absensi->guru->gambar) : null,
                ];
            });

        // Top 5 Guru Tercepat (yang datang paling awal)
        $top5Tercepat = AbsensiGuru::with(['guru.jabatan', 'guru.divisi'])
            ->whereDate('tanggal', $today)
            ->whereNotNull('absensi_masuk')
            ->orderBy('absensi_masuk', 'asc')
            ->limit(5)
            ->get()
            ->map(function ($absensi, $index) {
                return [
                    'ranking' => $index + 1,
                    'nama_guru' => $absensi->guru->nama_guru,
                    'jabatan' => $absensi->guru->jabatan->nama_jabatan ?? '-',
                    'divisi' => $absensi->guru->divisi->nama_divisi ?? '-',
                    'absensi_masuk' => Carbon::parse($absensi->absensi_masuk)->format('H:i'),
                    'foto' => $absensi->guru->gambar ? asset('storage/' . $absensi->guru->gambar) : null,
                ];
            });

        // Guru yang belum absen hari ini
        $guruHadirIds = AbsensiGuru::whereDate('tanggal', $today)->pluck('guru_id');
        $guruTidakHadir = Guru::with(['jabatan', 'divisi'])
            ->whereNotIn('id', $guruHadirIds)
            ->orderBy('nama_guru', 'asc')
            ->get()
            ->map(function ($guru) {
                return [
                    'id' => $guru->id,
                    'nama_guru' => $guru->nama_guru,
                    'jabatan' => $guru->jabatan->nama_jabatan ?? '-',
                    'divisi' => $guru->divisi->nama_divisi ?? '-',
                    'foto' => $guru->gambar ? asset('storage/' . $guru->gambar) : null,
                ];
            });

        $stats = [
            'total_guru' => Guru::count(),
            'hadir_hari_ini' => $absensiToday->count(),
            'belum_absen' => Guru::count() - $absensiToday->count(),
            'sudah_pulang' => $absensiToday->whereNotNull('absensi_pulang')->count(),
        ];

        return response()->json([
            'absensi' => $absensiToday,
            'top5_tercepat' => $top5Tercepat,
            'guru_tidak_hadir' => $guruTidakHadir,
            'stats' => $stats,
            'tanggal' => $today->format('d F Y'),
            'hari' => $today->translatedFormat('l'),
            'school_info' => [
                'nama_sekolah' => $setting->nama_sekolah,
                'logo_url' => $setting->logo_url,
                'is_holiday' => $setting->isHolidayToday(),
                'keterangan_libur' => $setting->isHolidayToday() ? $setting->keterangan_libur : null,
                'timezone' => $setting->timezone,
                'jam_kerja' => [
                    'mulai_masuk' => $setting->mulai_masuk_guru,
                    'jam_masuk' => $setting->jam_masuk_guru,
                    'jam_keluar' => $setting->jam_keluar_guru,
                    'batas_keluar' => $setting->batas_keluar_guru,
                    'jam_mulai_lembur' => $setting->jam_mulai_lembur,
                    'jam_akhir_lembur' => $setting->jam_akhir_lembur,
                ]
            ]
        ]);
    }


}
