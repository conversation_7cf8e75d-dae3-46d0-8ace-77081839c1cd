<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sub_kelas', function (Blueprint $table) {
            // Drop existing foreign key constraint
            $table->dropForeign(['Walikelas_id']);
            
            // Add new foreign key with cascade delete
            $table->foreign('Walikelas_id')
                  ->references('id')
                  ->on('gurus')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sub_kelas', function (Blueprint $table) {
            // Drop cascade foreign key
            $table->dropForeign(['Walikelas_id']);
            
            // Restore original foreign key without cascade
            $table->foreign('Walikelas_id')
                  ->references('id')
                  ->on('gurus');
        });
    }
};
