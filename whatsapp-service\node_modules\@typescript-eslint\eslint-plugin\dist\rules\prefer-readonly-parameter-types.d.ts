import type { TypeOrValueSpecifier } from '../util';
export type Options = [
    {
        allow?: TypeOrValueSpecifier[];
        checkParameterProperties?: boolean;
        ignoreInferredTypes?: boolean;
        treatMethodsAsReadonly?: boolean;
    }
];
export type MessageIds = 'shouldBeReadonly';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<"shouldBeReadonly", Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=prefer-readonly-parameter-types.d.ts.map