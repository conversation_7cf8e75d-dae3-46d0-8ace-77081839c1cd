<?php

namespace App\Filament\Resources;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Models\AbsensiGuru;
use App\Models\Guru;
use Filament\Resources\Resource;
use App\Exports\AbsensiGuruExport;
use Maatwebsite\Excel\Facades\Excel;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Select;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\TimePicker;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\AbsensiGuruResource\Pages;
use App\Filament\Resources\AbsensiGuruResource\RelationManagers;

class AbsensiGuruResource extends Resource
{
    protected static ?string $model = AbsensiGuru::class;
    protected static ?string $navigationLabel = 'Absensi Guru';
    protected static ?string $navigationGroup = 'Data Guru';
    protected static ?string $navigationIcon = 'heroicon-o-table-cells';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('guru_id')
                ->relationship('guru', 'nama_guru')
                ->label('Nama Guru')
                ->required()
                ->preload()
                ->searchable(),
                DatePicker::make('tanggal')
                ->label('Tanggal')
                ->required(),
                TimePicker::make('absensi_masuk')
                ->label('Absensi Masuk')
                ->required(),
                TimePicker::make('absensi_pulang')
                ->label('Absensi Keluar')
                ->required(),
                Radio::make('status')
                ->label('Status')
                ->options([
                    'Hadir' => 'Hadir',
                    'Sakit' => 'Sakit',
                    'Izin' => 'Izin',
                    'Alfa' => 'Alfa',
                ])
                ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('guru.nama_guru')
                    ->label('Nama Guru')
                    ->sortable()
                    ->searchable(),
                TextColumn::make('tanggal')
                    ->label('Tanggal')
                    ->date('d-m-Y')
                    ->sortable(),
                TextColumn::make('absensi_masuk')
                    ->label('Absensi Masuk')
                    ->formatStateUsing(fn ($state) => $state ? \Carbon\Carbon::parse($state)->format('H:i:s') : '-')
                    ->sortable(),
                TextColumn::make('absensi_pulang')
                    ->label('Absensi Keluar')
                    ->formatStateUsing(fn ($state) => $state ? \Carbon\Carbon::parse($state)->format('H:i:s') : '-')
                    ->sortable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Hadir' => 'success',
                        'Sakit' => 'warning',
                        'Izin' => 'info',
                        'Alfa' => 'danger',
                    })
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('guru_id')
                    ->label('Guru')
                    ->relationship('guru', 'nama_guru')
                    ->searchable()
                    ->preload(),
                SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'Hadir' => 'Hadir',
                        'Sakit' => 'Sakit',
                        'Izin' => 'Izin',
                        'Alfa' => 'Alfa',
                    ]),
                Filter::make('tanggal')
                    ->form([
                        DatePicker::make('tanggal_dari')
                            ->label('Dari Tanggal'),
                        DatePicker::make('tanggal_sampai')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['tanggal_dari'],
                                fn (Builder $query, $date): Builder => $query->whereDate('tanggal', '>=', $date)
                            )
                            ->when(
                                $data['tanggal_sampai'],
                                fn (Builder $query, $date): Builder => $query->whereDate('tanggal', '<=', $date)
                            );
                    }),
            ])
            ->headerActions([
                Tables\Actions\Action::make('export_excel')
                    ->label('Export Excel')
                    ->icon('heroicon-o-document-arrow-down')
                    ->color('success')
                    ->action(function ($livewire) {
                        $tableFilters = $livewire->tableFilters;
                        $filterData = [];

                        // Extract filter values properly
                        if (isset($tableFilters['guru_id']['value'])) {
                            $filterData['guru_id'] = $tableFilters['guru_id']['value'];
                        }

                        if (isset($tableFilters['status']['value'])) {
                            $filterData['status'] = $tableFilters['status']['value'];
                        }

                        if (isset($tableFilters['tanggal']['tanggal_dari'])) {
                            $filterData['tanggal_dari'] = $tableFilters['tanggal']['tanggal_dari'];
                        }

                        if (isset($tableFilters['tanggal']['tanggal_sampai'])) {
                            $filterData['tanggal_sampai'] = $tableFilters['tanggal']['tanggal_sampai'];
                        }

                        $filename = 'absensi-guru-' . date('Y-m-d-H-i-s') . '.xlsx';
                        return Excel::download(new AbsensiGuruExport($filterData), $filename);
                    }),

                Tables\Actions\Action::make('print')
                    ->label('Print')
                    ->icon('heroicon-o-printer')
                    ->color('info')
                    ->action(function ($livewire) {
                        $tableFilters = $livewire->tableFilters;
                        $params = [];

                        // Extract filter values for URL
                        if (isset($tableFilters['guru_id']['value'])) {
                            $params['guru_id'] = $tableFilters['guru_id']['value'];
                        }

                        if (isset($tableFilters['status']['value'])) {
                            $params['status'] = $tableFilters['status']['value'];
                        }

                        if (isset($tableFilters['tanggal']['tanggal_dari'])) {
                            $params['tanggal_dari'] = $tableFilters['tanggal']['tanggal_dari'];
                        }

                        if (isset($tableFilters['tanggal']['tanggal_sampai'])) {
                            $params['tanggal_sampai'] = $tableFilters['tanggal']['tanggal_sampai'];
                        }

                        $url = route('absensi-guru.print', $params);
                        return redirect()->to($url);
                    })
                    ->openUrlInNewTab(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAbsensiGurus::route('/'),
            'create' => Pages\CreateAbsensiGuru::route('/create'),
            'edit' => Pages\EditAbsensiGuru::route('/{record}/edit'),
        ];
    }
}