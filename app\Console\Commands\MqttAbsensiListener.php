<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use PhpMqtt\Client\MqttClient;
use PhpMqtt\Client\ConnectionSettings;
use App\Models\Alat;
use App\Models\Guru;
use App\Models\AbsensiGuru;

class MqttAbsensiListener extends Command
{
    protected $signature = 'mqtt:listen-absensi';
    protected $description = 'Listen to MQTT absensi/data topic and process absensi automatically';

    public function handle()
    {
        \Log::info('MQTT listener started');
        $host = 'ffe180c2d6054ac78559eccdc9597e4f.s1.eu.hivemq.cloud';
        $port = 8883;
        $clientId = 'laravel_mqtt_backend_' . time();
        $username = 'absensi-sekolah';
        $password = 'Acekolah123';

        $settings = (new ConnectionSettings)
            ->setUsername($username)
            ->setPassword($password)
            ->setUseTls(true);

        try {
            $mqtt = new MqttClient($host, $port, $clientId);
            $mqtt->connect($settings, true);
            \Log::info('MQTT connected to broker');

            $mqtt->subscribe('absensi/data', function (string $topic, string $message) {
            \Log::info('MQTT message received', ['topic' => $topic, 'message' => $message]);
            $data = json_decode($message, true);
            if (!$data || !isset($data['device_id'], $data['UUIDguru'], $data['timestamp'])) {
                \Log::warning('MQTT absensi/data: Invalid payload', ['message' => $message]);
                return;
            }

            $alat = Alat::where('UUid', $data['device_id'])->first();
            if (!$alat) {
                \Log::warning('MQTT absensi/data: Unknown device_id', ['device_id' => $data['device_id']]);
                return;
            }

            $guru = Guru::where('UUid', $data['UUIDguru'])->first();
            if (!$guru) {
                \Log::warning('MQTT absensi/data: Unknown UUIDguru', ['UUIDguru' => $data['UUIDguru']]);
                return;
            }

            try {
                $tanggal = date('Y-m-d', $data['timestamp']);
                $jam = date('H:i:s', $data['timestamp']);
                $absensi = AbsensiGuru::where('guru_id', $guru->id)
                    ->where('tanggal', $tanggal)
                    ->first();

                if (!$absensi) {
                    // Tap pertama hari ini: absensi masuk
                    $absensi = AbsensiGuru::create([
                        'guru_id' => $guru->id,
                        'alat_id' => $alat->id,
                        'tanggal' => $tanggal,
                        'absensi_masuk' => $jam,
                        'status' => 'Hadir',
                    ]);
                    \Log::info('Absensi masuk otomatis berhasil', [
                        'guru_id' => $guru->id,
                        'alat_id' => $alat->id,
                        'absensi_id' => $absensi->id,
                        'tanggal' => $tanggal,
                        'absensi_masuk' => $jam,
                        'data' => $data
                    ]);
                } else if ($absensi && !$absensi->absensi_pulang) {
                    // Tap kedua hari ini: absensi pulang
                    $absensi->absensi_pulang = $jam;
                    $absensi->save();
                    \Log::info('Absensi pulang otomatis berhasil', [
                        'guru_id' => $guru->id,
                        'alat_id' => $alat->id,
                        'absensi_id' => $absensi->id,
                        'tanggal' => $tanggal,
                        'absensi_pulang' => $jam,
                        'data' => $data
                    ]);
                } else {
                    \Log::info('Absensi sudah lengkap hari ini', [
                        'guru_id' => $guru->id,
                        'alat_id' => $alat->id,
                        'absensi_id' => $absensi->id,
                        'tanggal' => $tanggal,
                        'data' => $data
                    ]);
                }
            } catch (\Throwable $e) {
                \Log::error('AbsensiGuru create error', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'data' => $data
                ]);
            }
            }, 0);

            $mqtt->loop(true);
        } catch (\Throwable $e) {
            \Log::error('MQTT connection or runtime error', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
            ]);
            echo "MQTT ERROR: " . $e->getMessage() . "\n";
        }
    }
}