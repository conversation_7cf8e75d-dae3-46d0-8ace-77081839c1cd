<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use PhpMqtt\Client\MqttClient;
use PhpMqtt\Client\ConnectionSettings;
use App\Models\Alat;
use App\Models\Guru;
use App\Models\AbsensiGuru;
use App\Services\WhatsAppService;

class MqttAbsensiListener extends Command
{
    protected $signature = 'mqtt:listen-absensi';
    protected $description = 'Listen to MQTT absensi/data topic and process absensi automatically';

    // Cache untuk mencegah duplikasi pesan
    private $processedMessages = [];

    public function handle()
    {
        \Log::info('MQTT listener started');
        $host = 'ffe180c2d6054ac78559eccdc9597e4f.s1.eu.hivemq.cloud';
        $port = 8883;
        $clientId = 'laravel_mqtt_backend_' . time();
        $username = 'absensi-sekolah';
        $password = 'Acekolah123';

        $settings = (new ConnectionSettings)
            ->setUsername($username)
            ->setPassword($password)
            ->setUseTls(true);

        try {
            $mqtt = new MqttClient($host, $port, $clientId);
            $mqtt->connect($settings, true);
            \Log::info('MQTT connected to broker');

            $mqtt->subscribe('absensi/data', function (string $topic, string $message) {
            \Log::info('MQTT message received', ['topic' => $topic, 'message' => $message]);
            $data = json_decode($message, true);
            if (!$data || !isset($data['device_id'], $data['UUIDguru'], $data['timestamp'])) {
                \Log::warning('MQTT absensi/data: Invalid payload', ['message' => $message]);
                return;
            }

            // Cek duplikasi pesan berdasarkan UUID guru + timestamp
            $messageKey = $data['UUIDguru'] . '_' . $data['timestamp'];
            if (in_array($messageKey, $this->processedMessages)) {
                \Log::info('Duplicate MQTT message ignored', [
                    'message_key' => $messageKey,
                    'data' => $data
                ]);
                return;
            }

            // Tambahkan ke cache (batasi ukuran cache)
            $this->processedMessages[] = $messageKey;
            if (count($this->processedMessages) > 100) {
                array_shift($this->processedMessages); // Hapus yang paling lama
            }

            $alat = Alat::where('UUid', $data['device_id'])->first();
            if (!$alat) {
                \Log::warning('MQTT absensi/data: Unknown device_id', ['device_id' => $data['device_id']]);
                return;
            }

            $guru = Guru::where('UUid', $data['UUIDguru'])->first();
            if (!$guru) {
                \Log::warning('MQTT absensi/data: Unknown UUIDguru', ['UUIDguru' => $data['UUIDguru']]);
                return;
            }

            try {
                $tanggal = date('Y-m-d', $data['timestamp']);
                $jam = date('H:i:s', $data['timestamp']);
                $absensi = AbsensiGuru::where('guru_id', $guru->id)
                    ->where('tanggal', $tanggal)
                    ->first();

                if (!$absensi) {
                    // Tap pertama hari ini: absensi masuk
                    // Nonaktifkan Observer sementara untuk mencegah duplikasi
                    $absensi = AbsensiGuru::withoutEvents(function () use ($guru, $alat, $tanggal, $jam) {
                        return AbsensiGuru::create([
                            'guru_id' => $guru->id,
                            'alat_id' => $alat->id,
                            'tanggal' => $tanggal,
                            'absensi_masuk' => $jam,
                            'status' => 'Hadir',
                        ]);
                    });
                    \Log::info('Absensi masuk otomatis berhasil', [
                        'guru_id' => $guru->id,
                        'alat_id' => $alat->id,
                        'absensi_id' => $absensi->id,
                        'tanggal' => $tanggal,
                        'absensi_masuk' => $jam,
                        'data' => $data
                    ]);

                    // Send WhatsApp notification
                    $this->sendWhatsAppNotification($absensi, 'masuk');
                } else if ($absensi && !$absensi->absensi_pulang) {
                    // Tap kedua hari ini: absensi pulang
                    // Nonaktifkan Observer sementara untuk mencegah duplikasi
                    AbsensiGuru::withoutEvents(function () use ($absensi, $jam) {
                        $absensi->absensi_pulang = $jam;
                        $absensi->save();
                    });
                    \Log::info('Absensi pulang otomatis berhasil', [
                        'guru_id' => $guru->id,
                        'alat_id' => $alat->id,
                        'absensi_id' => $absensi->id,
                        'tanggal' => $tanggal,
                        'absensi_pulang' => $jam,
                        'data' => $data
                    ]);

                    // Send WhatsApp notification
                    $this->sendWhatsAppNotification($absensi, 'pulang');
                } else {
                    \Log::info('Absensi sudah lengkap hari ini', [
                        'guru_id' => $guru->id,
                        'alat_id' => $alat->id,
                        'absensi_id' => $absensi->id,
                        'tanggal' => $tanggal,
                        'data' => $data
                    ]);
                }
            } catch (\Throwable $e) {
                \Log::error('AbsensiGuru create error', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'data' => $data
                ]);
            }
            }, 0);

            $mqtt->loop(true);
        } catch (\Throwable $e) {
            \Log::error('MQTT connection or runtime error', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
            ]);
            echo "MQTT ERROR: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Send WhatsApp notification for absensi
     */
    private function sendWhatsAppNotification(AbsensiGuru $absensi, string $type): void
    {
        try {
            $whatsappService = app(WhatsAppService::class);

            if (!$whatsappService->isEnabled()) {
                \Log::info('WhatsApp notification skipped - service disabled', [
                    'absensi_id' => $absensi->id,
                    'type' => $type
                ]);
                return;
            }

            $result = $whatsappService->sendAbsensiNotification($absensi, $type);

            if ($result['success']) {
                \Log::info('WhatsApp notification sent successfully via MQTT', [
                    'absensi_id' => $absensi->id,
                    'guru_id' => $absensi->guru_id,
                    'type' => $type
                ]);
            } else {
                \Log::error('WhatsApp notification failed via MQTT', [
                    'absensi_id' => $absensi->id,
                    'guru_id' => $absensi->guru_id,
                    'type' => $type,
                    'error' => $result['error']
                ]);
            }
        } catch (\Exception $e) {
            \Log::error('Exception in WhatsApp notification via MQTT', [
                'absensi_id' => $absensi->id,
                'guru_id' => $absensi->guru_id,
                'type' => $type,
                'error' => $e->getMessage()
            ]);
        }
    }
}