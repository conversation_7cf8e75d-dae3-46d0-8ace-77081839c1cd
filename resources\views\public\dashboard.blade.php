<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Absensi - Monitor Umum</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .clock-text {
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .status-badge {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .scroll-container {
            max-height: calc(100vh - 200px);
            overflow-y: auto;
        }
        
        .scroll-container::-webkit-scrollbar {
            width: 8px;
        }
        
        .scroll-container::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
        }
        
        .scroll-container::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
        }
        
        .scroll-container::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.5);
        }

        .trophy-gold { color: #FFD700; }
        .trophy-silver { color: #C0C0C0; }
        .trophy-bronze { color: #CD7F32; }

        .ranking-badge {
            background: linear-gradient(45deg, #667eea, #764ba2);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 10px rgba(102, 126, 234, 0.5); }
            to { box-shadow: 0 0 20px rgba(118, 75, 162, 0.8); }
        }

        .absent-card {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .top-performer-card {
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(22, 163, 74, 0.1));
            border: 1px solid rgba(34, 197, 94, 0.3);
        }

        .status-tepat-waktu { background: rgba(34, 197, 94, 0.8); color: white; }
        .status-terlambat { background: rgba(239, 68, 68, 0.8); color: white; }
        .status-lembur { background: rgba(168, 85, 247, 0.8); color: white; }
        .status-waktu-pulang { background: rgba(59, 130, 246, 0.8); color: white; }
        .status-terlalu-awal { background: rgba(156, 163, 175, 0.8); color: white; }
        .status-lewat-jam-kerja { background: rgba(245, 158, 11, 0.8); color: white; }
        .status-lewat-lembur { background: rgba(220, 38, 38, 0.8); color: white; }
    </style>
</head>
<body class="min-h-screen">
    <div class="container mx-auto px-4 py-4">
        <!-- Header Section -->
        <div class="text-center mb-6">
            <!-- Logo dan Nama Sekolah -->
            <div class="flex items-center justify-center mb-4">
                <div id="school-logo" class="mr-4" style="display: none;">
                    <img id="logo-image" src="" alt="Logo Sekolah" class="h-16 w-16 object-contain">
                </div>
                <div>
                    <h1 id="school-name" class="text-3xl md:text-4xl font-bold text-white mb-1 clock-text">
                        DASHBOARD ABSENSI
                    </h1>
                    <div id="holiday-notice" class="bg-red-500/80 backdrop-blur-sm rounded-lg px-4 py-2 mt-2" style="display: none;">
                        <div class="flex items-center justify-center text-white">
                            <i class="fas fa-calendar-times mr-2"></i>
                            <span class="font-semibold">HARI LIBUR</span>
                            <span id="holiday-description" class="ml-2 text-sm"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Clock and Date -->
            <div class="bg-white/20 backdrop-blur-lg rounded-xl p-4 mb-4 border border-white/30">
                <div class="text-center">
                    <div id="current-time" class="text-3xl md:text-5xl font-bold text-white mb-1 clock-text">
                        --:--:--
                    </div>
                    <div id="current-date" class="text-lg md:text-xl text-white/90 mb-2">
                        <span id="current-day">--</span>, <span id="current-full-date">-- -- ----</span>
                    </div>
                    <!-- Status Jam Kerja -->
                    <div id="work-status" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium" style="display: none;">
                        <i id="work-status-icon" class="mr-2"></i>
                        <span id="work-status-text">--</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top 5 Guru Tercepat & Guru Tidak Hadir -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
            <!-- Top 5 Guru Tercepat -->
            <div class="bg-white/10 backdrop-blur-lg rounded-xl p-4 border border-white/30 top-performer-card">
                <h2 class="text-lg font-bold text-white mb-3 flex items-center">
                    <i class="fas fa-trophy text-yellow-400 mr-2"></i>
                    Top 5 Guru Tercepat Hari Ini
                </h2>

                <div id="top5-container" class="space-y-2">
                    <div id="top5-loading" class="text-center py-3">
                        <i class="fas fa-spinner fa-spin text-white/60"></i>
                        <p class="text-white/80 text-sm mt-1">Memuat data...</p>
                    </div>

                    <div id="top5-list" style="display: none;">
                        <!-- Data akan dimuat via JavaScript -->
                    </div>

                    <div id="top5-empty" class="text-center py-3" style="display: none;">
                        <i class="fas fa-medal text-white/60 text-xl mb-1"></i>
                        <p class="text-white/80 text-sm">Belum ada data absensi</p>
                    </div>
                </div>
            </div>

            <!-- Guru Tidak Hadir -->
            <div class="bg-white/10 backdrop-blur-lg rounded-xl p-4 border border-white/30 absent-card">
                <h2 class="text-lg font-bold text-white mb-3 flex items-center">
                    <i class="fas fa-user-times text-red-400 mr-2"></i>
                    Guru Belum Absen
                    <span id="absent-count" class="ml-auto text-sm bg-red-500/30 px-2 py-1 rounded-full">0</span>
                </h2>

                <div id="absent-container" class="scroll-container max-h-64">
                    <div id="absent-loading" class="text-center py-3">
                        <i class="fas fa-spinner fa-spin text-white/60"></i>
                        <p class="text-white/80 text-sm mt-1">Memuat data...</p>
                    </div>

                    <div id="absent-list" class="space-y-2" style="display: none;">
                        <!-- Data akan dimuat via JavaScript -->
                    </div>

                    <div id="absent-empty" class="text-center py-3" style="display: none;">
                        <i class="fas fa-check-circle text-green-400 text-xl mb-1"></i>
                        <p class="text-white/80 text-sm">Semua guru sudah absen!</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Absensi List -->
        <div class="bg-white/10 backdrop-blur-lg rounded-xl p-4 border border-white/30">
            <h2 class="text-lg font-bold text-white mb-4 flex items-center">
                <i class="fas fa-list-alt mr-2"></i>
                Daftar Absensi Hari Ini
                <span class="ml-auto text-sm font-normal">
                    <i class="fas fa-sync-alt animate-spin" id="loading-icon" style="display: none;"></i>
                    Terakhir update: <span id="last-update">--:--:--</span>
                </span>
            </h2>

            <div id="absensi-container" class="scroll-container">
                <div id="loading-state" class="text-center py-6">
                    <i class="fas fa-spinner fa-spin text-2xl text-white/60 mb-3"></i>
                    <p class="text-white/80">Memuat data absensi...</p>
                </div>

                <div id="absensi-list" class="space-y-2" style="display: none;">
                    <!-- Data akan dimuat via JavaScript -->
                </div>

                <div id="empty-state" class="text-center py-6" style="display: none;">
                    <i class="fas fa-calendar-times text-2xl text-white/60 mb-3"></i>
                    <p class="text-white/80">Belum ada data absensi hari ini</p>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Configuration
        const CONFIG = {
            UPDATE_INTERVAL: 30000, // 30 seconds
            CLOCK_INTERVAL: 1000,   // 1 second
        };

        // State
        let lastUpdateTime = null;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateClock();
            loadAbsensiData();
            
            // Set intervals
            setInterval(updateClock, CONFIG.CLOCK_INTERVAL);
            setInterval(loadAbsensiData, CONFIG.UPDATE_INTERVAL);
        });

        // Update clock
        function updateClock() {
            const now = new Date();

            // Format waktu HH:MM:SS
            const timeString = now.toLocaleTimeString('id-ID', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false,
                timeZone: 'Asia/Jakarta'
            });

            // Format hari
            const dayString = now.toLocaleDateString('id-ID', {
                weekday: 'long',
                timeZone: 'Asia/Jakarta'
            });

            // Format tanggal lengkap
            const dateString = now.toLocaleDateString('id-ID', {
                day: 'numeric',
                month: 'long',
                year: 'numeric',
                timeZone: 'Asia/Jakarta'
            });

            document.getElementById('current-time').textContent = timeString;
            document.getElementById('current-day').textContent = dayString;
            document.getElementById('current-full-date').textContent = dateString;
        }

        // Load absensi data
        function loadAbsensiData() {
            const loadingIcon = document.getElementById('loading-icon');
            loadingIcon.style.display = 'inline-block';

            fetch('/api/absensi-today')
                .then(response => response.json())
                .then(data => {
                    updateSchoolInfo(data.school_info);
                    updateAbsensiList(data.absensi);
                    updateTop5List(data.top5_tercepat);
                    updateAbsentList(data.guru_tidak_hadir);
                    updateWorkStatus(data.school_info);
                    updateLastUpdateTime();
                })
                .catch(error => {
                    console.error('Error loading absensi data:', error);
                })
                .finally(() => {
                    loadingIcon.style.display = 'none';
                });
        }



        // Update absensi list
        function updateAbsensiList(absensiData) {
            const loadingState = document.getElementById('loading-state');
            const absensiList = document.getElementById('absensi-list');
            const emptyState = document.getElementById('empty-state');
            
            loadingState.style.display = 'none';
            
            if (absensiData.length === 0) {
                absensiList.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }
            
            emptyState.style.display = 'none';
            absensiList.style.display = 'block';
            
            absensiList.innerHTML = absensiData.map(absensi => `
                <div class="bg-white/10 backdrop-blur-sm rounded-lg p-3 border border-white/20 card-hover">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                                ${absensi.foto ?
                                    `<img src="${absensi.foto}" alt="${absensi.nama_guru}" class="w-8 h-8 rounded-full object-cover">` :
                                    `<i class="fas fa-user text-white text-sm"></i>`
                                }
                            </div>
                            <div>
                                <h3 class="font-semibold text-white text-base">${absensi.nama_guru}</h3>
                                <p class="text-white/70 text-xs">${absensi.jabatan} - ${absensi.divisi}</p>
                            </div>
                        </div>

                        <div class="flex items-center space-x-3">
                            <div class="text-center">
                                <div class="text-white/70 text-xs mb-1">Masuk</div>
                                <div class="text-white font-semibold text-sm">
                                    ${absensi.absensi_masuk || '--:--'}
                                </div>
                                ${absensi.status_masuk ? `<div class="mt-1"><span class="px-1 py-0.5 rounded text-xs ${getWorkStatusClass(absensi.status_masuk)}">${getWorkStatusText(absensi.status_masuk)}</span></div>` : ''}
                            </div>

                            <div class="text-center">
                                <div class="text-white/70 text-xs mb-1">Pulang</div>
                                <div class="text-white font-semibold text-sm">
                                    ${absensi.absensi_pulang || '--:--'}
                                </div>
                                ${absensi.status_pulang ? `<div class="mt-1"><span class="px-1 py-0.5 rounded text-xs ${getWorkStatusClass(absensi.status_pulang)}">${getWorkStatusText(absensi.status_pulang)}</span></div>` : ''}
                            </div>

                            <div class="text-center">
                                <span class="px-2 py-1 rounded-full text-xs font-medium status-badge ${getStatusClass(absensi.status)}">
                                    ${absensi.status}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Get status class
        function getStatusClass(status) {
            switch(status) {
                case 'Hadir': return 'bg-green-500/80 text-green-100';
                case 'Sakit': return 'bg-yellow-500/80 text-yellow-100';
                case 'Izin': return 'bg-blue-500/80 text-blue-100';
                case 'Alfa': return 'bg-red-500/80 text-red-100';
                default: return 'bg-gray-500/80 text-gray-100';
            }
        }

        // Get work status class
        function getWorkStatusClass(status) {
            switch(status) {
                case 'tepat_waktu': return 'status-tepat-waktu';
                case 'terlambat': return 'status-terlambat';
                case 'lembur': return 'status-lembur';
                case 'waktu_pulang': return 'status-waktu-pulang';
                case 'terlalu_awal': return 'status-terlalu-awal';
                case 'lewat_jam_kerja': return 'status-lewat-jam-kerja';
                case 'lewat_lembur': return 'status-lewat-lembur';
                default: return 'bg-gray-500/80 text-white';
            }
        }

        // Get work status text
        function getWorkStatusText(status) {
            switch(status) {
                case 'tepat_waktu': return 'Tepat Waktu';
                case 'terlambat': return 'Terlambat';
                case 'lembur': return 'Lembur';
                case 'waktu_pulang': return 'Waktu Pulang';
                case 'terlalu_awal': return 'Terlalu Awal';
                case 'lewat_jam_kerja': return 'Lewat Jam';
                case 'lewat_lembur': return 'Lewat Lembur';
                default: return 'Unknown';
            }
        }

        // Update Top 5 list
        function updateTop5List(top5Data) {
            const loadingState = document.getElementById('top5-loading');
            const top5List = document.getElementById('top5-list');
            const emptyState = document.getElementById('top5-empty');

            loadingState.style.display = 'none';

            if (top5Data.length === 0) {
                top5List.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            emptyState.style.display = 'none';
            top5List.style.display = 'block';

            top5List.innerHTML = top5Data.map(guru => `
                <div class="flex items-center space-x-2 bg-white/10 rounded-lg p-2 card-hover">
                    <div class="flex-shrink-0">
                        <div class="w-6 h-6 rounded-full flex items-center justify-center ranking-badge text-white font-bold text-xs">
                            ${guru.ranking}
                        </div>
                    </div>
                    <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0">
                        ${guru.foto ?
                            `<img src="${guru.foto}" alt="${guru.nama_guru}" class="w-6 h-6 rounded-full object-cover">` :
                            `<i class="fas fa-user text-white text-xs"></i>`
                        }
                    </div>
                    <div class="flex-1 min-w-0">
                        <h4 class="font-semibold text-white text-sm truncate">${guru.nama_guru}</h4>
                        <p class="text-white/70 text-xs truncate">${guru.jabatan}</p>
                    </div>
                    <div class="flex items-center space-x-1">
                        ${guru.ranking === 1 ? '<i class="fas fa-trophy trophy-gold text-sm"></i>' : ''}
                        ${guru.ranking === 2 ? '<i class="fas fa-trophy trophy-silver text-sm"></i>' : ''}
                        ${guru.ranking === 3 ? '<i class="fas fa-trophy trophy-bronze text-sm"></i>' : ''}
                        <span class="text-white font-bold text-sm">${guru.absensi_masuk}</span>
                    </div>
                </div>
            `).join('');
        }

        // Update absent list
        function updateAbsentList(absentData) {
            const loadingState = document.getElementById('absent-loading');
            const absentList = document.getElementById('absent-list');
            const emptyState = document.getElementById('absent-empty');
            const absentCount = document.getElementById('absent-count');

            loadingState.style.display = 'none';
            absentCount.textContent = absentData.length;

            if (absentData.length === 0) {
                absentList.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            emptyState.style.display = 'none';
            absentList.style.display = 'block';

            absentList.innerHTML = absentData.map(guru => `
                <div class="flex items-center space-x-3 bg-white/5 rounded-lg p-2 card-hover">
                    <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0">
                        ${guru.foto ?
                            `<img src="${guru.foto}" alt="${guru.nama_guru}" class="w-6 h-6 rounded-full object-cover">` :
                            `<i class="fas fa-user text-white text-xs"></i>`
                        }
                    </div>
                    <div class="flex-1 min-w-0">
                        <h4 class="font-medium text-white text-sm truncate">${guru.nama_guru}</h4>
                        <p class="text-white/60 text-xs truncate">${guru.jabatan} - ${guru.divisi}</p>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-yellow-400 text-sm"></i>
                    </div>
                </div>
            `).join('');
        }

        // Update school info
        function updateSchoolInfo(schoolInfo) {
            // Update nama sekolah
            const schoolNameElement = document.getElementById('school-name');
            schoolNameElement.textContent = schoolInfo.nama_sekolah || 'DASHBOARD ABSENSI';

            // Update logo
            const logoContainer = document.getElementById('school-logo');
            const logoImage = document.getElementById('logo-image');
            if (schoolInfo.logo_url) {
                logoImage.src = schoolInfo.logo_url;
                logoImage.alt = `Logo ${schoolInfo.nama_sekolah}`;
                logoContainer.style.display = 'block';
            } else {
                logoContainer.style.display = 'none';
            }

            // Update status hari libur
            const holidayNotice = document.getElementById('holiday-notice');
            const holidayDescription = document.getElementById('holiday-description');
            if (schoolInfo.is_holiday) {
                holidayDescription.textContent = schoolInfo.keterangan_libur || '';
                holidayNotice.style.display = 'block';
            } else {
                holidayNotice.style.display = 'none';
            }
        }

        // Update work status
        function updateWorkStatus(schoolInfo) {
            if (schoolInfo.is_holiday) {
                const workStatus = document.getElementById('work-status');
                const workStatusIcon = document.getElementById('work-status-icon');
                const workStatusText = document.getElementById('work-status-text');

                workStatus.className = 'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-500/80 text-white';
                workStatusIcon.className = 'fas fa-calendar-times mr-2';
                workStatusText.textContent = 'HARI LIBUR';
                workStatus.style.display = 'inline-flex';
                return;
            }

            // Determine current work status based on time
            const now = new Date();
            const currentTime = now.toTimeString().slice(0, 8);

            const jamKerja = schoolInfo.jam_kerja;
            let status = 'di_luar_jam_kerja';
            let statusText = 'Di Luar Jam Kerja';
            let statusClass = 'bg-gray-500/80 text-white';
            let iconClass = 'fas fa-clock mr-2';

            if (currentTime >= jamKerja.mulai_masuk && currentTime <= jamKerja.jam_masuk) {
                status = 'waktu_masuk';
                statusText = 'Waktu Masuk';
                statusClass = 'bg-green-500/80 text-white';
                iconClass = 'fas fa-sign-in-alt mr-2';
            } else if (currentTime > jamKerja.jam_masuk && currentTime <= jamKerja.jam_keluar) {
                status = 'jam_kerja';
                statusText = 'Jam Kerja';
                statusClass = 'bg-blue-500/80 text-white';
                iconClass = 'fas fa-briefcase mr-2';
            } else if (currentTime > jamKerja.jam_keluar && currentTime <= jamKerja.batas_keluar) {
                status = 'waktu_pulang';
                statusText = 'Waktu Pulang';
                statusClass = 'bg-orange-500/80 text-white';
                iconClass = 'fas fa-sign-out-alt mr-2';
            } else if (currentTime > jamKerja.batas_keluar && currentTime <= jamKerja.jam_akhir_lembur) {
                status = 'waktu_lembur';
                statusText = 'Waktu Lembur';
                statusClass = 'bg-purple-500/80 text-white';
                iconClass = 'fas fa-clock mr-2';
            }

            const workStatus = document.getElementById('work-status');
            const workStatusIcon = document.getElementById('work-status-icon');
            const workStatusText = document.getElementById('work-status-text');

            workStatus.className = `inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusClass}`;
            workStatusIcon.className = iconClass;
            workStatusText.textContent = statusText;
            workStatus.style.display = 'inline-flex';
        }

        // Update last update time
        function updateLastUpdateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('id-ID', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('last-update').textContent = timeString;
        }
    </script>
</body>
</html>
