<x-filament-panels::page>
    <!-- Service Status Card -->
    <div class="mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    Status WhatsApp Service
                </h3>
                <div class="flex items-center space-x-2">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        {{ $isServiceRunning ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                        {{ $isServiceRunning ? 'Running' : 'Stopped' }}
                    </span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        {{ $serviceStatus === 'connected' ? 'bg-green-100 text-green-800' :
                           ($serviceStatus === 'connecting' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800') }}">
                        {{ ucfirst($serviceStatus) }}
                    </span>
                </div>
            </div>

            <!-- QR Code Section -->
            @if($isServiceRunning && $qrCode)
                <div class="border-t pt-4">
                    <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">
                        Scan QR Code untuk Menghubungkan WhatsApp
                    </h4>
                    <div class="flex justify-center">
                        <div class="bg-white p-4 rounded-lg shadow-inner">
                            <div id="qrcode" class="flex justify-center"></div>
                        </div>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400 text-center mt-3">
                        Buka WhatsApp di ponsel Anda → Ketuk menu (⋮) → WhatsApp Web → Scan QR code di atas
                    </p>
                </div>
            @elseif($isServiceRunning && $serviceStatus === 'connected')
                <div class="border-t pt-4">
                    <div class="flex items-center justify-center py-8">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <h4 class="text-lg font-medium text-gray-900 dark:text-white">WhatsApp Terhubung!</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Service siap mengirim notifikasi</p>
                        </div>
                    </div>
                </div>
            @elseif($isServiceRunning)
                <div class="border-t pt-4">
                    <div class="flex items-center justify-center py-8">
                        <div class="text-center">
                            <div class="animate-spin w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full mx-auto mb-3"></div>
                            <h4 class="text-md font-medium text-gray-900 dark:text-white">Menunggu Koneksi...</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Service sedang memulai, QR code akan muncul sebentar lagi</p>
                        </div>
                    </div>
                </div>
            @else
                <div class="border-t pt-4">
                    <div class="flex items-center justify-center py-8">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636"></path>
                                </svg>
                            </div>
                            <h4 class="text-md font-medium text-gray-900 dark:text-white">Service Tidak Aktif</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Aktifkan WhatsApp untuk memulai service</p>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Settings Form -->
    <x-filament-panels::form wire:submit="submit">
        {{ $this->form }}
    </x-filament-panels::form>

    <!-- Auto-refresh QR Code -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            @if($qrCode)
                QRCode.toCanvas(document.getElementById('qrcode'), '{{ $qrCode }}', {
                    width: 256,
                    margin: 2,
                    color: {
                        dark: '#000000',
                        light: '#FFFFFF'
                    }
                });
            @endif

            // Auto-refresh status every 10 seconds
            setInterval(function() {
                @this.call('updateServiceStatus');
            }, 10000);
        });

        // Listen for Livewire updates to refresh QR code
        document.addEventListener('livewire:updated', function() {
            const qrElement = document.getElementById('qrcode');
            if (qrElement && '{{ $qrCode }}') {
                qrElement.innerHTML = '';
                QRCode.toCanvas(qrElement, '{{ $qrCode }}', {
                    width: 256,
                    margin: 2,
                    color: {
                        dark: '#000000',
                        light: '#FFFFFF'
                    }
                });
            }
        });
    </script>
</x-filament-panels::page>