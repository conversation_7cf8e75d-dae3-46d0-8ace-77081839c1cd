name: ci

on: [push, pull_request]

# This allows a subsequently queued workflow run to interrupt previous runs
concurrency:
  group: "${{ github.workflow }} @ ${{ github.event.pull_request.head.label || github.head_ref || github.ref }}"
  cancel-in-progress: true

jobs:
  test:
    runs-on: ubuntu-latest

    permissions:
      contents: read

    strategy:
      matrix:
        node-version: [6.x, 8.x, 10.x, 12.x, 14.x, 16.x, 18.x]

    steps:
      - uses: actions/checkout@v3
        with:
          persist-credentials: false

      - name: Use Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install
        run: |
          npm install

      - name: Run tests
        run: |
          npm run test
