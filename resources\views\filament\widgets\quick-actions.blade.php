<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center gap-2">
                <x-heroicon-o-bolt class="h-5 w-5" />
                Quick Actions
            </div>
        </x-slot>

        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <!-- <PERSON>bah Guru -->
            <a href="{{ route('filament.admin.resources.gurus.create') }}" 
               class="flex flex-col items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors group">
                <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mb-2 group-hover:bg-blue-600 transition-colors">
                    <x-heroicon-o-user-plus class="h-6 w-6 text-white" />
                </div>
                <span class="text-sm font-medium text-blue-700"><PERSON><PERSON> Guru</span>
            </a>

            <!-- <PERSON>hat Absensi -->
            <a href="{{ route('filament.admin.resources.absensi-gurus.index') }}" 
               class="flex flex-col items-center p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors group">
                <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mb-2 group-hover:bg-green-600 transition-colors">
                    <x-heroicon-o-clipboard-document-check class="h-6 w-6 text-white" />
                </div>
                <span class="text-sm font-medium text-green-700">Lihat Absensi</span>
            </a>

            <!-- Kelola Alat -->
            <a href="{{ route('filament.admin.resources.alats.index') }}" 
               class="flex flex-col items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors group">
                <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mb-2 group-hover:bg-purple-600 transition-colors">
                    <x-heroicon-o-device-phone-mobile class="h-6 w-6 text-white" />
                </div>
                <span class="text-sm font-medium text-purple-700">Kelola Alat</span>
            </a>

            <!-- Settings -->
            <a href="{{ route('filament.admin.pages.settings') }}" 
               class="flex flex-col items-center p-4 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors group">
                <div class="w-12 h-12 bg-gray-500 rounded-full flex items-center justify-center mb-2 group-hover:bg-gray-600 transition-colors">
                    <x-heroicon-o-cog-6-tooth class="h-6 w-6 text-white" />
                </div>
                <span class="text-sm font-medium text-gray-700">Pengaturan</span>
            </a>

            <!-- Dashboard Umum -->
            <a href="{{ route('public.dashboard') }}" 
               target="_blank"
               class="flex flex-col items-center p-4 bg-indigo-50 hover:bg-indigo-100 rounded-lg transition-colors group">
                <div class="w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center mb-2 group-hover:bg-indigo-600 transition-colors">
                    <x-heroicon-o-tv class="h-6 w-6 text-white" />
                </div>
                <span class="text-sm font-medium text-indigo-700">Monitor Umum</span>
            </a>

            <!-- Export Data -->
            <a href="{{ route('absensi-guru.export-csv') }}" 
               class="flex flex-col items-center p-4 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors group">
                <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mb-2 group-hover:bg-orange-600 transition-colors">
                    <x-heroicon-o-document-arrow-down class="h-6 w-6 text-white" />
                </div>
                <span class="text-sm font-medium text-orange-700">Export Data</span>
            </a>
        </div>

        <!-- Additional Info Cards -->
        <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- System Status -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                    <div>
                        <h4 class="font-medium text-green-800">Sistem Online</h4>
                        <p class="text-sm text-green-600">Semua layanan berjalan normal</p>
                    </div>
                </div>
            </div>

            <!-- Last Backup -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center">
                    <x-heroicon-o-shield-check class="h-6 w-6 text-blue-500 mr-3" />
                    <div>
                        <h4 class="font-medium text-blue-800">Backup Terakhir</h4>
                        <p class="text-sm text-blue-600">{{ now()->subHours(2)->diffForHumans() }}</p>
                    </div>
                </div>
            </div>

            <!-- Server Time -->
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <div class="flex items-center">
                    <x-heroicon-o-clock class="h-6 w-6 text-gray-500 mr-3" />
                    <div>
                        <h4 class="font-medium text-gray-800">Waktu Server</h4>
                        <p class="text-sm text-gray-600" id="server-time">{{ now()->format('d M Y H:i:s') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </x-filament::section>

    <script>
        // Update server time every second
        setInterval(function() {
            const now = new Date();
            const options = { 
                year: 'numeric', 
                month: 'short', 
                day: '2-digit',
                hour: '2-digit', 
                minute: '2-digit', 
                second: '2-digit',
                hour12: false
            };
            document.getElementById('server-time').textContent = now.toLocaleDateString('id-ID', options);
        }, 1000);
    </script>
</x-filament-widgets::widget>
