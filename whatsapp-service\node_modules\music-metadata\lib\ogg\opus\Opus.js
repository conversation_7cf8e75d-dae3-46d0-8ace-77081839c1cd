"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IdHeader = void 0;
const Token = require("token-types");
/**
 * Opus ID Header parser
 * Ref: https://wiki.xiph.org/OggOpus#ID_Header
 */
class IdHeader {
    constructor(len) {
        this.len = len;
        if (len < 19) {
            throw new Error("ID-header-page 0 should be at least 19 bytes long");
        }
    }
    get(buf, off) {
        return {
            magicSignature: new Token.StringType(8, 'ascii').get(buf, off + 0),
            version: buf.readUInt8(off + 8),
            channelCount: buf.readUInt8(off + 9),
            preSkip: buf.readInt16LE(off + 10),
            inputSampleRate: buf.readInt32LE(off + 12),
            outputGain: buf.readInt16LE(off + 16),
            channelMapping: buf.readUInt8(off + 18)
        };
    }
}
exports.IdHeader = IdHeader;
