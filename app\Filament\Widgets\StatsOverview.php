<?php

namespace App\Filament\Widgets;

use App\Models\Guru;
use App\Models\AbsensiGuru;
use App\Models\Alat;
use App\Models\User;
use Carbon\Carbon;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class StatsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();

        // Basic counts
        $totalGuru = Guru::count();
        $totalAlat = Alat::count();
        $totalUsers = User::count();

        // Today's attendance
        $absensiToday = AbsensiGuru::whereDate('tanggal', $today)->count();
        $hadirToday = AbsensiGuru::whereDate('tanggal', $today)->where('status', 'Hadir')->count();
        $belumAbsen = $totalGuru - $absensiToday;

        // This month stats
        $absensiThisMonth = AbsensiGuru::whereDate('tanggal', '>=', $thisMonth)->count();

        // Attendance rate today
        $attendanceRate = $totalGuru > 0 ? round(($hadirToday / $totalGuru) * 100, 1) : 0;

        // Late arrivals today
        $lateToday = AbsensiGuru::whereDate('tanggal', $today)
            ->whereTime('absensi_masuk', '>', '08:00:00') // Assuming 8 AM is the limit
            ->count();

        return [
            Stat::make('Total Guru', $totalGuru)
                ->description('Guru terdaftar')
                ->descriptionIcon('heroicon-m-users')
                ->color('primary'),

            Stat::make('Hadir Hari Ini', $hadirToday)
                ->description("Dari {$totalGuru} guru")
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),

            Stat::make('Belum Absen', $belumAbsen)
                ->description('Guru belum absen')
                ->descriptionIcon('heroicon-m-clock')
                ->color($belumAbsen > 0 ? 'warning' : 'success'),

            Stat::make('Tingkat Kehadiran', $attendanceRate . '%')
                ->description('Hari ini')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color($attendanceRate >= 80 ? 'success' : ($attendanceRate >= 60 ? 'warning' : 'danger')),

            Stat::make('Terlambat', $lateToday)
                ->description('Guru terlambat hari ini')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($lateToday > 0 ? 'danger' : 'success'),

            Stat::make('Total Absensi Bulan Ini', $absensiThisMonth)
                ->description('Record absensi')
                ->descriptionIcon('heroicon-m-calendar-days')
                ->color('info'),

            Stat::make('Perangkat Aktif', $totalAlat)
                ->description('Alat absensi')
                ->descriptionIcon('heroicon-m-device-phone-mobile')
                ->color('gray'),

            Stat::make('Admin Users', $totalUsers)
                ->description('Pengguna sistem')
                ->descriptionIcon('heroicon-m-user-group')
                ->color('gray'),
        ];
    }
}