<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Models\Guru;
use App\Models\SubKelas;
use App\Exceptions\DatabaseConstraintException;
use Illuminate\Database\QueryException;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Testing Error Handling System ===\n\n";

// Test 1: Foreign Key Constraint Error
echo "1. Testing Foreign Key Constraint Error:\n";
try {
    // Try to create a sub_kelas with non-existent guru
    $subKelas = new SubKelas();
    $subKelas->nama_subkelas = 'Test Kelas';
    $subKelas->Walikelas_id = 99999; // Non-existent guru ID
    $subKelas->Kelas_id = 1;
    $subKelas->save();
    
    echo "✗ Should have failed but didn't\n";
} catch (QueryException $e) {
    $friendlyException = DatabaseConstraintException::fromQueryException($e);
    echo "✓ Caught QueryException\n";
    echo "Original Error: " . substr($e->getMessage(), 0, 100) . "...\n";
    echo "Friendly Message: " . $friendlyException->getMessage() . "\n";
}

echo "\n";

// Test 2: Duplicate Entry Error
echo "2. Testing Duplicate Entry Error:\n";
try {
    // Try to create guru with duplicate NUPTK
    $existingGuru = Guru::first();
    if ($existingGuru) {
        $newGuru = new Guru();
        $newGuru->UUid = 'test-uuid-' . time();
        $newGuru->nama_guru = 'Test Guru';
        $newGuru->JK = 'L';
        $newGuru->TTL = '1990-01-01';
        $newGuru->NUPTK = $existingGuru->NUPTK; // Duplicate NUPTK
        $newGuru->Jabatan_id = 1;
        $newGuru->TKT = 'S1';
        $newGuru->Jurusan = 'Test';
        $newGuru->Tahun_Lulus = 2020;
        $newGuru->TMT = '2020-01-01';
        $newGuru->SI_nonInduk = 'Induk';
        $newGuru->Alamat = 'Test Address';
        $newGuru->No_Hp = '08123456789';
        $newGuru->Divisi_id = 1;
        $newGuru->StatusGuru_id = 1;
        $newGuru->save();
        
        echo "✗ Should have failed but didn't\n";
    } else {
        echo "⚠ No existing guru found to test duplicate NUPTK\n";
    }
} catch (QueryException $e) {
    $friendlyException = DatabaseConstraintException::fromQueryException($e);
    echo "✓ Caught QueryException\n";
    echo "Original Error: " . substr($e->getMessage(), 0, 100) . "...\n";
    echo "Friendly Message: " . $friendlyException->getMessage() . "\n";
}

echo "\n";

// Test 3: Test different error codes
echo "3. Testing Error Message Mapping:\n";

$testCases = [
    [
        'code' => 1451,
        'message' => 'Cannot delete or update a parent row: a foreign key constraint fails (`test`.`sub_kelas`, CONSTRAINT `sub_kelas_walikelas_id_foreign` FOREIGN KEY (`Walikelas_id`) REFERENCES `gurus` (`id`))',
        'expected' => 'wali kelas'
    ],
    [
        'code' => 1062,
        'message' => 'Duplicate entry \'12345\' for key \'gurus_nuptk_unique\'',
        'expected' => 'NUPTK sudah digunakan'
    ],
    [
        'code' => 1452,
        'message' => 'Cannot add or update a child row: a foreign key constraint fails',
        'expected' => 'Data yang direferensikan tidak ditemukan'
    ],
    [
        'code' => 1054,
        'message' => 'Unknown column \'invalid_column\' in \'field list\'',
        'expected' => 'kesalahan struktur database'
    ]
];

foreach ($testCases as $index => $testCase) {
    echo "Test 3." . ($index + 1) . " - Error Code {$testCase['code']}:\n";
    
    // Create a mock QueryException
    $mockException = new QueryException(
        'test',
        [],
        new \PDOException($testCase['message'], $testCase['code'])
    );
    
    // Set errorInfo manually
    $reflection = new \ReflectionClass($mockException);
    $errorInfoProperty = $reflection->getProperty('errorInfo');
    $errorInfoProperty->setAccessible(true);
    $errorInfoProperty->setValue($mockException, [null, $testCase['code'], $testCase['message']]);
    
    $friendlyException = DatabaseConstraintException::fromQueryException($mockException);
    $friendlyMessage = $friendlyException->getMessage();
    
    echo "Friendly Message: {$friendlyMessage}\n";
    
    if (stripos($friendlyMessage, $testCase['expected']) !== false) {
        echo "✓ Message mapping correct\n";
    } else {
        echo "✗ Message mapping incorrect\n";
    }
    echo "\n";
}

echo "=== Error Handling Test Complete ===\n";
echo "The system now provides user-friendly error messages for:\n";
echo "- Foreign key constraint violations\n";
echo "- Duplicate entry errors\n";
echo "- Database structure errors\n";
echo "- General database errors\n";
echo "\nAll errors are logged for debugging while showing friendly messages to users.\n";
