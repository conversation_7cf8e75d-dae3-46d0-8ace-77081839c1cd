"use strict";
/**
 * Extended Lame Header
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExtendedLameHeader = void 0;
const Token = require("token-types");
const common = require("../common/Util");
const ReplayGainDataFormat_1 = require("./ReplayGainDataFormat");
/**
 * Info Tag
 * @link http://gabriel.mp3-tech.org/mp3infotag.html
 * @link https://github.com/quodlibet/mutagen/blob/abd58ee58772224334a18817c3fb31103572f70e/mutagen/mp3/_util.py#L112
 */
exports.ExtendedLameHeader = {
    len: 27,
    get: (buf, off) => {
        const track_peak = Token.UINT32_BE.get(buf, off + 2);
        return {
            revision: common.getBitAllignedNumber(buf, off, 0, 4),
            vbr_method: common.getBitAllignedNumber(buf, off, 4, 4),
            lowpass_filter: 100 * Token.UINT8.get(buf, off + 1),
            track_peak: track_peak === 0 ? undefined : track_peak / Math.pow(2, 23),
            track_gain: ReplayGainDataFormat_1.ReplayGain.get(buf, 6),
            album_gain: ReplayGainDataFormat_1.ReplayGain.get(buf, 8),
            music_length: Token.UINT32_BE.get(buf, off + 20),
            music_crc: Token.UINT8.get(buf, off + 24),
            header_crc: Token.UINT16_BE.get(buf, off + 24)
        };
    }
};
