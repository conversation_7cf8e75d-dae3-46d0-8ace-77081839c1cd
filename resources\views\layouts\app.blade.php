<!DOCTYPE html>
<html>

<head>
    <title>{{ config('app.name', '<PERSON><PERSON>') }}</title>
    @filamentStyles
</head>

<body>
    @filamentScripts
    <script>
    window.mqttConfig = {
        host: '{{ config('
        mqtt.host ') }}',
        port: {
            {
                config('mqtt.port')
            }
        },
        clientId: '{{ config('
        mqtt.client_id ') }}',
        username: '{{ config('
        mqtt.username ') }}',
        password: '{{ config('
        mqtt.password ') }}'
    };
    </script>
    <div id="mqtt-status">Menghubungkan ke MQTT...</div>
    {{ $slot }}
</body>

</html>