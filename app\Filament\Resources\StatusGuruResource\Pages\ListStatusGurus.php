<?php

namespace App\Filament\Resources\StatusGuruResource\Pages;

use App\Filament\Resources\StatusGuruResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListStatusGurus extends ListRecords
{
    protected static string $resource = StatusGuruResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
