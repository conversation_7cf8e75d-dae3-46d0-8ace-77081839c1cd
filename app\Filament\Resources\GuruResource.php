<?php

namespace App\Filament\Resources;

use Filament\Forms;
use App\Models\Guru;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\DateTimePicker;
use App\Filament\Resources\GuruResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\GuruResource\RelationManagers;
use Filament\Notifications\Notification;
use App\Traits\HandlesResourceErrors;

class GuruResource extends Resource
{
    use HandlesResourceErrors;

    protected static ?string $model = Guru::class;
    protected static ?string $navigationLabel = 'Guru';
    protected static ?string $navigationGroup = 'Data Guru';
    protected static ?string $navigationIcon = 'heroicon-o-academic-cap';
    protected static ?int $navigationSort = -2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('UUid')
                    ->label('UUID Guru')
                    ->live()
                    ->required()
                    ->afterStateHydrated(function ($component, $state, $livewire) {
                        // Isi otomatis jika ada updateUuidGuru terbaru
                        if (property_exists($livewire, 'updateUuidGuru') && !empty($livewire->updateUuidGuru)) {
                            // Ambil UUID guru terakhir yang masuk
                            $lastUuid = array_key_last($livewire->updateUuidGuru);
                            $component->state($livewire->updateUuidGuru[$lastUuid]);
                        }
                    }),
                TextInput::make('nama_guru')
                ->label('Nama Guru')
                ->required(),
                Radio::make('JK')
                ->label('Jenis Kelamin')
                ->options([
                    'L' => 'Laki-laki',
                    'P' => 'Perempuan',
                ])
                ->required(),
                TextInput::make('NUPTK')
                ->label('Nomor Unik Pendidik dan Tenaga Kependidikan')
                ->required(),
                DatePicker::make('TTL')
                ->label('Tanggal Lahir')
                ->required(),
                TextInput::make('Jurusan')
                ->label('Jurusan')
                ->required(),
                TextInput::make('TKT')
                ->label('TKT')
                ->required(),
                TextInput::make('Tahun_Lulus')
                ->label('Tahun Lulus')
                ->required(),
                DatePicker::make('TMT')
                ->label('Terhitung Mulai Tanggal')
                ->required(),
                Radio::make('SI_nonInduk')
                ->label('Nomer Induk')
                ->options([
                    'Induk' => 'Induk',
                    'Non Induk' => 'Non Induk',
                ])
                ->required(),
                TextInput::make('Alamat')
                ->label('Alamat')
                ->required(),
                TextInput::make('No_Hp')
                ->label('No HP / WhatsApp')
                ->tel()
                
                ->required()
                ->helperText('Nomor HP yang akan digunakan untuk notifikasi WhatsApp'),

                Toggle::make('whatsapp_notification')
                    ->label('Aktifkan Notifikasi WhatsApp')
                    ->default(true)
                    ->helperText('Nyalakan untuk menerima notifikasi absensi via WhatsApp'),
                Select::make('Jabatan_id')
                ->relationship('jabatan', 'nama_jabatan')
                ->required()
                ->preload()
                ->searchable()
                ->createOptionForm([
                    TextInput::make('nama_jabatan')
                    ->label('Nama Jabatan')
                    ->required(),
                    Radio::make('status')
                    ->label('Status')
                    ->options([
                        'aktif' => 'Aktif',
                        'tidak aktif' => 'Tidak Aktif',
                    ]),
                ]),
                Select::make('Divisi_id')
                ->label('Divisi')
                ->relationship('divisi', 'nama_divisi')
                ->required()
                ->preload()
                ->searchable()
                ->createOptionForm([
                    TextInput::make('nama_divisi')
                    ->label('Nama Divisi')
                    ->required(),
                    Radio::make('status')
                    ->label('Status')
                    ->options([
                        'aktif' => 'Aktif',
                        'tidak aktif' => 'Tidak Aktif',
                    ]),
                ]),
                Select::make('StatusGuru_id')
                ->label('Status Guru')
                ->relationship('satusguru', 'nama_statusguru')
                ->required()
                ->preload()
                ->searchable()
                ->createOptionForm([
                    TextInput::make('nama_statusguru')
                    ->label('Nama Status Guru')
                    ->required(),
                    Radio::make('status')
                    ->label('Status')
                    ->options([
                        'aktif' => 'Aktif',
                        'tidak aktif' => 'Tidak Aktif',
                    ]),
                ]),
                FileUpload::make('gambar')
                ->label('Foto')
                ->disk('public')
                ->directory('guru')
                ->image()
                ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('gambar')
                    ->label('Foto')
                    ->circular(),
                TextColumn::make('UUid')
                    ->label('UUID')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('nama_guru')
                    ->label('Nama Guru')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('JK')
                    ->label('Jenis Kelamin')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'L' => 'Laki-laki',
                        'P' => 'Perempuan',
                        default => $state,
                    })
                    ->sortable(),
                TextColumn::make('NUPTK')
                    ->label('NUPTK')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('TKT')
                    ->label('TKT')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('Jurusan')
                    ->label('Jurusan')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('Tahun_Lulus')
                    ->label('Tahun Lulus')
                    ->sortable(),
                TextColumn::make('TMT')
                    ->label('TMT')
                    ->date()
                    ->sortable(),
                TextColumn::make('SI_nonInduk')
                    ->label('Nomer Induk')
                    ->sortable(),
                TextColumn::make('Alamat')
                    ->label('Alamat')
                    ->searchable(),
                TextColumn::make('No_Hp')
                    ->label('No HP / WhatsApp')
                    ->searchable(),

                IconColumn::make('whatsapp_notification')
                    ->label('Notif WA')
                    ->boolean()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('jabatan.nama_jabatan')
                    ->label('Jabatan')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('divisi.nama_divisi')
                    ->label('Divisi')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('satusguru.nama_statusguru')
                    ->label('Status Guru')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('deleted_at')
                    ->label('Dihapus')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->action(function (Guru $record) {
                        return $this->safeExecute(function () use ($record) {
                            // Cek apakah guru masih menjadi wali kelas
                            $subKelasCount = $record->subKelas()->count();
                            if ($subKelasCount > 0) {
                                Notification::make()
                                    ->title('Tidak dapat menghapus guru')
                                    ->body("Guru ini masih menjadi wali kelas di {$subKelasCount} sub kelas. Silakan ubah wali kelas terlebih dahulu atau gunakan soft delete.")
                                    ->warning()
                                    ->send();
                                return false;
                            }

                            $record->delete(); // Soft delete

                            Notification::make()
                                ->title('Guru berhasil dihapus')
                                ->body('Data guru telah dipindahkan ke trash. Anda dapat mengembalikannya jika diperlukan.')
                                ->success()
                                ->send();

                            return true;
                        }, 'penghapusan guru');
                    }),
                Tables\Actions\ForceDeleteAction::make()
                    ->action(function (Guru $record) {
                        return $this->safeExecute(function () use ($record) {
                            $record->forceDelete();

                            Notification::make()
                                ->title('Guru berhasil dihapus permanen')
                                ->body('Data guru telah dihapus secara permanen dari sistem.')
                                ->success()
                                ->send();

                            return true;
                        }, 'penghapusan permanen guru');
                    }),
                Tables\Actions\RestoreAction::make()
                    ->action(function (Guru $record) {
                        return $this->safeExecute(function () use ($record) {
                            $record->restore();

                            Notification::make()
                                ->title('Guru berhasil dipulihkan')
                                ->body('Data guru telah dipulihkan dan dapat digunakan kembali.')
                                ->success()
                                ->send();

                            return true;
                        }, 'pemulihan guru');
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListGurus::route('/'),
            'create' => Pages\CreateGuru::route('/create'),
            'edit' => Pages\EditGuru::route('/{record}/edit'),
        ];
    }
}