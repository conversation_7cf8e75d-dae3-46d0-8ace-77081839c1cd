<?php

namespace App\Filament\Widgets;

use App\Models\AbsensiGuru;
use App\Models\Guru;
use Carbon\Carbon;
use Filament\Widgets\ChartWidget;

class MonthlySummary extends ChartWidget
{
    protected static ?string $heading = 'Ringkasan Absensi Bulanan';
    protected static ?int $sort = 5;
    protected int | string | array $columnSpan = 'full';

    protected function getData(): array
    {
        $currentMonth = Carbon::now()->startOfMonth();
        $totalGuru = Guru::count();
        
        // Get attendance data for current month
        $hadirCount = AbsensiGuru::whereDate('tanggal', '>=', $currentMonth)
            ->where('status', 'Hadir')
            ->count();
            
        $sakitCount = AbsensiGuru::whereDate('tanggal', '>=', $currentMonth)
            ->where('status', 'Sakit')
            ->count();
            
        $izinCount = AbsensiGuru::whereDate('tanggal', '>=', $currentMonth)
            ->where('status', 'Izin')
            ->count();
            
        $alfaCount = AbsensiGuru::whereDate('tanggal', '>=', $currentMonth)
            ->where('status', 'Alfa')
            ->count();

        return [
            'datasets' => [
                [
                    'data' => [$hadirCount, $sakitCount, $izinCount, $alfaCount],
                    'backgroundColor' => [
                        'rgba(34, 197, 94, 0.8)',   // Green for Hadir
                        'rgba(245, 158, 11, 0.8)',  // Yellow for Sakit
                        'rgba(59, 130, 246, 0.8)',  // Blue for Izin
                        'rgba(239, 68, 68, 0.8)',   // Red for Alfa
                    ],
                    'borderColor' => [
                        'rgba(34, 197, 94, 1)',
                        'rgba(245, 158, 11, 1)',
                        'rgba(59, 130, 246, 1)',
                        'rgba(239, 68, 68, 1)',
                    ],
                    'borderWidth' => 2,
                ],
            ],
            'labels' => ['Hadir', 'Sakit', 'Izin', 'Alfa'],
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }

    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'maintainAspectRatio' => false,
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) {
                            const label = context.label || "";
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return label + ": " + value + " (" + percentage + "%)";
                        }'
                    ]
                ],
            ],
        ];
    }
}
