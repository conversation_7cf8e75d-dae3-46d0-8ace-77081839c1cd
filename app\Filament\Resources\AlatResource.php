<?php

namespace App\Filament\Resources;

use Filament\Forms;
use App\Models\Alat;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Livewire\Attributes\On;
use Filament\Resources\Resource;
use Filament\Forms\Components\Select;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\AlatResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\AlatResource\RelationManagers;

class AlatResource extends Resource
{
    protected static ?string $model = Alat::class;
    protected static ?string $navigationLabel = 'Alat';
    protected static ?string $navigationGroup = 'Umum';
    protected static ?string $navigationIcon = 'heroicon-o-speaker-wave';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('UUid')
                    ->label('UUID')
                    ->live()
                    ->options(function ($livewire) {
                        \Log::info("Livewire class: " . get_class($livewire));
                        \Log::info("Fetching uuidOptions: " . json_encode($livewire->uuidOptions ?? []));
                        return $livewire->uuidOptions ?? []; 
                    })
                    ->searchable()
                    ->required(),
                Select::make('setting_id')
                    ->relationship('sekolah', 'nama_sekolah')
                    ->label('Nama Sekolah')
                    ->required()
                    ->preload()
                    ->searchable(),
                TextInput::make('nama_alat')
                    ->label('Nama Alat')
                    ->required()
                    ->maxLength(255),
                TextInput::make('mode_alat')
                    ->label('Mode Alat')
                    ->required()
                    ->maxLength(255),
                TextInput::make('tipe_pengguna')
                    ->label('Tipe Pengguna')
                    ->required()
                    ->maxLength(255),
                Select::make('status')
                    ->label('Status')
                    ->options([
                        'aktif' => 'Aktif',
                        'tidak aktif' => 'Tidak Aktif',
                    ])
                    ->required(),
                Select::make('timezone')
                    ->label('Timezone')
                    ->options([
                        'WIB' => 'WIB',
                        'WITA' => 'WITA',
                        'WIT' => 'WIT',
                    ])
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('UUid')
                    ->label('UUID')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('sekolah.nama_sekolah')
                    ->label('Nama Sekolah')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('nama_alat')
                    ->label('Nama Alat')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('mode_alat')
                    ->label('Mode Alat')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('tipe_pengguna')
                    ->label('Tipe Pengguna')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'aktif' => 'success',
                        'tidak aktif' => 'danger',
                    })
                    ->sortable(),
                TextColumn::make('timezone')
                    ->label('Timezone')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAlats::route('/'),
            'create' => Pages\CreateAlat::route('/create'),
            'edit' => Pages\EditAlat::route('/{record}/edit'),
        ];
    }

}