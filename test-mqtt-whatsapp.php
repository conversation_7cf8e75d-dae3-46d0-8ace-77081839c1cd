<?php

require_once 'vendor/autoload.php';

use PhpMqtt\Client\MqttClient;
use PhpMqtt\Client\ConnectionSettings;

echo "=== Test MQTT WhatsApp Integration ===\n";

// MQTT Configuration
$host = 'ffe180c2d6054ac78559eccdc9597e4f.s1.eu.hivemq.cloud';
$port = 8883;
$clientId = 'test_mqtt_whatsapp_' . time();
$username = 'absensi-sekolah';
$password = 'Acekolah123';

$settings = (new ConnectionSettings)
    ->setUsername($username)
    ->setPassword($password)
    ->setUseTls(true);

try {
    $mqtt = new MqttClient($host, $port, $clientId);
    $mqtt->connect($settings, true);
    echo "✓ Connected to MQTT broker\n";

    // Test data for absensi masuk
    $testData = [
        'UUIDguru' => '0F9D8E7FDGW', // UUID guru tes
        'timestamp' => time(),
        'action' => 'absensi',
        'method' => 'rfid',
        'device_id' => 'ESP32-7CECEC',
        'firmware_version' => '1.2.0',
        'rssi' => -53
    ];

    echo "Sending test absensi masuk data...\n";
    echo "Data: " . json_encode($testData) . "\n";

    $mqtt->publish('absensi/data', json_encode($testData), 0);
    echo "✓ Test message sent to absensi/data topic\n";

    // Wait a moment
    sleep(3);

    // Test data for absensi pulang (same guru, different timestamp)
    $testData['timestamp'] = time() + 28800; // 8 hours later
    
    echo "Sending test absensi pulang data...\n";
    echo "Data: " . json_encode($testData) . "\n";

    $mqtt->publish('absensi/data', json_encode($testData), 0);
    echo "✓ Test message sent to absensi/data topic\n";

    $mqtt->disconnect();
    echo "✓ Disconnected from MQTT broker\n";

    echo "\n=== Test Complete ===\n";
    echo "Check:\n";
    echo "1. Laravel logs for MQTT processing\n";
    echo "2. WhatsApp for notifications\n";
    echo "3. Database for new absensi records\n";

} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    exit(1);
}
