<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Models\Setting;
use App\Services\WhatsAppProcessService;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Testing WhatsApp Toggle Persistence Fix ===\n\n";

$processService = app(WhatsAppProcessService::class);

echo "1. Current Database State:\n";
$setting = Setting::getActive();
if ($setting) {
    echo "WhatsApp Enabled in DB: " . ($setting->whatsapp_enabled ? 'TRUE' : 'FALSE') . "\n";
} else {
    echo "No settings found in database\n";
}

echo "\n2. Current Service State:\n";
$status = $processService->getStatus();
echo "Service Running: " . ($status['process_running'] ? 'TRUE' : 'FALSE') . "\n";
echo "Connection Status: " . ($status['connection_status'] ?? 'unknown') . "\n";

echo "\n3. Testing Toggle OFF (Stop Service):\n";
$stopResult = $processService->stopService();
if ($stopResult['success']) {
    echo "✅ Service stopped successfully\n";
    
    // Simulate saving toggle state
    if ($setting) {
        $setting->whatsapp_enabled = false;
        $setting->save();
        echo "✅ Database updated: whatsapp_enabled = FALSE\n";
    }
} else {
    echo "❌ Failed to stop service: " . ($stopResult['error'] ?? 'Unknown error') . "\n";
}

echo "\n4. Verify State After Stop:\n";
$setting = Setting::getActive();
echo "Database State: " . ($setting->whatsapp_enabled ? 'TRUE' : 'FALSE') . "\n";

$status = $processService->getStatus();
echo "Service State: " . ($status['process_running'] ? 'TRUE' : 'FALSE') . "\n";

echo "\n5. Simulate Page Refresh (Check Persistence):\n";
// This simulates what happens when user refreshes the page
$setting = Setting::getActive();
$serviceStatus = $processService->getStatus();

echo "After 'Refresh':\n";
echo "Database says: " . ($setting->whatsapp_enabled ? 'ENABLED' : 'DISABLED') . "\n";
echo "Service says: " . ($serviceStatus['process_running'] ? 'RUNNING' : 'STOPPED') . "\n";

if ($setting->whatsapp_enabled === $serviceStatus['process_running']) {
    echo "✅ Database and Service are IN SYNC\n";
} else {
    echo "❌ Database and Service are OUT OF SYNC\n";
    echo "This would cause auto-restart on page refresh\n";
}

echo "\n6. Testing Toggle ON (Start Service):\n";
$startResult = $processService->startService();
if ($startResult['success']) {
    echo "✅ Service started successfully\n";
    
    // Simulate saving toggle state
    if ($setting) {
        $setting->whatsapp_enabled = true;
        $setting->save();
        echo "✅ Database updated: whatsapp_enabled = TRUE\n";
    }
} else {
    echo "❌ Failed to start service: " . ($startResult['error'] ?? 'Unknown error') . "\n";
}

echo "\n7. Final State Verification:\n";
$setting = Setting::getActive();
$status = $processService->getStatus();

echo "Database State: " . ($setting->whatsapp_enabled ? 'TRUE' : 'FALSE') . "\n";
echo "Service State: " . ($status['process_running'] ? 'TRUE' : 'FALSE') . "\n";

if ($setting->whatsapp_enabled === $status['process_running']) {
    echo "✅ Final state is SYNCHRONIZED\n";
} else {
    echo "❌ Final state is NOT SYNCHRONIZED\n";
}

echo "\n=== Fix Summary ===\n";
echo "✅ Added saveToggleState() - Immediately saves toggle to database\n";
echo "✅ Added syncDatabaseWithServiceStatus() - Syncs DB with actual service\n";
echo "✅ Updated mount() - Checks and syncs state on page load\n";
echo "✅ Added error handling - Reverts toggle if service operation fails\n";
echo "✅ Added logging - Tracks state changes for debugging\n";

echo "\n=== How It Works Now ===\n";
echo "1. User toggles WhatsApp ON/OFF → State saved to database immediately\n";
echo "2. Service starts/stops → Status updated in real-time\n";
echo "3. Page refresh → System checks if DB and service are in sync\n";
echo "4. If out of sync → Database updated to match actual service status\n";
echo "5. Toggle shows correct state → No unexpected auto-start/stop\n";

echo "\n=== Benefits ===\n";
echo "• Toggle state persists across page refreshes\n";
echo "• No unexpected service restarts\n";
echo "• Database always reflects actual service status\n";
echo "• Better error handling and user feedback\n";
echo "• Logging for debugging state issues\n";

echo "\n=== Test Complete ===\n";
echo "The toggle persistence issue should now be fixed!\n";
echo "Try: Toggle OFF → Refresh page → Toggle should stay OFF\n";
