<?php

namespace App\Http\Controllers;

use App\Models\Alat;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class DeviceController extends Controller
{
    public function registerDevice(Request $request)
    {
        $request->validate([
            'UUid' => 'required|uuid|unique:alats,UUid',
            'nama_alat' => 'required|string|max:255',
            'mode_alat' => 'required|string|max:255',
            'tipe_pengguna' => 'required|string|max:255',
            'status' => 'required|in:aktif,tidak aktif',
            'timezone' => 'required|in:WIB,WITA,WIT',
            'setting_id' => 'required|exists:settings,id',
        ]);

        try {
            $alat = Alat::create($request->all());
            Log::info('Device registered to database: ' . $alat->UUid);
            return response()->json(['message' => 'Device registered successfully'], 201);
        } catch (\Exception $e) {
            Log::error('Failed to register device: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to register device: ' . $e->getMessage()], 500);
        }
    }
}