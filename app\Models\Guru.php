<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Guru extends Model
{
    use SoftDeletes;

    protected $guarded = [];

    protected $dates = ['deleted_at'];

    public function jabatan(){
        return $this->belongsTo(Jabatan::class, 'Jabatan_id', 'id');
    }
    public function divisi(){
        return $this->belongsTo(Divisi::class, 'Divisi_id', 'id');
    }
    public function satusguru(){
        return $this->belongsTo(StatusGuru::class, 'StatusGuru_id', 'id');
    }

    // Relasi dengan sub kelas (sebagai wali kelas)
    public function subKelas(){
        return $this->hasMany(SubKelas::class, 'Walikelas_id', 'id');
    }

    // Relasi dengan absensi guru
    public function absensiGuru(){
        return $this->hasMany(AbsensiGuru::class, 'guru_id', 'id');
    }
}