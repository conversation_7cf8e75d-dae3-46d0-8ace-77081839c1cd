<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Guru extends Model
{
    protected $guarded = [];

    public function jabatan(){
        return $this->belongsTo(Jabatan::class, 'Jabatan_id', 'id');
    }
    public function divisi(){
        return $this->belongsTo(Divisi::class, 'Divisi_id', 'id');
    }
    public function satusguru(){
        return $this->belongsTo(StatusGuru::class, 'StatusGuru_id', 'id');
    }
}