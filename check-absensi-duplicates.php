<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use App\Models\AbsensiGuru;
use App\Models\Guru;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Checking Absensi Duplicates ===\n";

// Cek absensi hari ini
$today = date('Y-m-d');
echo "Checking absensi for date: $today\n\n";

$absensiToday = AbsensiGuru::whereDate('tanggal', $today)
    ->orderBy('id', 'desc')
    ->get(['id', 'guru_id', 'tanggal', 'absensi_masuk', 'absensi_pulang', 'created_at']);

echo "Total records today: " . $absensiToday->count() . "\n\n";

foreach ($absensiToday as $absensi) {
    $guru = Guru::find($absensi->guru_id);
    echo "ID: {$absensi->id} | Guru: {$guru->nama_guru} | Masuk: {$absensi->absensi_masuk} | Pulang: {$absensi->absensi_pulang} | Created: {$absensi->created_at}\n";
}

echo "\n=== Checking for Duplicates ===\n";

// Cek duplikasi berdasarkan guru_id dan tanggal
$duplicates = AbsensiGuru::whereDate('tanggal', $today)
    ->selectRaw('guru_id, tanggal, COUNT(*) as count')
    ->groupBy('guru_id', 'tanggal')
    ->having('count', '>', 1)
    ->get();

if ($duplicates->count() > 0) {
    echo "Found duplicates:\n";
    foreach ($duplicates as $duplicate) {
        $guru = Guru::find($duplicate->guru_id);
        echo "Guru: {$guru->nama_guru} | Date: {$duplicate->tanggal} | Count: {$duplicate->count}\n";
        
        // Show detail records
        $records = AbsensiGuru::where('guru_id', $duplicate->guru_id)
            ->whereDate('tanggal', $duplicate->tanggal)
            ->orderBy('id')
            ->get(['id', 'absensi_masuk', 'absensi_pulang', 'created_at']);
            
        foreach ($records as $record) {
            echo "  - ID: {$record->id} | Masuk: {$record->absensi_masuk} | Pulang: {$record->absensi_pulang} | Created: {$record->created_at}\n";
        }
        echo "\n";
    }
} else {
    echo "No duplicates found.\n";
}

echo "\n=== Recent MQTT Logs ===\n";
$logFile = 'storage/logs/laravel.log';
if (file_exists($logFile)) {
    $logs = file($logFile);
    $recentLogs = array_slice($logs, -20); // Last 20 lines
    
    foreach ($recentLogs as $log) {
        if (strpos($log, 'MQTT message received') !== false || 
            strpos($log, 'Absensi masuk otomatis berhasil') !== false ||
            strpos($log, 'Absensi pulang otomatis berhasil') !== false) {
            echo $log;
        }
    }
}
