<?php

namespace App\Observers;

use App\Models\AbsensiGuru;
use App\Services\WhatsAppService;
use Illuminate\Support\Facades\Log;

class AbsensiGuruObserver
{
    protected WhatsAppService $whatsappService;

    public function __construct(WhatsAppService $whatsappService)
    {
        $this->whatsappService = $whatsappService;
    }

    /**
     * Handle the AbsensiGuru "created" event.
     * Triggered when a teacher checks in (absen masuk)
     */
    public function created(AbsensiGuru $absensiGuru): void
    {
        // Only send notification if absensi_masuk is set (check in)
        if ($absensiGuru->absensi_masuk) {
            $this->sendNotification($absensiGuru, 'masuk');
        }
    }

    /**
     * Handle the AbsensiGuru "updated" event.
     * Triggered when a teacher checks out (absen pulang)
     */
    public function updated(AbsensiGuru $absensiGuru): void
    {
        // Check if absensi_pulang was just added (check out)
        if ($absensiGuru->wasChanged('absensi_pulang') && $absensiGuru->absensi_pulang) {
            $this->sendNotification($absensiGuru, 'pulang');
        }
    }

    /**
     * Send WhatsApp notification
     */
    protected function sendNotification(AbsensiGuru $absensiGuru, string $type): void
    {
        try {
            // Check if WhatsApp service is enabled
            if (!$this->whatsappService->isEnabled()) {
                Log::info('WhatsApp notification skipped - service disabled', [
                    'guru_id' => $absensiGuru->guru_id,
                    'type' => $type
                ]);
                return;
            }

            // Send notification
            $result = $this->whatsappService->sendAbsensiNotification($absensiGuru, $type);
            
            if ($result['success']) {
                Log::info('WhatsApp notification sent successfully', [
                    'guru_id' => $absensiGuru->guru_id,
                    'guru_name' => $absensiGuru->guru->nama_guru,
                    'type' => $type,
                    'number' => $absensiGuru->guru->no_whatsapp
                ]);
            } else {
                Log::warning('WhatsApp notification failed', [
                    'guru_id' => $absensiGuru->guru_id,
                    'guru_name' => $absensiGuru->guru->nama_guru,
                    'type' => $type,
                    'error' => $result['error']
                ]);
            }

        } catch (\Exception $e) {
            Log::error('WhatsApp notification error', [
                'guru_id' => $absensiGuru->guru_id,
                'type' => $type,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
