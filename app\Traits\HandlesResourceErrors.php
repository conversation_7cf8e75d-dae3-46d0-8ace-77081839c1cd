<?php

namespace App\Traits;

use Throwable;
use Illuminate\Database\QueryException;
use Filament\Notifications\Notification;
use App\Exceptions\DatabaseConstraintException;

trait HandlesResourceErrors
{
    /**
     * Handle database errors with user-friendly notifications
     */
    protected function handleDatabaseError(Throwable $e, string $operation = 'operasi'): bool
    {
        if ($e instanceof QueryException) {
            $friendlyException = DatabaseConstraintException::fromQueryException($e);
            
            Notification::make()
                ->title('Operasi Gagal')
                ->body($friendlyException->getMessage())
                ->danger()
                ->persistent()
                ->send();
        } else {
            Notification::make()
                ->title('<PERSON><PERSON><PERSON><PERSON>')
                ->body("Gagal melakukan {$operation}. Silakan coba lagi atau hubungi administrator.")
                ->danger()
                ->send();
        }

        // Log the error for debugging
        \Log::error("Resource {$operation} error", [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'user_id' => auth()->id(),
            'resource' => static::class,
        ]);

        return false; // Indicate operation failed
    }

    /**
     * Safely execute a database operation with error handling
     */
    protected function safeExecute(callable $operation, string $operationName = 'operasi'): bool
    {
        try {
            $result = $operation();
            return $result !== false;
        } catch (Throwable $e) {
            return $this->handleDatabaseError($e, $operationName);
        }
    }
}
