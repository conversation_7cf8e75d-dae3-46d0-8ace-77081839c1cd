"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ID3v1TagMapper = void 0;
const GenericTagMapper_1 = require("../common/GenericTagMapper");
/**
 * ID3v1 tag mappings
 */
const id3v1TagMap = {
    title: 'title',
    artist: 'artist',
    album: 'album',
    year: 'year',
    comment: 'comment',
    track: 'track',
    genre: 'genre'
};
class ID3v1TagMapper extends GenericTagMapper_1.CommonTagMapper {
    constructor() {
        super(['ID3v1'], id3v1TagMap);
    }
}
exports.ID3v1TagMapper = ID3v1TagMapper;
//# sourceMappingURL=ID3v1TagMap.js.map