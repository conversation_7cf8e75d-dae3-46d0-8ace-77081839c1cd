<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('gurus', function (Blueprint $table) {
            $table->id();
            $table->uuid('UUid');
            $table->string('nama_guru');
            $table->enum('JK', ['L', 'P']);
            $table->date('TTL');
            $table->string('NUPTK')->unique();
            $table->foreignId('Jabatan_id')->constrained('jabatans');
            $table->string('TKT');
            $table->string('Jurusan');
            $table->year('Tahun_Lulus');
            $table->date('TMT');
            $table->enum('SI_nonInduk', ['Induk', 'Non Induk']);
            $table->string('Alamat');
            $table->string('No_Hp');
            $table->foreignId('Divisi_id')->constrained('divisis');
            $table->foreignId('StatusGuru_id')->constrained('status_gurus');
            $table->string('gambar')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('gurus');
    }
};