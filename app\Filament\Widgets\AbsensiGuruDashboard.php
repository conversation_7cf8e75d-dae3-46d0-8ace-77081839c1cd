<?php

namespace App\Filament\Widgets;

use App\Models\AbsensiGuru;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Support\Carbon;

class AbsensiGuruDashboard extends BaseWidget
{
    protected static ?string $heading = 'Absensi Guru Hari Ini';
    protected static ?int $sort = 6;
    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                AbsensiGuru::query()
                    ->with(['guru.jabatan', 'guru.divisi'])
                    ->whereDate('tanggal', Carbon::today())
            )
            ->defaultSort('absensi_masuk', 'asc')
            ->columns([
                Tables\Columns\ImageColumn::make('guru.gambar')
                    ->label('Foto')
                    ->circular()
                    ->size(40)
                    ->defaultImageUrl(url('/images/default-avatar.png')),

                Tables\Columns\TextColumn::make('guru.nama_guru')
                    ->label('<PERSON>a Guru')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('guru.jabatan.nama_jabatan')
                    ->label('Jabatan')
                    ->badge()
                    ->color('primary'),

                Tables\Columns\TextColumn::make('guru.divisi.nama_divisi')
                    ->label('Divisi')
                    ->badge()
                    ->color('gray'),

                Tables\Columns\TextColumn::make('absensi_masuk')
                    ->label('Jam Masuk')
                    ->time('H:i')
                    ->badge()
                    ->color(function ($record) {
                        if (!$record->absensi_masuk) return 'gray';
                        $jamMasuk = Carbon::parse($record->absensi_masuk);
                        $batasWaktu = Carbon::parse('08:00');
                        return $jamMasuk->lte($batasWaktu) ? 'success' : 'danger';
                    }),

                Tables\Columns\TextColumn::make('absensi_pulang')
                    ->label('Jam Pulang')
                    ->time('H:i')
                    ->badge()
                    ->color(fn ($record) => $record->absensi_pulang ? 'info' : 'gray'),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Hadir' => 'success',
                        'Sakit' => 'warning',
                        'Izin' => 'info',
                        'Alfa' => 'danger',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('durasi_kerja')
                    ->label('Durasi Kerja')
                    ->getStateUsing(function ($record) {
                        if ($record->absensi_masuk && $record->absensi_pulang) {
                            $masuk = Carbon::parse($record->absensi_masuk);
                            $pulang = Carbon::parse($record->absensi_pulang);
                            $diff = $masuk->diff($pulang);
                            return $diff->format('%H:%I');
                        }
                        return '-';
                    })
                    ->badge()
                    ->color('info'),
            ])
            ->striped()
            ->paginated([10, 25, 50]);
    }
}