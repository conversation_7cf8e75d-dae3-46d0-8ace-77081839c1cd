<?php

namespace App\Filament\Widgets;

use App\Models\AbsensiGuru;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Support\Carbon;

class AbsensiGuruDashboard extends BaseWidget
{
    protected static ?int $sort = 2;
    protected int | string | array $columnSpan = 'full';
    public function table(Table $table): Table
    {
        return $table
            ->query(
                AbsensiGuru::query()
                    ->whereDate('tanggal', Carbon::today())
            )
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('guru.nama_guru'),
                Tables\Columns\TextColumn::make('tanggal'),
                Tables\Columns\TextColumn::make('absensi_masuk'),
                Tables\Columns\TextColumn::make('absensi_pulang'),
                Tables\Columns\TextColumn::make('status'),
            ]);
    }
}