<?php

namespace App\Filament\Widgets;

use App\Models\AbsensiGuru;
use Carbon\Carbon;
use Filament\Widgets\ChartWidget;

class AbsensiChart extends ChartWidget
{
    protected static ?string $heading = 'Grafik Absensi 7 Hari Terakhir';
    protected static ?int $sort = 3;
    protected int | string | array $columnSpan = 'full';

    protected function getData(): array
    {
        $data = [];
        $labels = [];
        $hadirData = [];
        $sakitData = [];
        $izinData = [];
        $alfaData = [];

        // Get data for last 7 days
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $labels[] = $date->translatedFormat('D, d M');

            $hadir = AbsensiGuru::whereDate('tanggal', $date)->where('status', 'Hadir')->count();
            $sakit = AbsensiGuru::whereDate('tanggal', $date)->where('status', 'Sakit')->count();
            $izin = AbsensiGuru::whereDate('tanggal', $date)->where('status', 'Izin')->count();
            $alfa = AbsensiGuru::whereDate('tanggal', $date)->where('status', 'Alfa')->count();

            $hadirData[] = $hadir;
            $sakitData[] = $sakit;
            $izinData[] = $izin;
            $alfaData[] = $alfa;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Hadir',
                    'data' => $hadirData,
                    'backgroundColor' => 'rgba(34, 197, 94, 0.2)',
                    'borderColor' => 'rgba(34, 197, 94, 1)',
                    'borderWidth' => 2,
                    'fill' => true,
                ],
                [
                    'label' => 'Sakit',
                    'data' => $sakitData,
                    'backgroundColor' => 'rgba(245, 158, 11, 0.2)',
                    'borderColor' => 'rgba(245, 158, 11, 1)',
                    'borderWidth' => 2,
                    'fill' => true,
                ],
                [
                    'label' => 'Izin',
                    'data' => $izinData,
                    'backgroundColor' => 'rgba(59, 130, 246, 0.2)',
                    'borderColor' => 'rgba(59, 130, 246, 1)',
                    'borderWidth' => 2,
                    'fill' => true,
                ],
                [
                    'label' => 'Alfa',
                    'data' => $alfaData,
                    'backgroundColor' => 'rgba(239, 68, 68, 0.2)',
                    'borderColor' => 'rgba(239, 68, 68, 1)',
                    'borderWidth' => 2,
                    'fill' => true,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'maintainAspectRatio' => false,
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'stepSize' => 1,
                    ],
                ],
            ],
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'mode' => 'index',
                    'intersect' => false,
                ],
            ],
            'interaction' => [
                'mode' => 'nearest',
                'axis' => 'x',
                'intersect' => false,
            ],
        ];
    }
}
