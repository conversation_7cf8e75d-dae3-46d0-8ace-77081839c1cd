<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Guru;
use App\Models\AbsensiGuru;
use App\Services\WhatsAppService;
use Carbon\Carbon;

class TestWhatsAppNotification extends Command
{
    protected $signature = 'whatsapp:test-notification {guru_id?}';
    protected $description = 'Test WhatsApp notification system';

    public function handle()
    {
        $this->info('=== Testing WhatsApp Notification System ===');
        
        // Get guru
        $guruId = $this->argument('guru_id');
        if ($guruId) {
            $guru = Guru::find($guruId);
        } else {
            $guru = Guru::whereNotNull('no_whatsapp')
                        ->where('whatsapp_notification', true)
                        ->first();
        }

        if (!$guru) {
            $this->error('No guru found with WhatsApp enabled');
            return 1;
        }

        $this->info("Testing with guru: {$guru->nama_guru} ({$guru->no_whatsapp})");

        // Test WhatsApp Service
        $whatsappService = app(WhatsAppService::class);
        
        $this->info('Checking WhatsApp service...');
        if (!$whatsappService->isEnabled()) {
            $this->error('WhatsApp service is disabled');
            return 1;
        }

        $status = $whatsappService->getStatus();
        if (!$status['connected']) {
            $this->error('WhatsApp service not connected: ' . ($status['error'] ?? 'Unknown error'));
            return 1;
        }

        $this->info('✓ WhatsApp service is connected');

        // Test direct message
        $this->info('Testing direct message...');
        $result = $whatsappService->sendMessage(
            $guru->no_whatsapp,
            "🧪 Test direct message from Laravel Console\n\nHalo {$guru->nama_guru}, ini adalah test message langsung dari console command.\n\nWaktu: " . Carbon::now()->format('H:i:s')
        );

        if ($result['success']) {
            $this->info('✓ Direct message sent successfully');
        } else {
            $this->error('✗ Direct message failed: ' . $result['error']);
        }

        // Test Observer notification
        $this->info('Testing Observer notification...');
        
        // Delete existing absensi for today
        AbsensiGuru::where('guru_id', $guru->id)
                   ->whereDate('tanggal', Carbon::today())
                   ->delete();

        // Create new absensi (should trigger Observer)
        $this->info('Creating absensi masuk...');
        $absensi = AbsensiGuru::create([
            'guru_id' => $guru->id,
            'tanggal' => Carbon::today(),
            'absensi_masuk' => Carbon::now()->format('H:i:s'),
            'status' => 'Hadir'
        ]);

        $this->info("✓ Absensi created (ID: {$absensi->id})");

        // Wait a moment
        sleep(2);

        // Update for absensi pulang
        $this->info('Updating absensi pulang...');
        $absensi->update([
            'absensi_pulang' => Carbon::now()->addHours(8)->format('H:i:s')
        ]);

        $this->info('✓ Absensi updated');

        $this->info('=== Test Complete ===');
        $this->info('Check your WhatsApp for notifications!');
        $this->info('If no notifications received, check Laravel logs for Observer activity.');

        return 0;
    }
}
