<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Absensi - Monitor Umum</title>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .clock-text {
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .status-badge {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .scroll-container {
            max-height: calc(100vh - 300px);
            overflow-y: auto;
        }
        
        .scroll-container::-webkit-scrollbar {
            width: 8px;
        }
        
        .scroll-container::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
        }
        
        .scroll-container::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
        }
        
        .scroll-container::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.5);
        }

        .trophy-gold { color: #FFD700; }
        .trophy-silver { color: #C0C0C0; }
        .trophy-bronze { color: #CD7F32; }

        .ranking-badge {
            background: linear-gradient(45deg, #667eea, #764ba2);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 10px rgba(102, 126, 234, 0.5); }
            to { box-shadow: 0 0 20px rgba(118, 75, 162, 0.8); }
        }

        .absent-card {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .top-performer-card {
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(22, 163, 74, 0.1));
            border: 1px solid rgba(34, 197, 94, 0.3);
        }
    </style>
</head>
<body class="min-h-screen">
    <div class="container mx-auto px-6 py-8">
        <!-- Header Section -->
        <div class="text-center mb-8">
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-4 clock-text">
                <i class="fas fa-school mr-4"></i>
                DASHBOARD ABSENSI
            </h1>
            
            <!-- Clock and Date -->
            <div class="bg-white/20 backdrop-blur-lg rounded-2xl p-6 mb-6 border border-white/30">
                <div class="text-center">
                    <div id="current-time" class="text-5xl md:text-7xl font-bold text-white mb-2 clock-text">
                        --:--:--
                    </div>
                    <div id="current-date" class="text-xl md:text-2xl text-white/90">
                        <span id="current-day">--</span>, <span id="current-full-date">-- -- ----</span>
                    </div>
                </div>
            </div>
            
            <!-- Stats Cards -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                <div class="bg-white/20 backdrop-blur-lg rounded-xl p-4 border border-white/30 card-hover">
                    <div class="text-center">
                        <i class="fas fa-users text-3xl text-white mb-2"></i>
                        <div id="total-guru" class="text-2xl font-bold text-white">0</div>
                        <div class="text-sm text-white/80">Total Guru</div>
                    </div>
                </div>
                
                <div class="bg-green-500/30 backdrop-blur-lg rounded-xl p-4 border border-green-400/50 card-hover">
                    <div class="text-center">
                        <i class="fas fa-check-circle text-3xl text-green-100 mb-2"></i>
                        <div id="hadir-hari-ini" class="text-2xl font-bold text-green-100">0</div>
                        <div class="text-sm text-green-100/80">Hadir Hari Ini</div>
                    </div>
                </div>
                
                <div class="bg-yellow-500/30 backdrop-blur-lg rounded-xl p-4 border border-yellow-400/50 card-hover">
                    <div class="text-center">
                        <i class="fas fa-clock text-3xl text-yellow-100 mb-2"></i>
                        <div id="belum-absen" class="text-2xl font-bold text-yellow-100">0</div>
                        <div class="text-sm text-yellow-100/80">Belum Absen</div>
                    </div>
                </div>
                
                <div class="bg-blue-500/30 backdrop-blur-lg rounded-xl p-4 border border-blue-400/50 card-hover">
                    <div class="text-center">
                        <i class="fas fa-home text-3xl text-blue-100 mb-2"></i>
                        <div id="sudah-pulang" class="text-2xl font-bold text-blue-100">0</div>
                        <div class="text-sm text-blue-100/80">Sudah Pulang</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top 5 Guru Tercepat & Guru Tidak Hadir -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Top 5 Guru Tercepat -->
            <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/30 top-performer-card">
                <h2 class="text-xl font-bold text-white mb-4 flex items-center">
                    <i class="fas fa-trophy text-yellow-400 mr-3"></i>
                    Top 5 Guru Tercepat Hari Ini
                </h2>

                <div id="top5-container" class="space-y-3">
                    <div id="top5-loading" class="text-center py-4">
                        <i class="fas fa-spinner fa-spin text-white/60"></i>
                        <p class="text-white/80 text-sm mt-2">Memuat data...</p>
                    </div>

                    <div id="top5-list" style="display: none;">
                        <!-- Data akan dimuat via JavaScript -->
                    </div>

                    <div id="top5-empty" class="text-center py-4" style="display: none;">
                        <i class="fas fa-medal text-white/60 text-2xl mb-2"></i>
                        <p class="text-white/80 text-sm">Belum ada data absensi</p>
                    </div>
                </div>
            </div>

            <!-- Guru Tidak Hadir -->
            <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/30 absent-card">
                <h2 class="text-xl font-bold text-white mb-4 flex items-center">
                    <i class="fas fa-user-times text-red-400 mr-3"></i>
                    Guru Belum Absen
                    <span id="absent-count" class="ml-auto text-sm bg-red-500/30 px-2 py-1 rounded-full">0</span>
                </h2>

                <div id="absent-container" class="scroll-container max-h-80">
                    <div id="absent-loading" class="text-center py-4">
                        <i class="fas fa-spinner fa-spin text-white/60"></i>
                        <p class="text-white/80 text-sm mt-2">Memuat data...</p>
                    </div>

                    <div id="absent-list" class="space-y-2" style="display: none;">
                        <!-- Data akan dimuat via JavaScript -->
                    </div>

                    <div id="absent-empty" class="text-center py-4" style="display: none;">
                        <i class="fas fa-check-circle text-green-400 text-2xl mb-2"></i>
                        <p class="text-white/80 text-sm">Semua guru sudah absen!</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Absensi List -->
        <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/30">
            <h2 class="text-2xl font-bold text-white mb-6 flex items-center">
                <i class="fas fa-list-alt mr-3"></i>
                Daftar Absensi Hari Ini
                <span class="ml-auto text-sm font-normal">
                    <i class="fas fa-sync-alt animate-spin" id="loading-icon" style="display: none;"></i>
                    Terakhir update: <span id="last-update">--:--:--</span>
                </span>
            </h2>
            
            <div id="absensi-container" class="scroll-container">
                <div id="loading-state" class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-3xl text-white/60 mb-4"></i>
                    <p class="text-white/80">Memuat data absensi...</p>
                </div>
                
                <div id="absensi-list" class="space-y-3" style="display: none;">
                    <!-- Data akan dimuat via JavaScript -->
                </div>
                
                <div id="empty-state" class="text-center py-8" style="display: none;">
                    <i class="fas fa-calendar-times text-3xl text-white/60 mb-4"></i>
                    <p class="text-white/80">Belum ada data absensi hari ini</p>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Configuration
        const CONFIG = {
            UPDATE_INTERVAL: 30000, // 30 seconds
            CLOCK_INTERVAL: 1000,   // 1 second
        };

        // State
        let lastUpdateTime = null;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateClock();
            loadAbsensiData();
            
            // Set intervals
            setInterval(updateClock, CONFIG.CLOCK_INTERVAL);
            setInterval(loadAbsensiData, CONFIG.UPDATE_INTERVAL);
        });

        // Update clock
        function updateClock() {
            fetch('/api/current-time')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('current-time').textContent = data.time;
                    document.getElementById('current-day').textContent = data.day;
                    document.getElementById('current-full-date').textContent = data.date;
                })
                .catch(error => {
                    console.error('Error updating clock:', error);
                });
        }

        // Load absensi data
        function loadAbsensiData() {
            const loadingIcon = document.getElementById('loading-icon');
            loadingIcon.style.display = 'inline-block';

            fetch('/api/absensi-today')
                .then(response => response.json())
                .then(data => {
                    updateStats(data.stats);
                    updateAbsensiList(data.absensi);
                    updateTop5List(data.top5_tercepat);
                    updateAbsentList(data.guru_tidak_hadir);
                    updateLastUpdateTime();
                })
                .catch(error => {
                    console.error('Error loading absensi data:', error);
                })
                .finally(() => {
                    loadingIcon.style.display = 'none';
                });
        }

        // Update statistics
        function updateStats(stats) {
            document.getElementById('total-guru').textContent = stats.total_guru;
            document.getElementById('hadir-hari-ini').textContent = stats.hadir_hari_ini;
            document.getElementById('belum-absen').textContent = stats.belum_absen;
            document.getElementById('sudah-pulang').textContent = stats.sudah_pulang;
        }

        // Update absensi list
        function updateAbsensiList(absensiData) {
            const loadingState = document.getElementById('loading-state');
            const absensiList = document.getElementById('absensi-list');
            const emptyState = document.getElementById('empty-state');
            
            loadingState.style.display = 'none';
            
            if (absensiData.length === 0) {
                absensiList.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }
            
            emptyState.style.display = 'none';
            absensiList.style.display = 'block';
            
            absensiList.innerHTML = absensiData.map(absensi => `
                <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 card-hover">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                                ${absensi.foto ? 
                                    `<img src="${absensi.foto}" alt="${absensi.nama_guru}" class="w-10 h-10 rounded-full object-cover">` :
                                    `<i class="fas fa-user text-white text-lg"></i>`
                                }
                            </div>
                            <div>
                                <h3 class="font-semibold text-white text-lg">${absensi.nama_guru}</h3>
                                <p class="text-white/70 text-sm">${absensi.jabatan} - ${absensi.divisi}</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-6">
                            <div class="text-center">
                                <div class="text-white/70 text-xs mb-1">Masuk</div>
                                <div class="text-white font-semibold">
                                    ${absensi.absensi_masuk || '--:--'}
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <div class="text-white/70 text-xs mb-1">Pulang</div>
                                <div class="text-white font-semibold">
                                    ${absensi.absensi_pulang || '--:--'}
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <span class="px-3 py-1 rounded-full text-xs font-medium status-badge ${getStatusClass(absensi.status)}">
                                    ${absensi.status}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Get status class
        function getStatusClass(status) {
            switch(status) {
                case 'Hadir': return 'bg-green-500/80 text-green-100';
                case 'Sakit': return 'bg-yellow-500/80 text-yellow-100';
                case 'Izin': return 'bg-blue-500/80 text-blue-100';
                case 'Alfa': return 'bg-red-500/80 text-red-100';
                default: return 'bg-gray-500/80 text-gray-100';
            }
        }

        // Update Top 5 list
        function updateTop5List(top5Data) {
            const loadingState = document.getElementById('top5-loading');
            const top5List = document.getElementById('top5-list');
            const emptyState = document.getElementById('top5-empty');

            loadingState.style.display = 'none';

            if (top5Data.length === 0) {
                top5List.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            emptyState.style.display = 'none';
            top5List.style.display = 'block';

            top5List.innerHTML = top5Data.map(guru => `
                <div class="flex items-center space-x-3 bg-white/10 rounded-lg p-3 card-hover">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 rounded-full flex items-center justify-center ranking-badge text-white font-bold text-sm">
                            ${guru.ranking}
                        </div>
                    </div>
                    <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0">
                        ${guru.foto ?
                            `<img src="${guru.foto}" alt="${guru.nama_guru}" class="w-8 h-8 rounded-full object-cover">` :
                            `<i class="fas fa-user text-white text-sm"></i>`
                        }
                    </div>
                    <div class="flex-1 min-w-0">
                        <h4 class="font-semibold text-white text-sm truncate">${guru.nama_guru}</h4>
                        <p class="text-white/70 text-xs truncate">${guru.jabatan}</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        ${guru.ranking === 1 ? '<i class="fas fa-trophy trophy-gold text-lg"></i>' : ''}
                        ${guru.ranking === 2 ? '<i class="fas fa-trophy trophy-silver text-lg"></i>' : ''}
                        ${guru.ranking === 3 ? '<i class="fas fa-trophy trophy-bronze text-lg"></i>' : ''}
                        <span class="text-white font-bold text-lg">${guru.absensi_masuk}</span>
                    </div>
                </div>
            `).join('');
        }

        // Update absent list
        function updateAbsentList(absentData) {
            const loadingState = document.getElementById('absent-loading');
            const absentList = document.getElementById('absent-list');
            const emptyState = document.getElementById('absent-empty');
            const absentCount = document.getElementById('absent-count');

            loadingState.style.display = 'none';
            absentCount.textContent = absentData.length;

            if (absentData.length === 0) {
                absentList.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            emptyState.style.display = 'none';
            absentList.style.display = 'block';

            absentList.innerHTML = absentData.map(guru => `
                <div class="flex items-center space-x-3 bg-white/5 rounded-lg p-2 card-hover">
                    <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0">
                        ${guru.foto ?
                            `<img src="${guru.foto}" alt="${guru.nama_guru}" class="w-6 h-6 rounded-full object-cover">` :
                            `<i class="fas fa-user text-white text-xs"></i>`
                        }
                    </div>
                    <div class="flex-1 min-w-0">
                        <h4 class="font-medium text-white text-sm truncate">${guru.nama_guru}</h4>
                        <p class="text-white/60 text-xs truncate">${guru.jabatan} - ${guru.divisi}</p>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-yellow-400 text-sm"></i>
                    </div>
                </div>
            `).join('');
        }

        // Update last update time
        function updateLastUpdateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('id-ID', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('last-update').textContent = timeString;
        }
    </script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\absensi website\laravel-absensi\resources\views/public/dashboard.blade.php ENDPATH**/ ?>