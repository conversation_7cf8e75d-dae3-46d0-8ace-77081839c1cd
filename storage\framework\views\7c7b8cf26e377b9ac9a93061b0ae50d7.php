<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Absensi - Monitor Umum</title>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .clock-text {
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .status-badge {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .scroll-container {
            max-height: calc(100vh - 300px);
            overflow-y: auto;
        }
        
        .scroll-container::-webkit-scrollbar {
            width: 8px;
        }
        
        .scroll-container::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
        }
        
        .scroll-container::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
        }
        
        .scroll-container::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.5);
        }
    </style>
</head>
<body class="min-h-screen">
    <div class="container mx-auto px-6 py-8">
        <!-- Header Section -->
        <div class="text-center mb-8">
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-4 clock-text">
                <i class="fas fa-school mr-4"></i>
                DASHBOARD ABSENSI
            </h1>
            
            <!-- Clock and Date -->
            <div class="bg-white/20 backdrop-blur-lg rounded-2xl p-6 mb-6 border border-white/30">
                <div class="text-center">
                    <div id="current-time" class="text-5xl md:text-7xl font-bold text-white mb-2 clock-text">
                        --:--:--
                    </div>
                    <div id="current-date" class="text-xl md:text-2xl text-white/90">
                        <span id="current-day">--</span>, <span id="current-full-date">-- -- ----</span>
                    </div>
                </div>
            </div>
            
            <!-- Stats Cards -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                <div class="bg-white/20 backdrop-blur-lg rounded-xl p-4 border border-white/30 card-hover">
                    <div class="text-center">
                        <i class="fas fa-users text-3xl text-white mb-2"></i>
                        <div id="total-guru" class="text-2xl font-bold text-white">0</div>
                        <div class="text-sm text-white/80">Total Guru</div>
                    </div>
                </div>
                
                <div class="bg-green-500/30 backdrop-blur-lg rounded-xl p-4 border border-green-400/50 card-hover">
                    <div class="text-center">
                        <i class="fas fa-check-circle text-3xl text-green-100 mb-2"></i>
                        <div id="hadir-hari-ini" class="text-2xl font-bold text-green-100">0</div>
                        <div class="text-sm text-green-100/80">Hadir Hari Ini</div>
                    </div>
                </div>
                
                <div class="bg-yellow-500/30 backdrop-blur-lg rounded-xl p-4 border border-yellow-400/50 card-hover">
                    <div class="text-center">
                        <i class="fas fa-clock text-3xl text-yellow-100 mb-2"></i>
                        <div id="belum-absen" class="text-2xl font-bold text-yellow-100">0</div>
                        <div class="text-sm text-yellow-100/80">Belum Absen</div>
                    </div>
                </div>
                
                <div class="bg-blue-500/30 backdrop-blur-lg rounded-xl p-4 border border-blue-400/50 card-hover">
                    <div class="text-center">
                        <i class="fas fa-home text-3xl text-blue-100 mb-2"></i>
                        <div id="sudah-pulang" class="text-2xl font-bold text-blue-100">0</div>
                        <div class="text-sm text-blue-100/80">Sudah Pulang</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Absensi List -->
        <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/30">
            <h2 class="text-2xl font-bold text-white mb-6 flex items-center">
                <i class="fas fa-list-alt mr-3"></i>
                Daftar Absensi Hari Ini
                <span class="ml-auto text-sm font-normal">
                    <i class="fas fa-sync-alt animate-spin" id="loading-icon" style="display: none;"></i>
                    Terakhir update: <span id="last-update">--:--:--</span>
                </span>
            </h2>
            
            <div id="absensi-container" class="scroll-container">
                <div id="loading-state" class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-3xl text-white/60 mb-4"></i>
                    <p class="text-white/80">Memuat data absensi...</p>
                </div>
                
                <div id="absensi-list" class="space-y-3" style="display: none;">
                    <!-- Data akan dimuat via JavaScript -->
                </div>
                
                <div id="empty-state" class="text-center py-8" style="display: none;">
                    <i class="fas fa-calendar-times text-3xl text-white/60 mb-4"></i>
                    <p class="text-white/80">Belum ada data absensi hari ini</p>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Configuration
        const CONFIG = {
            UPDATE_INTERVAL: 30000, // 30 seconds
            CLOCK_INTERVAL: 1000,   // 1 second
        };

        // State
        let lastUpdateTime = null;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateClock();
            loadAbsensiData();
            
            // Set intervals
            setInterval(updateClock, CONFIG.CLOCK_INTERVAL);
            setInterval(loadAbsensiData, CONFIG.UPDATE_INTERVAL);
        });

        // Update clock
        function updateClock() {
            fetch('/api/current-time')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('current-time').textContent = data.time;
                    document.getElementById('current-day').textContent = data.day;
                    document.getElementById('current-full-date').textContent = data.date;
                })
                .catch(error => {
                    console.error('Error updating clock:', error);
                });
        }

        // Load absensi data
        function loadAbsensiData() {
            const loadingIcon = document.getElementById('loading-icon');
            loadingIcon.style.display = 'inline-block';
            
            fetch('/api/absensi-today')
                .then(response => response.json())
                .then(data => {
                    updateStats(data.stats);
                    updateAbsensiList(data.absensi);
                    updateLastUpdateTime();
                })
                .catch(error => {
                    console.error('Error loading absensi data:', error);
                })
                .finally(() => {
                    loadingIcon.style.display = 'none';
                });
        }

        // Update statistics
        function updateStats(stats) {
            document.getElementById('total-guru').textContent = stats.total_guru;
            document.getElementById('hadir-hari-ini').textContent = stats.hadir_hari_ini;
            document.getElementById('belum-absen').textContent = stats.belum_absen;
            document.getElementById('sudah-pulang').textContent = stats.sudah_pulang;
        }

        // Update absensi list
        function updateAbsensiList(absensiData) {
            const loadingState = document.getElementById('loading-state');
            const absensiList = document.getElementById('absensi-list');
            const emptyState = document.getElementById('empty-state');
            
            loadingState.style.display = 'none';
            
            if (absensiData.length === 0) {
                absensiList.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }
            
            emptyState.style.display = 'none';
            absensiList.style.display = 'block';
            
            absensiList.innerHTML = absensiData.map(absensi => `
                <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 card-hover">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                                ${absensi.foto ? 
                                    `<img src="${absensi.foto}" alt="${absensi.nama_guru}" class="w-10 h-10 rounded-full object-cover">` :
                                    `<i class="fas fa-user text-white text-lg"></i>`
                                }
                            </div>
                            <div>
                                <h3 class="font-semibold text-white text-lg">${absensi.nama_guru}</h3>
                                <p class="text-white/70 text-sm">${absensi.jabatan} - ${absensi.divisi}</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-6">
                            <div class="text-center">
                                <div class="text-white/70 text-xs mb-1">Masuk</div>
                                <div class="text-white font-semibold">
                                    ${absensi.absensi_masuk || '--:--'}
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <div class="text-white/70 text-xs mb-1">Pulang</div>
                                <div class="text-white font-semibold">
                                    ${absensi.absensi_pulang || '--:--'}
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <span class="px-3 py-1 rounded-full text-xs font-medium status-badge ${getStatusClass(absensi.status)}">
                                    ${absensi.status}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Get status class
        function getStatusClass(status) {
            switch(status) {
                case 'Hadir': return 'bg-green-500/80 text-green-100';
                case 'Sakit': return 'bg-yellow-500/80 text-yellow-100';
                case 'Izin': return 'bg-blue-500/80 text-blue-100';
                case 'Alfa': return 'bg-red-500/80 text-red-100';
                default: return 'bg-gray-500/80 text-gray-100';
            }
        }

        // Update last update time
        function updateLastUpdateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('id-ID', { 
                hour: '2-digit', 
                minute: '2-digit', 
                second: '2-digit' 
            });
            document.getElementById('last-update').textContent = timeString;
        }
    </script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\absensi website\laravel-absensi\resources\views/public/dashboard.blade.php ENDPATH**/ ?>