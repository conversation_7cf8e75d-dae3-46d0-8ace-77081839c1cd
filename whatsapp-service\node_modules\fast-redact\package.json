{"name": "fast-redact", "version": "3.5.0", "description": "very fast object redaction", "main": "index.js", "scripts": {"test": "tap test", "posttest": "standard index.js 'lib/*.js' 'example/*.js' benchmark/index.js test/index.js | snazzy", "cov": "tap --cov test", "cov-ui": "tap --coverage-report=html test", "ci": "tap --cov --100 test", "bench": "node benchmark"}, "keywords": ["redact", "censor", "performance", "performant", "gdpr", "fast", "speed", "serialize", "stringify"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"fastbench": "^1.0.1", "pino-noir": "^2.2.1", "snazzy": "^8.0.0", "standard": "^12.0.1", "tap": "^12.5.2"}, "engines": {"node": ">=6"}, "directories": {"example": "example", "lib": "lib", "test": "test"}, "repository": {"type": "git", "url": "git+https://github.com/davidmarkclements/fast-redact.git"}, "bugs": {"url": "https://github.com/davidmarkclements/fast-redact/issues"}, "homepage": "https://github.com/davidmarkclements/fast-redact#readme"}